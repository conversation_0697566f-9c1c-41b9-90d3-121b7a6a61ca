Also, always use semicolons after the end of a syntax (for javascript, typescript)

Also, if you want to use any new icons, add it to lib/icons.tsx using the createIcon function. Don't assume existence of any icons in @reframe/icons
general syntax for createIcon is:
```
export const RefreshIcon = createIcon("Refresh", [
  ["path", { d: "M1 4v6h6", key: "1"}],
  ["path", { d: "M23 20v-6h-6", key: "2"}],
  ["path", { d: "M3 15l6-6", key: "3"}],
  ["path", { d: "M21 9l-6 6", key: "4"}],
]);

export const AlertTriangleIcon = createIcon("AlertTriangle", [
  ["path", { d: "M12 9v4m0 4h.01M10.288 3.516L2.13 17.214A2 2 0 003.728 20h16.544a2 2 0 001.598-2.786L13.71 3.516a2 2 0 00-3.422 0z", key: "1"}],
]);
```

strictly follow this format for creating icons.

DO NOT UPDATE db/types.ts, it is auto-generated by kysely after running the migrations.
Also, make sure the database fields are snake_case, not camelCase.
But after the orm, it automatically converts them to camelCase. like abc_def -> abcDef

When doing migrations: SQLite (which Turso/LibSQL is based on) doesn't support adding multiple columns in a single ALTER TABLE statement. We need to modify the migration to add each column with a separate ALTER TABLE statement.

When refactoring, make sure to remove leftover code.

DO NOT BE LAZY, UPDATE ALL THE RELEVANT PORTIONS OF THE CODE.
BUT DO NOT UNNCESSARILY UPDATE OR REWRITE CODE THAT IS OUTSIDE THE SCOPE OF THE CHANGES YOU WERE TOLD TO MAKE.

Ignore linter errors for react spring web.

Adhere to the system constraints:
<system_constraints>
  You are operating in an environment called re:frame, a react/typescript framework and compiler.

  Each re:frame app is a full-stack application with an entry point at \`@/app.tsx\`, which recursively composes \`Route\`s into a tree structure to create the full application. Here is an example,

  <file path="@/app.tsx">
    import { createApp } from "@reframe/react/app.tsx";
    import { Page } from '@/lib/page.tsx';
    import { SignInPage } from '@/lib/sign-in/page.tsx';

    export default createApp(
      Router => (
        <Router.Route
          page={() => <Page path="readme" />}
          route:auth={Router => (
            <Router.Route
              route:sign-in={Router => (
                <Router.Route
                  page={() => <SignInPage />}
                />
              )}
            />
          )}
          route:home={Router => (
            <Router.Route
              page={() => <Page path="home" />}
            />
          )}
        />
      ),
    );
  </file>

  EXTREMELY IMPORTANT: \`createApp\` is a special function and not a regular react component. It should only contain top-level routing logic composed with Router.Route components. Do not include any other components or logic inside \`createApp\`.

  You can link to other pages using the \`Link\` component. Here is an example,

  <file path="@/lib/page.tsx">
    import { Link } from "@reframe/ui/main.tsx";

    export const Page = () => (
      <div>
        <Link to="/home/<USER>">Dashboard</Link>
        <Link to="/readme">Readme</Link>
      </div>
    );
  </file>

  <file path="@/lib/client.tsx">
    'use client';

    import { useRouter } from "@reframe/react/router.tsx";
    import { deleteItem } from './action.ts';

    export const Page = () => {
      const router = useRouter();

      return (
        <div>
          <button
            onClick={() => {
              await deleteSomething(router.search.id);
              router.navigate('/home/<USER>');
            }}
          >delete</button>
        </div>
      );
    }
  </file>

  IMPORTANT: When importing packages, unless they start with \`@reframe/\` (which are from re:frame registry), they are external packages and should be installed using \`npm:\` prefix. For example, \`import { name } from "npm:package";\`.

  SUPER IMPORTANT: when importing \`react\`, use \`npm:react@canary\` instead of \`react\`. This is because re:frame uses a custom build of react.

  ULTRA IMPORTANT: ALWAYS include the full filename with extension in the import statement, like \`import { name } from "npm:package/file.ts";\` or \`import { name } from "@/lib/file.tsx";\`. IT WILL NOT WORK WITHOUT THE FILE EXTENSION.

  Remember that, re:frame is a full-stack framework that starts on the server. By default all components starts rendering on the server. If you want to use any client side features, like states or react hooks, and DOM even listeners in any component, the containing file should start with the direcive "use client". Here is an example,

  <file path="@/lib/page.tsx">
    export const Page = () => {
      return (
        <div>
          {/* non interactive elements */}
          <Counter />
        </div>
      );
    };
  </file>

  <file path="@/lib/counter.tsx">
    "use client";

    export const Counter = () => {
      const [count, setCount] = useState(0);

      return (
        <div>
          <button onClick={() => setCount(count + 1)}>Increment</button>
          <p>Count: {count}</p>
        </div>
      );
    };
  </file>

  Similarly, if you need to make any server side operations, like fetching data or making any server side calls (where you might need accessed to priviledged resources like environment secret or file system), you should start the file with the directive "use server". Here is an example,

  <file path="@/lib/action.ts">
    "use server";

    import Reframe from '@';
    import { generateText } from 'npm:ai';
    import { createOpenAI } from 'npm:@ai-sdk/openai';

    const openai = createOpenAI({ apiKey: Reframe.env.OPENAI_API_KEY })

    export const generateSummary = async (article: string) => {
      const { text } = await generateText({
        model: openai('gpt-4o-mini'),
        system:
          'You are a professional writer. ' +
          'You write simple, clear, and concise content.',
        prompt: \`Summarize the following article in 3-5 sentences: \${article}\`,
      });

      return text;
    };
  </file>

  <file path="@/lib/page.tsx">
    import { generateSummary } from './action.ts';

    export const Page = () => {
      const [article, setArticle] = useState('');
      const [summary, setSummary] = useState('');

      return (
        <div>
          <input value={article} onChange={e => setArticle(e.target.value)} />
          <button onClick={async () => setSummary(await generateSummary(article))}>
            Generate Summary
          </button>
        </div>
      );
    };
  </file>

  IMPORTANT: The app includes a version check system that forces users to refresh when a new version is deployed. This is implemented through:
  
  1. A server endpoint at /api/version that returns the current version and timestamp
  2. Client-side checks on tab focus and periodic intervals
  3. A refresh overlay component that appears when a version mismatch is detected

  Here is an example of the version check implementation:

  <file path="@/lib/refresh-overlay.tsx">
    "use client";

    export const RefreshOverlay = () => (
      <div className="fixed inset-0 bg-black/90 z-[9999] flex items-center justify-center">
        <div className="text-center">
          <Text>New Version Available</Text>
          <Button onClick={() => window.location.reload()}>
            Refresh Now
          </Button>
        </div>
      </div>
    );
  </file>
</system_constraints>