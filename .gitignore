.build
**/.build

node_modules

.cache
**/.cache

.run
**/.run

hub/*.ts

commit-*.json

.env
**/.env

.env.*
**/.env.*

.venv
**/.venv

*.md

deno.lock
*.db
*.db-shm
*.db-wal

.cursor

ignore
scripts/

planning/
sajidthings
hub/@ethan/awaken/lib/payment-overlay backup.tsx
cmds.txt
deploy.py
deploy.sh
lister.py
crater.py
sync.log
deploy2.py
remote_copy/
deploy3.py
todoPlan.txt
.DS_Store
.cursorrules
tunnel.log
wireguard.conf
.github/workflows/fly.yml
fly-reset.ts
fly.toml
Dockerfile
coach_card_offerings.json
import-coach-cards.ts
migrate-profile-text.ts
drizzle-test.config.ts
drizzle.config.ts
package-lock.json
package.json
dump copy 2.sql
dump copy.sql
dump.sql
drizzle-local.config.ts