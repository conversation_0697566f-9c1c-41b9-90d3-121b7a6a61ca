import { fetchKnowledgeAction } from "./fetch-knowledge-actions.ts";

export interface ToolUseBlock {
  id: string;
  name: string;
  input: any;
}

export async function execTool(block: ToolUseBlock) {
  try {
    switch (block.name) {
      case "fetch_knowledge":
        return await fetchKnowledgeAction(block.input);
      default:
        return { error: `unknown tool ${block.name}` };
    }
  } catch (err) {
    return { error: (err as Error).message };
  }
}