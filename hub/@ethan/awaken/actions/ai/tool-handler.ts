"use server";

import Reframe from "@";
import { getControlUrlForCall } from './call-actions.ts'; // Adjust path if needed
import { generateGuidedSessionPlanAction } from './session-plan-actions.ts';
import { fetchKnowledgeAction } from "./fetch-knowledge-actions.ts";

// Define expected VAPI tool call request structure (Matching logged payload)
interface VapiToolCallPayload {
  message: {
    type: "tool-calls";
    toolCallList?: { // Make optional just in case it's sometimes missing despite logs
      id: string;
      type: string;
      function?: { // Function object might be optional
        name: string;
        arguments: Record<string, any>; // Arguments are an object
      };
    }[];
    // Include other potential fields like toolCalls, toolWithToolCallList if needed for typing, but we'll ignore them
    call?: {
      id?: string;
      metadata?: Record<string, any>;
    };
    timestamp?: number;
  };
}

// Define the response structure VAPI expects
interface VapiToolResponsePayload {
  results: {
    toolCallId: string; // Must match the ID from the request
    result: any; // The result of the tool execution
  }[];
}

/**
 * Handles POST requests from VAPI when a custom tool is called.
 */
export async function handleVapiToolCall(request: Request): Promise<Response> {
  console.log("[VAPI TOOL HANDLER] Received tool call request.");

  // 1. Verify the Shared Secret
  const authHeader = request.headers.get("Authorization");
  const receivedSecret = authHeader?.startsWith("Bearer ")
    ? authHeader.substring(7)
    : null;

  if (receivedSecret !== Reframe.env.VAPI_TOOL_SECRET) {
    console.warn("[VAPI TOOL HANDLER] Unauthorized access attempt.");
    return new Response("Unauthorized", { status: 401 });
  }
  console.log("[VAPI TOOL HANDLER] Authorization successful.");

  // 2. Parse the Request Body
  let payload: VapiToolCallPayload;
  try {
    payload = await request.json();
    // Log the entire received payload for debugging
    console.log("[VAPI TOOL HANDLER] Full Parsed Payload:", JSON.stringify(payload, null, 2));
  } catch (error) {
    console.error("[VAPI TOOL HANDLER] Failed to parse JSON request body:", error);
    return new Response("Bad Request: Invalid JSON", { status: 400 });
  }

  // 3. Process Tool Calls
  const results: VapiToolResponsePayload["results"] = [];
  // Use message.toolCallList
  const toolCalls = payload.message?.toolCallList;

  if (!Array.isArray(toolCalls)) {
    console.error("[VAPI TOOL HANDLER] Invalid payload: 'message.toolCallList' is missing or not an array.");
    return new Response("Bad Request: Invalid payload structure", { status: 400 });
  }

  for (const toolCall of toolCalls) {
    const toolName = toolCall.function?.name;
    const callId = payload.message?.call?.id;
    const metadata = payload.message?.call?.metadata;
    console.log(`[VAPI TOOL HANDLER] Processing tool: ${toolName} (Call ID: ${callId}, Tool ID: ${toolCall.id})`);
    console.log(`[VAPI TOOL HANDLER] Metadata:`, metadata);
    let toolResult: any;
    let controlUrl: string | null = null;

    // Fetch controlUrl if callId exists
    if (callId) {
      try {
        controlUrl = await getControlUrlForCall(callId);
        if (!controlUrl) {
          console.warn(`[VAPI TOOL HANDLER] Could not find controlUrl for callId: ${callId}`);
        } else {
          console.log(`[VAPI TOOL HANDLER] Retrieved controlUrl: ${controlUrl}`);
        }
      } catch (dbError) {
        console.error(`[VAPI TOOL HANDLER] Database error fetching controlUrl for callId ${callId}:`, dbError);
      }
    } else {
      console.warn("[VAPI TOOL HANDLER] No callId found in the payload. Check 'message.call.id'.");
    }

    try {
      const args = toolCall.function?.arguments;

      switch (toolName) {
        case "create_guided_session":
          console.log("[VAPI TOOL HANDLER] Handling 'create_guided_session'");

          const description = args?.description as string | undefined;
          const channelId = metadata?.channelId as string | undefined;
          const coachName = metadata?.coachName as string | undefined;

          console.log("[VAPI TOOL HANDLER] Extracted Context:", { description, channelId, coachName });

          if (description && channelId && coachName) {
            console.log("[VAPI TOOL HANDLER] Triggering background session plan generation...");
            generateGuidedSessionPlanAction(channelId, description, coachName)
              .catch(triggerError => {
                console.error(`[VAPI TOOL HANDLER] Error triggering generateGuidedSessionPlanAction for channel ${channelId}:`, triggerError);
              });

            toolResult = "Okay, I'm creating that session plan for you now. It should appear on your home screen shortly.";

            if (controlUrl) {
              try {
                console.log(`[VAPI TOOL HANDLER] Sending confirmation via controlUrl: ${controlUrl}`);
                const response = await fetch(controlUrl, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    type: 'say',
                    content: toolResult
                  })
                });
                if (!response.ok) {
                  const errorText = await response.text();
                  console.error(`[VAPI TOOL HANDLER] Error sending message via controlUrl: ${response.status} ${response.statusText}`, errorText);
                } else {
                  console.log("[VAPI TOOL HANDLER] Successfully sent confirmation message via controlUrl.");
                }
              } catch (fetchError) {
                console.error("[VAPI TOOL HANDLER] Fetch error sending message via controlUrl:", fetchError);
              }
            } else {
              console.warn("[VAPI TOOL HANDLER] controlUrl unavailable, cannot send spoken confirmation message.");
            }
          } else {
            console.warn("[VAPI TOOL HANDLER] Missing required context for 'create_guided_session'. Need description, channelId, and coachName.");
            toolResult = "Sorry, I couldn't create the session. I'm missing some information (description, channelId, or coachName).";
          }
          break;

        /* ------------------------------------------------------------------ *
         * fetch_knowledge – synchronous lookup via Gemini-2.5 Flash
         * ------------------------------------------------------------------ */
        case "fetch_knowledge":
          console.log("[VAPI TOOL] fetch_knowledge invoked");

          // The args for this tool are defined by the Vapi function schema
          //   { query, client_state, needs, max_items }
          try {
            const { query, client_state, needs, max_items } = args ?? {};
            console.log("[VAPI TOOL] fetch_knowledge args:", { query, client_state, needs, max_items });

            if (!query) {
              console.warn("[VAPI TOOL] fetch_knowledge missing required 'query' parameter.");
              toolResult = {
                error: "Missing required parameter: query",
              };
              break;
            }

            const result = await fetchKnowledgeAction({
              query,
              kb_identifier: "jp-articles-v1", // Default for testing
              client_state,
              needs,
              max_items,
              // coachName: "JP Morgan", // Removed - kb_identifier specifies the KB
            });
            console.log("[VAPI TOOL] fetch_knowledge result:", JSON.stringify(result, null, 2));

            toolResult = result; // already JSON-serialisable
          } catch (fkErr) {
            console.error("[VAPI TOOL] fetch_knowledge error:", fkErr);
            toolResult = {
              error: fkErr instanceof Error ? fkErr.message : "unknown error",
            };
          }
          break;

        default:
          console.warn(`[VAPI TOOL HANDLER] Received unknown tool name: ${toolName}`);
          toolResult = `Error: Unknown tool '${toolName || "undefined"}' requested.`;
      }

      results.push({ toolCallId: toolCall.id, result: toolResult });

    } catch (error) {
      console.error(`[VAPI TOOL HANDLER] Error executing tool '${toolName}' (ID: ${toolCall.id}):`, error);
      results.push({
        toolCallId: toolCall.id,
        result: `Error processing tool ${toolName}: ${error instanceof Error ? error.message : "Unknown error"}`,
      });
    }
  }

  // 4. Send Formatted Response Back to VAPI
  const responsePayload: VapiToolResponsePayload = { results };
  console.log("[VAPI TOOL HANDLER] Sending response:", JSON.stringify(responsePayload));

  return Response.json(responsePayload, { status: 200 });
}