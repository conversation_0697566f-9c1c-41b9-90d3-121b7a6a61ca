"use server";

// Import necessary DB actions
import { initiateUserCardCreationAction, completeUserCardCreationAction } from "../db/coach-actions.ts";
import { getProfileText, decryptProfile, decryptMessages, getCoachPrompt } from "../../action.ts";
import { geminiRequest, openRouterRequest } from "../../lib/helper.ts";
import { verifyUserPermission } from "../../lib/auth-helper.ts";

export interface SystemPromptResponse {
  SystemPrompt: string;
}
/**
 * Generate a guided session plan for a specific coaching session.
 * This now handles the full lifecycle including creating a placeholder card,
 * generating the plan in the background, and updating the card upon completion.
 * 
 * @param channelIdStr - The user's channel ID (as string).
 * @param userGoal - The user's goal for the session.
 * @param coachName - The coach name.
 * @returns Promise<void> - The function now performs side effects (DB updates) and doesn't return the plan directly.
 */
export async function generateGuidedSessionPlanAction(
  channelIdStr: string,
  userGoal: string,
  coachName: string
): Promise<{ success: boolean; userCardId?: string | null; error?: string }> { // Return type changed
  console.log("[GUIDED_PLAN_ACTION] Generating session plan:", { channelIdStr, userGoal, coachName });
  if(!(await verifyUserPermission(channelIdStr))) return { success: false, error: "Action permission check failed" };

  // Convert channelIdStr to number early
  const channelId = parseInt(channelIdStr, 10);
  if (isNaN(channelId)) {
    console.error("[GUIDED_PLAN_ACTION] Invalid channelId provided:", channelIdStr);
    // return; // Exit if channelId is not a valid number
    return { success: false, error: "Invalid channelId provided" }; // Return failure
  }

  let newUserCardId: string | null = null; // To store the ID of the placeholder card

  try {
    // --- Step 1: Initiate Card Creation ---
    console.log("[GUIDED_PLAN_ACTION] Initiating user card creation...");
    const initiationResult = await initiateUserCardCreationAction(channelId, coachName, 'GUIDED_SESSION');

    if (!initiationResult.success) {
      console.error("[GUIDED_PLAN_ACTION] Failed to initiate user card creation:", initiationResult.error);
      // If initiation fails, we can't proceed.
      // return;
      return { success: false, error: initiationResult.error || "Failed to initiate user card creation" }; // Return failure
    }
    newUserCardId = initiationResult.userCardId;
    console.log(`[GUIDED_PLAN_ACTION] Placeholder card created with ID: ${newUserCardId}`);

    // --- Step 2: Gather Data for LLM (Profile, History) ---
    console.log("[GUIDED_PLAN_ACTION] Fetching profile and message history...");
    const [profileData, messagesData] = await Promise.all([
      getProfileText(channelIdStr), // Use original string for profile fetch if needed
      decryptMessages(channelIdStr, true, coachName, 15)
    ]);

    const { profileText } = profileData;
    console.log("[GUIDED_PLAN_ACTION] Profile and messages fetched and decrypted.");

    // --- Step 3: Fetch Prompt & Call Gemini ---
    console.log("[GUIDED_PLAN_ACTION] Fetching interventionSamplerPrompt...");
    let interventionSamplerSystemPrompt = `Analyze the client profile, goal, and history. Output potential interventions or key focus areas within <interventions>...</interventions> tags.`; // Default fallback
    try {
      const samplerPromptResponse = await getCoachPrompt(coachName, "interventionSamplerPrompt") as SystemPromptResponse;
      if (samplerPromptResponse && samplerPromptResponse.SystemPrompt) {
        interventionSamplerSystemPrompt = samplerPromptResponse.SystemPrompt;
        console.log("[GUIDED_PLAN_ACTION] interventionSamplerPrompt fetched successfully.");
      } else {
        console.log("[GUIDED_PLAN_ACTION] interventionSamplerPrompt not found or empty, using default.");
      }
    } catch (error) {
      console.error("[GUIDED_PLAN_ACTION] Error fetching interventionSamplerPrompt, using default:", error);
    }

    console.log("[GUIDED_PLAN_ACTION] Calling Gemini for interventions...");
    const geminiPayload = {
      system_instruction: {
        parts: { text: interventionSamplerSystemPrompt },
      },
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `<CLIENT_PROFILE>
${profileText || "No profile available"}
</CLIENT_PROFILE>
<SESSION_GOAL>${userGoal}</SESSION_GOAL>
<RECENT_CONVERSATION_HISTORY>
</RECENT_CONVERSATION_HISTORY>`,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 1.0, // Adjust as needed
        maxOutputTokens: 6000, // Adjust as needed
      },
      safetySettings: [
        { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
        { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
        { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
        { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" },
      ],
    };

    // Log the payload before sending to Gemini
    console.log("[GUIDED_PLAN_ACTION] Gemini Request Payload:", JSON.stringify(geminiPayload, null, 2));

    const geminiResponse = await geminiRequest([geminiPayload]);
    const geminiData = await geminiResponse.json();

    console.log("[GUIDED_PLAN_ACTION] Gemini Response Data:", JSON.stringify(geminiData, null, 2));

    if (!geminiData?.text1) { // Check for existence of text1
      // Throw error to be caught by outer catch block
      throw new Error("Gemini call for interventions failed or returned empty response.");
    }

    // Extract interventions (use full text as fallback if tags missing)
    let interventions = geminiData.text1;
    const interventionMatch = geminiData.text1.match(/<interventions>([\s\S]*?)<\/interventions>/i);
    if (interventionMatch && interventionMatch[1]) {
      interventions = interventionMatch[1].trim();
      console.log("[GUIDED_PLAN_ACTION] Extracted interventions from Gemini tags.");
    } else {
      console.log("[GUIDED_PLAN_ACTION] <interventions> tags not found in Gemini response, using full response.");
    }

    // --- Step 4: Fetch Session Planner Prompt ---
    console.log("[GUIDED_PLAN_ACTION] Fetching sessionPlannerPrompt");
    let sessionPlannerSystemPrompt = `You are an expert coach designing a detailed, personalized guided session plan. 
The plan should be structured and easy to follow.
Based on the user's profile, goal, and recent history, create the session plan.

**IMPORTANT OUTPUT FORMATTING:**
Your *entire* response MUST include the following XML-style tags exactly:
1.  A concise title for the session within <title>...</title> tags.
2.  A brief description suitable for a card UI within <description>...</description> tags.
3.  The full, detailed session plan content within <sessionPlan>...</sessionPlan> tags.

Example:
<title>Finding Calm Amidst Chaos</title>
<description>A guided session to practice mindfulness and reduce stress.</description>
<session_plan>
## Session Plan: Finding Calm

**Objective:** ...

**Section 1: Introduction (5 mins)**
- Welcome and check-in...

**Section 2: Mindfulness Practice (15 mins)**
- Guided breathing exercise...

**Section 3: Reflection (10 mins)**
- Journal prompts...

**Section 4: Closing (5 mins)**
- Summary and next steps...
</session_plan>`; // Enhanced default with tag instructions

    try {
      const plannerPromptResponse = await getCoachPrompt(coachName, "sessionPlannerPrompt") as SystemPromptResponse;
      if (plannerPromptResponse && plannerPromptResponse.SystemPrompt) {
        sessionPlannerSystemPrompt = plannerPromptResponse.SystemPrompt;
        console.log("[GUIDED_PLAN_ACTION] sessionPlannerPrompt fetched and formatting instructions appended.");
      } else {
        console.log("[GUIDED_PLAN_ACTION] sessionPlannerPrompt not found, using default with formatting instructions.");
      }
    } catch (error) {
      console.error("[GUIDED_PLAN_ACTION] Error fetching sessionPlannerPrompt, using default:", error);
      // Proceed with the default prompt defined above
    }

    // --- Step 5: Prepare User Prompt for Final LLM ---
    // *** Include extracted interventions in <YOUR_NOTES> ***
    const userPromptForFinalLLM = `<CLIENT_PROFILE>
${profileText || "No profile available"}
</CLIENT_PROFILE>
<SESSION_GOAL>${userGoal}</SESSION_GOAL>
<RECENT_CONVERSATION_HISTORY>
</RECENT_CONVERSATION_HISTORY>
<YOUR_NOTES>
${interventions} </YOUR_NOTES>`;

    console.log("[GUIDED_PLAN_ACTION] User prompt for final LLM:", userPromptForFinalLLM.substring(0, 200) + "...");
    console.log("[GUIDED_PLAN_ACTION] System prompt for final LLM:", sessionPlannerSystemPrompt.substring(0, 300) + "...");

    // --- Step 6: Call Final LLM (e.g., OpenRouter/o1) ---
    console.log("[GUIDED_PLAN_ACTION] Calling final LLM (e.g., OpenRouter/o1) for session plan...");
    
    const llmResponseText = await openRouterRequest(
      "google/gemini-2.5-pro", // Or preferred model
      sessionPlannerSystemPrompt,
      userPromptForFinalLLM
    );

    if (!llmResponseText) {
      // Throw error to be caught by outer catch block, which will handle card status
      throw new Error("Final LLM call returned empty response.");
    }

    console.log("[GUIDED_PLAN_ACTION] Received response from final LLM.");
    // console.log("[DEBUG] LLM Response:", llmResponseText); // Optional: Log full response for debugging

    // --- Step 7: Parse LLM Response for Tags ---
    console.log("[GUIDED_PLAN_ACTION] Parsing LLM response for title, description, and sessionPlan...");
    let generatedTitle: string | null = "Guided Session"; // Default fallback title
    let generatedDescription: string | null = "A personalized session plan."; // Default fallback description
    let planContent: string = llmResponseText; // Default fallback plan content

    const titleMatch = llmResponseText.match(/<title>([\s\S]*?)<\/title>/i);
    if (titleMatch && titleMatch[1]) {
      generatedTitle = titleMatch[1].trim();
      console.log("[GUIDED_PLAN_ACTION] Extracted Title:", generatedTitle);
    } else {
      console.warn("[GUIDED_PLAN_ACTION] <title> tags not found in LLM response. Using default.");
    }

    const descriptionMatch = llmResponseText.match(/<description>([\s\S]*?)<\/description>/i);
    if (descriptionMatch && descriptionMatch[1]) {
      generatedDescription = descriptionMatch[1].trim();
      console.log("[GUIDED_PLAN_ACTION] Extracted Description:", generatedDescription);
    } else {
      console.warn("[GUIDED_PLAN_ACTION] <description> tags not found in LLM response. Using default.");
    }

    const planMatch = llmResponseText.match(/<session_plan>([\s\S]*?)<\/session_plan>/i);
    if (planMatch && planMatch[1]) {
      planContent = planMatch[1].trim();
      console.log("[GUIDED_PLAN_ACTION] Extracted Session Plan content.");
    } else {
      console.warn("[GUIDED_PLAN_ACTION] <session_plan> tags not found in LLM response. Using full response as plan.");
      // Keep planContent as the full llmResponseText (already assigned)
    }

    // --- Step 8: Complete Card Creation ---
    console.log(`[GUIDED_PLAN_ACTION] Completing user card creation for ID: ${newUserCardId}`);
    const completionResult = await completeUserCardCreationAction(
      newUserCardId,
      { sessionPlan: planContent }, // Instance data containing the plan
      generatedTitle,
      generatedDescription
    );

    if (!completionResult.success) {
      // Throw error to be caught by outer catch block
      throw new Error(`Failed to complete user card creation: ${completionResult.error}`);
    }

    console.log(`[GUIDED_PLAN_ACTION] Successfully generated and stored session plan for card ID: ${newUserCardId}`);
    // Function completes successfully (void return)
    // Return success *of initiation* here. The actual plan generation continues in background.
    return { success: true, userCardId: newUserCardId };

  } catch (error) {
    console.error("[GUIDED_PLAN_ACTION] Error during session plan generation process:", error);

    // --- Error Handling: Update Card Status (Optional) ---
    // If an error occurred *after* the card was initiated, we might want to mark it as failed.
    if (newUserCardId) {
      console.error(`[GUIDED_PLAN_ACTION] An error occurred after card ${newUserCardId} was initiated. Card status remains 'GENERATING' unless explicitly updated.`);
      // TODO (Optional): Implement and call an action like:
      // await updateUserCardStatusAction(newUserCardId, 'FAILED'); 
      // This would require adding the action to coach-actions.ts and importing it.
      // For now, we just log the error. The card will stay in 'GENERATING' state.
      // Return failure if an error occurred *during the background process*
      // Note: This return is inside the catch, so it signifies an error *after* successful initiation potentially.
      // The frontend already received the success:true from the initiation step.
      // We might need a different mechanism (like polling or websockets) to notify the frontend of this *later* failure.
      // For now, just log it server-side. The success:true returned earlier only indicates initiation success.
    }
    // If the catch block is reached, it means an error occurred.
    // If initiation failed, we already returned { success: false }.
    // If initiation succeeded but background failed, we already returned { success: true }.
    // We must return *something* to satisfy the Promise type, even if the client already got the initiation status.
    // Return a failure state, indicating the overall process encountered an error, even if initiation looked successful initially.
    return { 
      success: false, 
      userCardId: newUserCardId, // Include ID if available, helps identify which card failed
      error: (error as Error).message || "Unknown error during background generation" 
    };
  }
}
