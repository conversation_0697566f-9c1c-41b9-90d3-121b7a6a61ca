/* actions/ai/fetch-knowledge-actions.ts
   --------------------------------------------------------------------------- *
   A synchronous helper for the Vapi "fetch_knowledge" tool.
   It bundles a (temporary) hard-coded knowledge-base, composes the system
   prompt, calls Gemini-2.5-Flash, then returns the JSON produced by the model.
* --------------------------------------------------------------------------- */
"use server";

import { geminiRequest } from "../../lib/helper.ts";
import { db } from "../../lib/db.ts"; // Import the database client

/** ---------------------------------------------------------
 *  Public helper – Vapi (or any server code) can call this.
 *  ---------------------------------------------------------
 */
export interface FetchKnowledgeArgs {
  query: string;
  kb_identifier: string; // Added: Identifier for the knowledge base
  client_state?: string;
  needs?: string[];
  max_items?: number;
  /*  Kept but ignored for now – we'll always use JP Morgan until
      the KB is made coach-specific. */
  coachName?: string;
}

export async function fetchKnowledgeAction({
  query,
  kb_identifier, // Added
  client_state = "",
  needs = [],
  max_items = 3,
}: FetchKnowledgeArgs) {

  /* ------------------------------------------------------------------ *
   * 1. Fetch Knowledge Base Content from DB
   * ------------------------------------------------------------------ */
  let knowledgeBaseContent = "";
  const dbStartTime = performance.now();
  try {
    console.log(`[FETCH KNOWLEDGE] Querying knowledge_bases for identifier: ${kb_identifier}`);
    const kbRecord = await db
      .selectFrom("knowledge_bases")
      .select(["content"])
      .where("identifier", "=", kb_identifier)
      .executeTakeFirst();

    if (!kbRecord || !kbRecord.content) {
      console.error(`[FETCH KNOWLEDGE] Knowledge base not found or empty for identifier: ${kb_identifier}`);
      // Return a structured error or empty response
      return { items: [], summary: "Error: Knowledge base not found." };
    }
    knowledgeBaseContent = kbRecord.content;
    const dbEndTime = performance.now();
    console.log(`[FETCH KNOWLEDGE] DB query took ${(dbEndTime - dbStartTime).toFixed(2)}ms`);

  } catch (dbError) {
    const dbEndTime = performance.now();
    console.error(`[FETCH KNOWLEDGE] Database error fetching KB '${kb_identifier}':`, dbError);
    console.error(`[FETCH KNOWLEDGE] DB query failed after ${(dbEndTime - dbStartTime).toFixed(2)}ms`);
    // Return a structured error
    return { items: [], summary: "Error: Failed to retrieve knowledge base." };
  }

  /* ------------------------------------------------------------------ *
   * 2.  Compose system-prompt template
   * ------------------------------------------------------------------ */
  const SYSTEM_PROMPT = `
You are a powerful AI transformational coach, master of all forms of coaching, and enlightened being.
You are playing the role of a knowledge tool on behalf of another powerful AI coach.

TASK:
1. Read the KNOWLEDGE_BASE below (may be very large).
2. Select up to ${max_items} blocks whose
     • tags OR content best match:
         – query: "${query}"
         – client_state: "${client_state}"
         – needs types: ${JSON.stringify(needs)}  (array; may be empty)
3. For each chosen block, return a JSON object:
   { "type": "...", "content": "...", "source": "...", "relevance": <0-1>,
     "use_hint": "..." }
   • "source" = TITLE without the leading '# TITLE:'.
   • "content" = block text (excerpted if necessary for length)
   • "use_hint" = one sentence suggestion on how a coach
       might weave it into conversation.
4. Wrap all objects inside
   { "items": [ ... ], "summary": "..." }
   • "summary" = ≤ 25-word thematic takeaway combining the items.
5. If no suitable item exists, return
   { "items": [], "summary":"none" }.

Return ≤ 600 words total.

### KNOWLEDGE_BASE
${knowledgeBaseContent}
`.trim();
  /* ------------------------------------------------------------------ *
   * 3.  Build Gemini payload   (Gemini 2.5 Flash)
   * ------------------------------------------------------------------ */
  const payload = {
    system_instruction: { parts: { text: SYSTEM_PROMPT } },
    contents: [
      {
        role: "user",
        parts: [{ text: "RUN" }], // trivial "user" part – model just needs a turn
      },
    ],
    generationConfig: { temperature: 1.0, thinkingConfig: { thinkingBudget: 300 } },
    safetySettings: [
      { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
      { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" },
    ],
  };
  /* ------------------------------------------------------------------ *
   * 4.  Call Gemini and return parsed JSON
   * ------------------------------------------------------------------ */
  console.log("[FETCH KNOWLEDGE] Calling Gemini API...");
  const response = await geminiRequest([payload], "gemini-2.5-flash");
  const data = await response.json();

  // Gemini wrapper → data.text1 is the raw string from the model
  const raw = data.text1?.trim() || "{}";
  console.log("[FETCH KNOWLEDGE] Raw model output:", raw);

  // The model is instructed to output pure JSON; try to parse but fall back
  // to raw string for maximum robustness.
  try {
    // Try direct parse first
    const parsedResult = JSON.parse(raw);
    console.log("[FETCH KNOWLEDGE] Parsed result:", JSON.stringify(parsedResult));
    return parsedResult;
  } catch (e) {
    // Extract from markdown code blocks if present
    const jsonMatch = raw.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      try {
        const parsedResult = JSON.parse(jsonMatch[1]);
        console.log("[FETCH KNOWLEDGE] Parsed result from markdown:", JSON.stringify(parsedResult));
        return parsedResult;
      } catch (innerError) {
        console.error("[FETCH KNOWLEDGE] Error parsing extracted JSON:", innerError);
      }
    }
    console.error("[FETCH KNOWLEDGE] Error parsing JSON:", e, "Returning raw string.");
    return raw; // let the caller decide what to do
  }
} 