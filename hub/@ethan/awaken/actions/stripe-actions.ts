"use server";

import Reframe from "@";
import stripe from "../lib/stripe-lib.ts";
import { db } from "../lib/db.ts";


// Helper function to get Stripe price ID for plan
function getPriceIdForPlan(planId: string): string {
  const priceMap = {
    'basic_plan_monthly': Reframe.env.STRIPE_BASIC_MONTHLY_PRICE_ID,
    'basic_plan_yearly': Reframe.env.STRIPE_BASIC_YEARLY_PRICE_ID,
    'premium_plan_monthly': Reframe.env.STRIPE_PREMIUM_MONTHLY_PRICE_ID,
    'premium_plan_yearly': Reframe.env.STRIPE_PREMIUM_YEARLY_PRICE_ID
  };
  const priceId = priceMap[planId];
  if (!priceId) {
    throw new Error(`Invalid plan ID: ${planId}`);
  }
  return priceId;
}


export const createTopUpSession = async (
  channelId: string,
  amount: number,
  essenceAmount: number,
  topUpId: string
) => {
  try {
    // Get user data
    const user = await db
      .selectFrom("user")
      .select(["email", "stripeCustomerId"])
      .where("channelId", "=", Number(channelId))
      .executeTakeFirst();

    if (!user) {
      throw new Error("User not found");
    }

    // Create checkout session with price_data
    const session = await stripe.checkout.sessions.create({
      customer: user.stripeCustomerId,
      payment_method_types: ["card"],
      line_items: [{
        price_data: {
          currency: "usd",
          product_data: {
            name: `${essenceAmount} Essence Top-up`,
            description: `Add ${essenceAmount} essence to your Awaken account`
          },
          unit_amount: amount * 100 // amount in cents
        },
        quantity: 1
      }],
      mode: "payment",
      success_url: `${Reframe.env.APP_URL}/chat?top_up_success=true&top_up_id=${topUpId}`,
      cancel_url: `${Reframe.env.APP_URL}/chat`,
      metadata: {
        channelId,
        essenceAmount: essenceAmount.toString(),
        type: "top_up",
        topUpId
      },
      payment_intent_data: {
        description: `Top up (${essenceAmount} essence)`
      }
    });

    return session.url;
  } catch (error) {
    console.error("Error creating top-up session:", error);
    throw error;
  }
}; 

export const createCheckoutSession = async (userId: string, planId: string, email: string) => {
  try {
    console.log("[CHECKOUT] Creating session for:", { userId, planId, email });

    // Get or create Stripe customer
    let customer;
    const user = await db
      .selectFrom("user")
      .select(["stripeCustomerId"])
      .where("channel_id", "=", Number(userId))
      .executeTakeFirst();

    if (user?.stripeCustomerId) {
      customer = { id: user.stripeCustomerId };
    } else {
      // Create new customer in Stripe
      try {
        customer = await stripe.customers.create(email, { userId });
  
        // Update user with Stripe customer ID
        await db
          .updateTable("user")
          .set({ stripeCustomerId: customer.id })
          .where("channel_id", "=", Number(userId))
          .execute();
      } catch (error) {
        console.error("[CHECKOUT] Error creating customer:", error);
        throw error;
      }
    }

    // Get price ID based on plan
    const priceId = getPriceIdForPlan(planId);
    
    // Determine if this is a basic or premium plan
    const isPremiumPlan = planId.startsWith('premium_');
    const isBasicPlan = planId.startsWith('basic_');
    
    // Select the appropriate coupon ID based on plan type
    let couponId = null;
    if (isPremiumPlan) {
      couponId = Reframe.env.STRIPE_PREMIUM_DISCOUNT_COUPON_ID;
    } else if (isBasicPlan) {
      couponId = Reframe.env.STRIPE_BASIC_DISCOUNT_COUPON_ID;
    }

    // Create checkout session options
    const sessionOptions = {
      customer: customer.id,
      payment_method_types: ["card"],
      line_items: [{ price: priceId, quantity: 1 }],
      mode: "subscription",
      success_url: `${Reframe.env.APP_URL}/chat?subscription_success=true&plan=${planId.split('_')[0]}`,
      cancel_url: `${Reframe.env.APP_URL}/chat`,
      metadata: {
        userId,
        planId
      },
    };
    
    // Only add coupon if one is applicable
    if (couponId) {
      sessionOptions.discounts = [
        {
          coupon: couponId
        }
      ];
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create(sessionOptions);

    console.log("[CHECKOUT] Session created:", session.id);
    return session;

  } catch (error) {
    console.error("[CHECKOUT] Error:", error);
    throw error;
  }
};

export const cancelSubscription = async (subscriptionId: string) => {
  try {
    console.log("[CANCEL] Canceling subscription:", subscriptionId);
    
    // Ensure subscription exists
    const existingSubscription = await db
      .selectFrom("subscription")
      .select(["id", "user_id"])
      .where("id", "=", subscriptionId)
      .executeTakeFirst();
    
    if (!existingSubscription) {
      throw new Error("Subscription not found");
    }
    
    // Update the subscription to cancel at period end
    const updatedSubscription = await stripe.subscriptions.cancel(subscriptionId);
    
    if (!updatedSubscription) {
      throw new Error("Failed to cancel subscription");
    }
    
    // Update the subscription status in the database
    await db
      .updateTable("subscription")
      .set({
        cancel_at_period_end: true
      })
      .where("id", "=", subscriptionId)
      .execute();
    
    console.log("[CANCEL] Successfully canceled subscription for the end of billing period");
    
    return {
      success: true,
      currentPeriodEnd: new Date(updatedSubscription.current_period_end * 1000).toISOString()
    };
  } catch (error) {
    console.error("[CANCEL] Error canceling subscription:", error);
    throw error;
  }
};