"use server";

import { db } from "../../lib/db.ts";
// Import SUPERUSER_EMAILS, BETA_USER_EMAILS from server constants

/**
 * Updates the email_daily setting for a user
 * 
 * @param channelId - The channel ID of the user
 * @param enabled - Whether daily emails should be enabled (true) or disabled (false)
 * @returns Promise<boolean> - True if the update was successful, false otherwise
 */
export const updateDailyEmailSetting = async (
  channelId: number,
  enabled: boolean
): Promise<boolean> => {
  try {
    console.log(`[USER] Updating email_daily setting for channel ${channelId} to ${enabled}`);
    
    // Convert boolean to integer (1 = true, 0 = false) for SQLite
    const emailDailyValue = enabled ? 1 : 0;
    
    // Update the user record
    const result = await db
      .updateTable("user")
      .set({
        emailDaily: emailDailyValue,
      })
      .where("channelId", "=", channelId)
      .execute();
    
    console.log(`[USER] Updated email_daily setting for channel ${channelId}`, result);
    return true;
  } catch (error) {
    console.error(`[USER] Error updating email_daily setting for channel ${channelId}:`, error);
    return false;
  }
};

/**
 * Updates the selected coach for a user
 * 
 * @param channelId - The channel ID of the user
 * @param coachName - The name of the selected coach
 * @returns Promise<boolean> - True if the update was successful, false otherwise
 */
export const updateSelectedCoach = async (
  channelId: number,
  coachName: string
): Promise<boolean> => {
  try {
    console.log(`[USER] Updating selected coach for channel ${channelId} to ${coachName}`);
    
    // Update the user record
    const result = await db
      .updateTable("user")
      .set({
        selectedCoach: coachName,
      })
      .where("channelId", "=", channelId)
      .execute();
    
    console.log(`[USER] Updated selected coach for channel ${channelId}`, result);
    return true;
  } catch (error) {
    console.error(`[USER] Error updating selected coach for channel ${channelId}:`, error);
    return false;
  }
};

/**
 * Checks if a user email belongs to a superuser.
 * 
 * @param email - The email address to check.
 * @returns Promise<boolean> - True if the email belongs to a superuser, false otherwise.
 */