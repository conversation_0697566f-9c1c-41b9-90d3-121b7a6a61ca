"use server";
import { appendToEncryptedProfileJSON } from "./profile-append.ts";
import { getAuthenticatedUser } from "../../lib/auth-helper.ts";

export async function appendProfileAction(raw: string) {
  const user = await getAuthenticatedUser();

  if (!user?.channelId) {
    console.error("User authenticated but channelId is missing in appendProfileAction. User:", user);
    throw "not signed in";
  }

  const snippet = raw.replace(/^#profile#/i, "").trim();
  if (!snippet) throw "empty snippet";

  await appendToEncryptedProfileJSON(user.channelId.toString(), snippet);
}
