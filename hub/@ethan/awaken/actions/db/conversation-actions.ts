"use server";

import { v4 as uuidv4 } from "npm:uuid";
import { db } from "../../lib/db.ts";
import Reframe from "@";
import CryptoJS from "npm:crypto-js";
import { sql } from "npm:kysely";
import { Storage } from "npm:@google-cloud/storage";
import { Buffer } from "node:buffer";
import { transcription<PERSON>he<PERSON> } from "../../lib/server-constants.ts";
import { geminiRequest } from "../../lib/helper.ts";
import { handleUserTurn } from "../../lib/organic-email-scheduler.ts"; // Using scheduler to avoid circular dependency


// Define the expected return type for saveMessage
// This needs to match the structure returned at the end of saveMessage
// Also allow null for cases where cleaning removes all content
interface SaveMessageResult {
  id: string;
  fields: {
    Channel: number;
    Sender: string;
    Date: string;
    Status: string;
    Type: string;
    CoachName: string;
    Audio: string | null;
  }
}

/**
 * Save a new message to the conversation table
 * 
 * @param channelId - The user's channel ID
 * @param sender - Who sent the message (user or assistant)
 * @param content - The message content (will be encrypted)
 * @param date - Optional date (defaults to current timestamp)
 * @param status - Optional status (defaults to "Default")
 * @param messageType - Optional message type (defaults to "message")
 * @param coachName - Optional coach name (defaults to default coach)
 * @returns The created message record or null if content was empty after cleaning
 */
export const saveMessage = async (
  channelId: string | number, 
  sender: string, 
  content: string, 
  date?: string,
  status = "Default",
  messageType = "message",
  coachName?: string,
  audio: string | null = null
): Promise<SaveMessageResult | null> => {
  try {
    console.log(`[DB] Saving message from ${sender} for channel ${channelId}`);
    console.log(`[DB] Using coach: ${coachName || 'default (will be resolved later)'}`);
    
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }
    
    // Clean the content before encryption
    const cleanedContent = content
      .replace(/\[.*?\]/g, '') // Remove text within square brackets
      .replace(/<.*?>/g, '') // Remove text within angle brackets
      .trim(); // Trim leading/trailing whitespace
    
    // Only proceed if there's content left after cleaning
    if (!cleanedContent) {
      console.log(`[DB] Message content is empty after cleaning. Skipping save.`);
      return null; // Or handle as appropriate, maybe return a specific indicator?
    }

    // Encrypt the cleaned content
    const encryptedContent = CryptoJS.AES.encrypt(cleanedContent, secretKey).toString();
    
    // CRITICAL FIX: All messages MUST use ISO format consistently
    // This format: "2025-03-04T17:54:00.799Z"
    const messageDate = date || new Date().toISOString();
    
    // Ensure we have a proper ISO string by validating format
    let validatedDate = messageDate;
    if (!messageDate.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/)) {
      console.log(`[DB] Date format validation failed, creating new ISO date. Invalid format: ${messageDate}`);
      validatedDate = new Date().toISOString();
    }
    
    console.log(`[DB] Using date for storage: ${validatedDate}`);
    
    // Generate a new UUID for the message
    const messageId = uuidv4();
    
    const message = await db
      .insertInto("conversation")
      .values({
        id: messageId,
        channel_id: Number(channelId),
        sender,
        content: encryptedContent,
        date: validatedDate, // Use validated ISO format
        status,
        message_type: messageType,
        coachName: coachName,
        audio: audio,
        created_at: new Date().toISOString()
      })
      .returning(["id", "channel_id", "sender", "date", "status", "message_type", "coachName"])
      .executeTakeFirst();
    
    console.log(`[DB] Message saved successfully with ID ${messageId}`);
    console.log(`[DB] Stored with date: ${validatedDate}`);
    
    // Trigger organic email scheduling for user messages when feature is enabled
    console.log(`[DB] Checking organic email feature: FEAT_ORGANIC_EMAIL=${Reframe.env.FEAT_ORGANIC_EMAIL}, sender=${sender}`);
    if (Reframe.env.FEAT_ORGANIC_EMAIL === "on" && sender === "user") {
      console.log(`[DB] Triggering organic email for user ${channelId}, coach ${coachName || "Kokoro"}`);
      try {
        await handleUserTurn(String(channelId), coachName || "Kokoro");
        console.log(`[DB] ✅ Successfully scheduled organic email for user ${channelId}`);
      } catch (error) {
        console.error(`[DB] ❌ Failed to schedule organic email for user ${channelId}:`, error);
        console.error(`[DB] Error details:`, error.stack);
        // Don't throw error - organic email scheduling shouldn't break message saving
      }
    } else {
      console.log(`[DB] Skipping organic email: feature=${Reframe.env.FEAT_ORGANIC_EMAIL}, sender=${sender}`);
    }
    
    // Return in the same format as Airtable implementation
    return {
      id: messageId,
      fields: {
        Channel: Number(channelId),
        Sender: sender,
        Date: validatedDate, // Use validated ISO format in response
        Status: status,
        Type: messageType,
        CoachName: coachName || "Kokoro", // Ensure coach name is always set
        Audio: audio
      }
    };
  } catch (error) {
    console.error("[DB] Error saving message:", error);
    throw error;
  }
};

/**
 * Fetch messages for a channel with optional pagination and coach filter
 * 
 * @param channelId - The user's channel ID
 * @param limit - Maximum number of messages to return (default: 25)
 * @param before - Optional ISO date string to fetch messages before a certain time
 * @param decryptContent - Whether to decrypt message content (default: true)
 * @param coachName - Optional coach name to filter messages (defaults to default coach, use "all" to show messages from all coaches)
 * @param messageType - Optional message type to filter by (e.g., "message", "daily_awakening")
 * @returns Array of messages, sorted by date (oldest first)
 */
export const getMessages = async (
  channelId: string | number,
  limit = 25,
  before?: string,
  decryptContent = true,
  coachName?: string,
  messageTypes?: string[]
) => {
  try {
    console.log("THIS CALLED GET MESSAGES");
    console.time("getMessages");
    console.log(`[DB] Fetching messages for channel ${channelId}`);
    console.log(`[DB] Params: limit=${limit}, before=${before || 'none'}, decrypt=${decryptContent}, coachName=${coachName || 'default'}, messageTypes=${messageTypes}`);
    
    // Check if we want to show all coaches
    const showAllCoaches = coachName === "all";
    
    // Fetch default coach if coachName is not provided and we're not showing all coaches
    const effectiveCoachName = showAllCoaches 
      ? ""  // Use empty string instead of null to maintain type compatibility
      : (coachName || "Kokoro");
    
    console.log(`[DB] Coach filter: ${showAllCoaches ? 'DISABLED (showing all coaches)' : effectiveCoachName}`);
    
    let query = db
      .selectFrom("conversation")
      .select(["id", "channel_id", "sender", "content", "date", "status", "message_type", "audio", "coachName", "seenByUser"])
      .where("channel_id", "=", Number(channelId))
      .orderBy("date", "desc")
      .limit(limit);
    
    // Log the query structure for debugging
    console.log(`[DB] Query SELECT columns:`, ["id", "channel_id", "sender", "content", "date", "status", "message_type", "audio", "coachName", "seenByUser"]);
    
    // Only apply coach filter if we're not showing all coaches
    if (!showAllCoaches && effectiveCoachName) {
      query = query.where("coachName", "=", effectiveCoachName);
      console.log(`[DB] Filtering messages by coachName = "${effectiveCoachName}"`);
    } else {
      console.log(`[DB] Showing messages from ALL coaches (filter disabled)`);
    }
    
    // Add before filter if provided
    if (before) {
      try {
        query = query.where("date", "<", before);
        console.log(`[DB] Using date filter: ${before}`);
      } catch (error) {
        console.error("[DB] Invalid date format for 'before' parameter:", before);
      }
    }
    
    // Add message type filter if provided
    if (messageTypes) {
      query = query.where("message_type", "in", messageTypes);
      console.log(`[DB] Filtering messages by message_type = "${messageTypes}"`);
    }
    
    const messages = await query.execute();
    console.log(`[DB] Retrieved ${messages.length} messages from database`);
    
    // Debug raw data from database
    if (messages.length > 0) {
      // Log coach names in the raw database results
      const dbCoachNames = [...new Set(messages.map(m => m.coachName || 'unknown'))];
      console.log(`[DB] Raw coach_name values in database results: ${dbCoachNames.join(', ')}`);
      

      
      // Log a sample message
      console.log(`[DB] Sample raw message from DB:`, {
        id: messages[0].id,
        coach_name: messages[0].coachName,
        sender: messages[0].sender,
        content_preview: messages[0].content.substring(0, 30) + '...'
      });
    }
    
    console.timeEnd("getMessages");
    
    if (decryptContent) {
      // Decrypt message content
      const secretKey = Reframe.env.SECRET_KEY;
      if (!secretKey) {
        throw new Error("Encryption key not found");
      }
      
      const decryptedMessages = messages.map(message => {
        try {
          const decryptedContent = CryptoJS.AES.decrypt(message.content, secretKey).toString(CryptoJS.enc.Utf8);

          // Add logging for coach_name
          // console.log(`[DEBUG] Raw coach_name from DB: "${message.coachName}" for message ID: ${message.id}`);
          // console.log("MESSAGE", message);
          return {
            Id: message.id, // Capital 'Id' for backward compatibility
            Channel: message.channelId,
            Sender: message.sender,
            Content: decryptedContent,
            Date: message.date, // Keep original ISO format
            Status: message.status,
            Type: message.messageType,
            Audio: message.audio,
            CoachName: message.coachName || "Kokoro", // Use default if coach_name is null/undefined
            SeenByUser: message.seenByUser
          };
        } catch (error) {
          console.error(`[DB] Failed to decrypt message ${message.id}:`, error);
          return null;
        }
      }).filter(message => message !== null);
      
      // Return in reverse order (oldest first) for consistency with existing code
      return decryptedMessages.reverse();
    }
    
    // If not decrypting, just format and return
    return messages.map(message => ({
      Id: message.id,
      Channel: message.channelId,
      Sender: message.sender,
      Content: message.content,
      Date: message.date, // Keep original ISO format
      Status: message.status,
      Type: message.messageType,
      Audio: message.audio,
      CoachName: message.coachName || "Kokoro", // Use default if coach_name is null/undefined
      SeenByUser: message.seenByUser
    })).reverse();
    
  } catch (error) {
    console.error("[DB] Error fetching messages:", error);
    throw error;
  }
};

/**
 * Get all messages for a channel as raw objects
 * Used by prepareUserPromptClaudeAction and other functions that need the raw data
 * 
 * @param channelId - The user's channel ID
 * @param parse - Whether to return as objects (true) or JSON string (false)
 * @param coachName - Optional coach name to filter messages (defaults to default coach, use "all" to show messages from all coaches)
 * @param messageCount - Optional number of messages to return (defaults to 25)
 * @param messageType - Optional message type to filter by (e.g., "message", "daily_awakening")
 * @returns Messages as objects or JSON string
 */
export const getAllMessagesRaw = async (channelId: string, parse = false, coachName?: string, messageCount: number = 25, messageTypes?: string[]) => {
  try {
    console.log(`[DB] Getting all raw messages for channel ${channelId}, coachName=${coachName || 'default'}, messageCount=${messageCount}, messageTypes=${messageTypes}`);
    
    // Check if we want to show all coaches
    const showAllCoaches = coachName === "all";
    
    // To match getMessages implementation, pass "all" when showing all coaches
    const effectiveCoachName = showAllCoaches 
      ? "all"  // Pass "all" to maintain the coach filter bypass
      : (coachName || "Kokoro");
    
    console.log(`[DB] getAllMessagesRaw - Requested coach: "${coachName || 'none'}", using: ${effectiveCoachName === "all" ? 'ALL COACHES' : `"${effectiveCoachName}"`}`);
    
    // Call getMessages with the effective coach name
    // Provide defaults for all parameters to avoid undefined values
    const messages = await getMessages(
      channelId, 
      messageCount, 
      undefined, // before parameter (always undefined in this context)
      true, // decryptContent parameter (always true)
      effectiveCoachName,
      messageTypes
    );
    
    console.log(`[DB] Retrieved ${messages.length} messages with ISO date format`);
    
    // Check coach names in messages
    if (messages.length > 0) {
      const coachesInMessages = [...new Set(messages.map(m => m.CoachName))];
      console.log(`[DB] Coaches found in retrieved messages: ${coachesInMessages.join(', ')}`);
    }
    
    return parse ? messages : JSON.stringify(messages);
  } catch (error) {
    console.error("[DB] Error getting raw messages:", error);
    return parse ? [] : "[]";
  }
};

/**
 * Update the status of a message (e.g., star/unstar)
 * 
 * @param messageId - The ID of the message to update
 * @param newStatus - The new status to set (e.g., "Default", "Starred")
 * @returns The updated message
 */
export const updateMessageStatus = async (messageId: string, newStatus: string) => {
  try {
    console.log(`[DB] Updating message ${messageId} status to ${newStatus}`);
    
    const updatedMessage = await db
      .updateTable("conversation")
      .set({ status: newStatus })
      .where("id", "=", messageId)
      .returning(["id", "status"])
      .executeTakeFirst();
    
    console.log(`[DB] Message status updated successfully`);
    
    // Return in a format compatible with the Airtable response
    return {
      records: [
        {
          id: updatedMessage.id,
          fields: {
            Status: updatedMessage.status
          }
        }
      ]
    };
  } catch (error) {
    console.error("[DB] Error updating message status:", error);
    throw error;
  }
};

/**
 * Get starred messages for a user
 * 
 * @param channelId - The user's channel ID
 * @param limit - Maximum number of messages to return (default: 24)
 * @param coachName - Optional coach name to filter messages (defaults to default coach)
 * @returns Array of starred messages
 */
export const getStarredMessages = async (channelId: string | number, limit = 120, coachName?: string) => {
  try {
    console.log(`[DB] Getting starred messages for channel ${channelId}, coachName=${coachName || 'default'}`);
    
    // Fetch default coach if coachName is not provided
    const effectiveCoachName = coachName || "Kokoro";
    
    const starredMessages = await db
      .selectFrom("conversation")
      .select(["id", "channel_id", "sender", "content", "date", "status", "message_type", "audio", "coachName", "seenByUser"])
      .where("channel_id", "=", Number(channelId))
      .where("status", "=", "Starred")
      .orderBy("date", "desc")
      .limit(limit)
      .execute();
    
    console.log(`[DB] Retrieved ${starredMessages.length} starred messages`);
    
    // Decrypt and format messages
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }
    
    const decryptedMessages = starredMessages.map(message => {
      try {
        const decryptedContent = CryptoJS.AES.decrypt(message.content, secretKey).toString(CryptoJS.enc.Utf8);
        
        return {
          Id: message.id,
          Channel: message.channel_id,
          Sender: message.sender,
          Content: decryptedContent,
          Date: message.date,
          Status: message.status,
          Type: message.message_type,
          Audio: message.audio,
          CoachName: message.coachName || effectiveCoachName, // Use actual coachName from DB, fallback to effectiveCoachName
          SeenByUser: message.seenByUser
        };
      } catch (error) {
        console.error(`[DB] Failed to decrypt starred message ${message.id}:`, error);
        return null;
      }
    }).filter(message => message !== null);
    
    return decryptedMessages;
  } catch (error) {
    console.error("[DB] Error getting starred messages:", error);
    throw error;
  }
};

/**
 * Get the first message for a channel
 * 
 * @param channelId - The user's channel ID
 * @param coachName - Optional coach name to filter messages (defaults to default coach)
 * @returns The first message or null if not found
 */
export const getFirstMessage = async (channelId: string | number, coachName?: string) => {
  try {
    console.log(`[DB] Getting first message for channel ${channelId}, coachName=${coachName || 'default'}`);
    
    // Fetch default coach if coachName is not provided
    const effectiveCoachName = coachName || "Kokoro";
    
    const firstMessage = await db
      .selectFrom("conversation")
      .select(["id", "channel_id", "sender", "content", "date", "status", "message_type", "audio", "seenByUser"])
      .where("channel_id", "=", Number(channelId))
      .where("coachName", "=", effectiveCoachName) // Add coachName filter
      .orderBy("date", "asc")
      .limit(1)
      .executeTakeFirst();
    
    if (!firstMessage) {
      console.log(`[DB] No messages found for channel ${channelId} with coach ${effectiveCoachName}`);
      return null;
    }
    
    // Decrypt message content
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }
    
    const decryptedContent = CryptoJS.AES.decrypt(firstMessage.content, secretKey).toString(CryptoJS.enc.Utf8);
    
    console.log(`[DB] First message found with date ${firstMessage.date}`);
    
    return {
      Id: firstMessage.id,
      Channel: firstMessage.channelId,
      Sender: firstMessage.sender,
      Content: decryptedContent,
      Date: firstMessage.date,
      Status: firstMessage.status,
      Type: firstMessage.messageType,
      CoachName: effectiveCoachName, // Include coachName in response
      Audio: firstMessage.audio,
      SeenByUser: firstMessage.seenByUser
    };
  } catch (error) {
    console.error("[DB] Error getting first message:", error);
    return null;
  }
};

/**
 * Get daily awakening messages for a specific user
 * 
 * @param channelId - The user's channel ID
 * @param limit - Maximum number of messages to return (default: 7)
 * @param coachName - Optional coach name to filter messages
 * @returns Array of daily awakening messages, sorted by date (newest first)
 */
export const getDailyAwakeningMessages = async (channelId: string | number, limit = 7, coachName?: string) => {
  try {
    console.log(`[DB] Getting daily awakening messages for channel ${channelId}`);
    
    // Fetch default coach if coachName is not provided
    const effectiveCoachName = coachName || "Kokoro";
    
    const dailyMessages = await db
      .selectFrom("conversation")
      .select(["id", "channel_id", "sender", "content", "date", "status", "message_type", "seenByUser"])
      .where("channel_id", "=", Number(channelId))
      .where("message_type", "=", "daily_awakening")
      .where("coachName", "=", effectiveCoachName)
      .orderBy("date", "desc")
      .limit(limit)
      .execute();
    
    console.log(`[DB] Retrieved ${dailyMessages.length} daily awakening messages`);
    
    // Decrypt and format messages
    const secretKey = Reframe.env.SECRET_KEY;
    if (!secretKey) {
      throw new Error("Encryption key not found");
    }
    
    const decryptedMessages = dailyMessages.map(message => {
      try {
        const decryptedContent = CryptoJS.AES.decrypt(message.content, secretKey).toString(CryptoJS.enc.Utf8);
        
        return {
          Id: message.id,
          Channel: message.channelId,
          Sender: message.sender,
          Content: decryptedContent,
          Date: message.date,
          Status: message.status,
          Type: message.messageType,
          CoachName: effectiveCoachName,
          SeenByUser: message.seenByUser
        };
      } catch (error) {
        console.error(`[DB] Failed to decrypt daily message ${message.id}:`, error);
        return null;
      }
    }).filter(message => message !== null);
    
    return decryptedMessages;
  } catch (error) {
    console.error("[DB] Error getting daily awakening messages:", error);
    return [];
  }
};

/**
 * Type definition for decrypted messages 
 */
export interface DecryptedMessage {
  Id: string;
  Channel: number;
  Sender: string;
  Content: string;
  Date: string;
  Status: string;
  Type: string;
  Audio?: string;
  CoachName?: string;
  SeenByUser?: boolean;
}

/**
 * Result type for message history preparation
 */
export interface PrepMessageHistoryResult {
  conversationText: string;
  timeElapsedNote: string;
}

/**
 * Prepares conversation history (oldest-first) as "Sender: Content" lines
 * and returns a note about how much time has elapsed since the last message.
 * 
 * @param messages - Array of decrypted messages
 * @returns Object containing formatted conversation text and time elapsed note
 */
export const prepMessageHistoryAction = (messages: DecryptedMessage[]): PrepMessageHistoryResult => {
  console.log("[PREP] Preparing message history from", messages.length, "messages");
  
  // 1) Compute time difference since last message
  let timeElapsedNote = "[first message from client]";
  
  if (Array.isArray(messages) && messages.length > 0) {
    const lastMsgDate = messages[messages.length - 1].Date;
    if (lastMsgDate) {
      const lastMsgTime = new Date(lastMsgDate).getTime();
      const now = Date.now();
      const diffMs = now - lastMsgTime;
      
      if (diffMs > 0) {
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffMinutes = Math.floor(diffMs / (1000 * 60)) % 60;
        timeElapsedNote = `(TIME_ELAPSED: ${diffHours} hours ${diffMinutes} minutes since the above thread)`;
      }
    }
  }
  console.log("[PREP] Time elapsed note:", timeElapsedNote);

  // 2) Assemble "previous conversation" as lines "Sender: Content", oldest-first
  console.log("[PREP] Assembling conversation text");
  const sorted = Array.isArray(messages) 
    ? [...messages].sort((a, b) => new Date(a.Date).getTime() - new Date(b.Date).getTime())
    : [];
    
  const conversationText = sorted
    .map((msg) => `${msg.Sender}: ${msg.Content}`.replace(/"""/g, "'"))
    .join("\n");
  console.log("[PREP] Conversation text assembled successfully");
  
  return { conversationText, timeElapsedNote };
}; 

export const uploadAudio = async (audioBlob: Blob, channelId: string, messageId?: string) => {
  try {
    // Generate a unique filename using UUID and add extension
    const fileName = `${channelId}_${messageId || uuidv4()}_${Date.now()}.mp3`;
    
    // TODO: Move these credentials to environment variables
    const secret = ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;
    
    const projectId = 'trim-approach-381018';
    const bucketName = 'awaken-audio-files';

    const storage = new Storage({
      projectId: projectId,
      credentials: secret
    });
    
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(fileName);
    
    // Convert Blob to buffer for GCP storage
    const arrayBuffer = await audioBlob.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Upload with appropriate content type
    await file.save(buffer, {
      contentType: audioBlob.type, // Use the actual blob type instead of hardcoding
      metadata: {
        channelId: channelId,
        messageId: messageId || 'none',
        uploadTime: new Date().toISOString()
      }
    });
    
    console.log(`[DB] Audio file ${fileName} uploaded successfully`);

    // const audioUrl = `https://storage.googleapis.com/${bucketName}/${fileName}`;
    const audioUrl = `https://www.googleapis.com/download/storage/v1/b/${bucketName}/o/${fileName}?alt=media`

    // update to set the audio url to the message with assistant message id
    if(messageId) {
      try {
        await db.updateTable("conversation").set({ audio: audioUrl }).where("id", "=", messageId).execute();
      } catch (error) {
        console.error("[DB] Error updating message with audio URL:", error);
      }
    }
    
    // Return the public URL
    return audioUrl;
  } catch (error) {
    console.error("[DB] Error uploading audio:", error);
    throw error;
  }
};

export const generateTranscription = async (audioBlob: Blob, provider: 'openai' | 'deepgram' = 'openai') => {
  const formData = new FormData();
  formData.append("file", audioBlob, "audio.webm");
  formData.append("model", "whisper-1");

  console.log("[ACTION] Generating transcription");
  

  if (provider === 'openai') {
    console.log("[ACTION] Generating transcription with OpenAI");
    formData.append("model", "whisper-1");
    const response = await fetch("https://api.openai.com/v1/audio/transcriptions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${Reframe.env.OPENAI_API_KEY}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
    }
    const data = await response.json();
    return data;
  } else if (provider === 'deepgram') {
    console.log("[ACTION] Generating transcription with Deepgram");
    const deepgramApiKey = Reframe.env.DEEPGRAM_API_KEY;
    
    const response = await fetch("https://api.deepgram.com/v1/listen?language=en&model=nova-3&smart_format=true", {
      method: "POST",
      headers: {
        "Authorization": `Token ${deepgramApiKey}`,
        "Content-Type": audioBlob.type 
      },
      body: audioBlob,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
    }
    const data = await response.json();
    // Updated parsing based on the provided Deepgram response format
    const transcript = data.results?.channels?.[0]?.alternatives?.[0]?.transcript || "";
    return { text: transcript }; 
  } else {
    throw new Error("Invalid transcription provider specified. Choose 'openai' or 'deepgram'.");
  }
};

/**
 * Transcode arbitrary audio bytes to MP3 in Deno.
 *
 * @param inputBytes  – raw bytes of your source audio (MP4/AAC, WebM/Opus, WAV, etc.)
 * @param bitrate     – desired audio bitrate (e.g. "128k", "192k")
 * @returns           – Uint8Array of MP3 data
 */
export async function transcodeToMp3(
  inputBytes: ArrayBuffer | Uint8Array,
  bitrate = "128k",
): Promise<Uint8Array> {
  // 1) Create temp files
  const inPath  = await Deno.makeTempFile({ prefix: "audio-in-", suffix: ".tmp" });
  const outPath = `${inPath}.mp3`;

  // 2) Write the input buffer to disk
  const data = inputBytes instanceof Uint8Array ? inputBytes : new Uint8Array(inputBytes);
  await Deno.writeFile(inPath, data);

  // 3) Invoke FFmpeg via Deno.Command
  const cmd = new Deno.Command("ffmpeg", {
    args: [
      "-y",           // overwrite output if exists
      "-i", inPath,   // input file
      "-b:a", bitrate,// e.g. "128k"
      "-vn",          // drop any video streams
      outPath,        // output file
    ],
    stderr: "piped",
  });

  const { code, stderr } = await cmd.output();
  if (code !== 0) {
    const err = new TextDecoder().decode(stderr);
    // clean up before throwing
    await Promise.all([Deno.remove(inPath), Deno.remove(outPath)]).catch(() => {});
    throw new Error(`FFmpeg failed (code ${code}):\n${err}`);
  }

  // 4) Read back the MP3 bytes
  const mp3Bytes = await Deno.readFile(outPath);

  // 5) Clean up temp files
  await Promise.all([Deno.remove(inPath), Deno.remove(outPath)]);

  return mp3Bytes;
}

/**
 * Server action to process audio data and send it to Whisper for transcription
 * 
 * @param encodedAudio - Array of numbers representing msgpack-encoded audio data
 * @param channelId - The user's channel ID
 * @returns Transcription result
 */
export const processAudioTranscriptionAction = async (arrayBuffer: ArrayBuffer, channelId: string) => {
  try {
    console.log(`[AUDIO] Processing audio transcription request for channel ${channelId}`);
    
    // Convert to Blob
    // const mp3Bytes = await transcodeToMp3(arrayBuffer);
    const audioBlob = new Blob([arrayBuffer], { type: "audio/mp3" });
    
    console.log(`[AUDIO] Successfully decoded audio blob of size: ${audioBlob.size} bytes`);

    const formData = new FormData();
    
    const mimeType = audioBlob.type.split(";")[0];
    const extension = mimeType.split("/")[1] || "webm";
    const fileName = `audio-recording-user-${channelId}-${new Date().toISOString()}.${extension}`;
    formData.append("file", audioBlob, fileName);
    
    // generate transcription with openai whisper
    const transcription = await generateTranscription(audioBlob, "deepgram");

    console.log(`[AUDIO] Successfully received transcription response`);

    try {
      // check transcription with transcription checker
      // call gemini with transcription checker
      // transcription checker is a payload for gemini call
      const transcriptionCheckerPayload = transcriptionChecker(transcription.text);
      const response = await geminiRequest([transcriptionCheckerPayload]);
      const data = await response.json();

      if(data.text1.includes("<BLANK>")) {
        throw new Error("Transcription is blank");
      }
    } catch (error) {
      console.error("[AUDIO] Error checking transcription:", error);
      throw error; // Re-throw the error to stop further processing in this function and notify the caller
    }
    
    console.log("ITS THE NEW TRANSCRIPTION SYSTEM");
    return transcription.text;
  } catch (error) {
    console.error("[AUDIO] Error processing audio transcription:", error);
    throw error;
  }
};

/**
 * Mark all messages from a coach in a channel as seen by the user.
 * This is triggered when a user opens a conversation.
 * @param channelId - The user's channel ID
 * @param coachName - The coach whose messages are to be marked as seen
 */
export const markMessagesSeenByUser = async (
  channelId: string | number,
  coachName: string,
) => {
  try {
    // Only mark NORMAL conversation messages (exclude coach thread messages)
    const result = await db
      .updateTable("conversation")
      .set({ seenByUser: 1 })
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("sender", "!=", "user")
      .where("message_type", "in", ["message", "summary"])
      .where((eb) => eb.or([
        eb("seenByUser", "=", 0),
        eb("seenByUser", "is", null),
      ]))
      .executeTakeFirst();

    console.log(`[DB] Marked CONVERSATION messages as seen by user for channel ${channelId} and coach ${coachName}. Rows updated: ${result.numUpdatedRows}`);
    return { success: true };
  } catch (error) {
    console.error("[DB] Error marking messages as seen by user:", error);
    return { success: false, error: (error as Error).message };
  }
};

/**
 * Mark all coach thread (message_type = 'coach_message') messages from a coach in a channel as seen by the user.
 * This is triggered when a user opens the coach thread overlay.
 * @param channelId - The user's channel ID
 * @param coachName - The coach whose thread messages are to be marked as seen
 */
export const markCoachThreadMessagesSeenByUser = async (
  channelId: string | number,
  coachName: string,
) => {
  try {
    const result = await db
      .updateTable("conversation")
      .set({ seenByUser: 1 })
      .where("channelId", "=", Number(channelId))
      .where("coachName", "=", coachName)
      .where("sender", "!=", "user")
      .where("message_type", "=", "coach_message")
      .where((eb) => eb.or([
        eb("seenByUser", "=", 0),
        eb("seenByUser", "is", null),
      ]))
      .executeTakeFirst();

    console.log(`[DB] Marked COACH THREAD messages as seen by user for channel ${channelId} and coach ${coachName}. Rows updated: ${result.numUpdatedRows}`);
    return { success: true };
  } catch (error) {
    console.error("[DB] Error marking coach thread messages as seen by user:", error);
    return { success: false, error: (error as Error).message };
  }
};