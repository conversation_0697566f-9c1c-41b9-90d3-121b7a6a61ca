"use server";

import {
  getProfileText,
  setProfileText,
  decryptProfile,
  encryptProfile,
} from "../../action.ts";

/**
 * Append *snippet* to a given section of the encrypted profile JSON.
 *
 * ─ section         defaults to "quickNotes"
 * ─ divider         inserted when the section already has content
 *
 * If the current profile isn't valid JSON (old accounts), it will be
 * wrapped into { legacy:"…previous string…" } first so nothing is lost.
 */
export async function appendToEncryptedProfileJSON(
  channelId: string,
  snippet: string,
  section = "quickNotes",
  divider = "\n\n---\n\n"
) {
  /* 1 ─ fetch & decrypt -------------------------------------------------- */
  const { profileText } = await getProfileText(channelId);

  const raw = profileText || "{}";

  /* 2 ─ parse or wrap ---------------------------------------------------- */
  let obj: any;
  try {
    obj = JSON.parse(raw);
  } catch {
    // legacy plain-text profile → preserve under .legacy
    obj = { legacy: raw };
  }

  /* 3 ─ mutate the chosen section --------------------------------------- */
  obj.sections = obj.sections ?? {};
  const existing = obj.sections[section]?.text ?? "";
  obj.sections[section] = {
    text: (existing ? existing + divider : "") + snippet.trim(),
    showToUser: true,
  };

  /* 4 ─ re-encrypt & store ---------------------------------------------- */
  const jsonString = JSON.stringify(obj, null, 2);

  await setProfileText(
    channelId,
    jsonString
  );

  return obj; // combined profile object (useful for logging)
}
