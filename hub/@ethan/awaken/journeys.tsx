"use client";

import React from "npm:react@canary";
import { motion, AnimatePresence } from "npm:framer-motion";
import { useRef } from "npm:react@canary";
import { ScrollArea, Button, Text } from "@reframe/ui/main.tsx";
import { Logo } from "./lib/logo.tsx";
import { integrationUpdate } from "./action.ts";

interface IntegrationPageProps {
  user: any;
  awakening: { id: string; content: string } | null;
  practice: { id: string; content: string } | null;
  commitment: { id: string; content: string; date: string } | null;
  notes: { id: string; content: string } | null;
}

// Attempt to parse awakening.content as { insight: string; purpose: string } JSON
function parseAwakeningContent(raw?: string) {
  if (!raw) {
    return { insight: "", purpose: "" };
  }
  try {
    const parsed = JSON.parse(raw);
    return {
      insight: typeof parsed.insight === "string" ? parsed.insight : raw,
      purpose: typeof parsed.purpose === "string" ? parsed.purpose : "",
    };
  } catch (_err) {
    // If parse fails, treat the entire content as 'insight'
    return { insight: raw, purpose: "" };
  }
}

function formatCommitmentDate(rawDate?: string) {
  if (!rawDate) return "";
  const date = new Date(rawDate);
  const day = date.getDate();
  const monthShort = date.toLocaleString("en-GB", { month: "short" });
  let hour = date.getHours();
  const ampm = hour >= 12 ? "PM" : "AM";
  hour = hour % 12 || 12;
  const minute = date.getMinutes();
  const minutePad = minute < 10 ? `0${minute}` : minute; 
  return `${day} ${monthShort}, ${hour}.${minutePad} ${ampm}`;
}

export const IntegrationPage: React.FC<IntegrationPageProps> = ({
  user,
  awakening,
  practice,
  commitment,
  notes,
}) => {
  // Parse awakening content for separate insight/purpose
  const awakeningData = parseAwakeningContent(awakening?.content);
  const [insight, setInsight] = React.useState(awakeningData.insight);
  const [purpose, setPurpose] = React.useState(awakeningData.purpose);

  // For practice, commitment, notes, store content directly
  const [practiceContent, setPracticeContent] = React.useState(practice?.content || "");
  const [commitmentContent, setCommitmentContent] = React.useState(commitment?.content || "");
  const [notesContent, setNotesContent] = React.useState(notes?.content || "");

  // Panel editing states
  const [editingInsight, setEditingInsight] = React.useState(false);
  const [editingPurpose, setEditingPurpose] = React.useState(false);
  const [editingPractice, setEditingPractice] = React.useState(false);
  const [editingCommitment, setEditingCommitment] = React.useState(false);
  const [editingNotes, setEditingNotes] = React.useState(false);

  // Whether to show all subsequent panels
  const [showPanels, setShowPanels] = React.useState(Boolean(insight));

  async function updatePanel(panelType: string, recordId: string, newContent: string) {
    if (!recordId) {
      alert("No record found to update.");
      return;
    }
    try {
      let tableName = "";
      switch (panelType) {
        case "awakening":
          tableName = "awakenings";
          break;
        case "practice":
          tableName = "practices";
          break;
        case "commitment":
          tableName = "commitments";
          break;
        case "notes":
          tableName = "awakening_notes";
          break;
      }
      const data = await integrationUpdate(tableName, recordId, newContent);
      if (!data.success) {
        throw new Error(data.error || "Update failed");
      }
    } catch (error) {
      console.error("Failed to update panel:", error);
      alert(error.message);
    }
  }

  // Stubs for AI generation
  async function handleAiAssist(panel: string) {
    console.log("Generating content for", panel);
    // In a real scenario, call your AI endpoint here:
    // const resp = await fetch("/some-ai-endpoint", ...)
    // ...
    alert(`AI generation for ${panel} is not yet implemented.`);
  }

  // Save logic for Insight/Purpose => stored in awakening
  async function handleSaveAwakening() {
    if (!awakening) {
      // If no awakening record, just store locally
      setEditingInsight(false);
      setEditingPurpose(false);
      if (insight) {
        setShowPanels(true);
      }
      return;
    }
    // Combine them into JSON
    const combined = JSON.stringify({ insight, purpose });
    await updatePanel("awakening", awakening.id, combined);
    setEditingInsight(false);
    setEditingPurpose(false);
    if (insight) {
      setShowPanels(true);
    }
  }

  return (
    <div
      className="
        relative min-h-screen text-white flex flex-col px-4 py-6 items-center
        before:absolute before:inset-0 before:-z-10
        before:bg-[radial-gradient(circle_at_center,_rgba(252,163,17,0.3)_0%,_rgba(0,0,0,0)_70%)]
        bg-black
      "
    >
      <motion.div
        className="relative flex flex-col items-center min-h-dvh text-orange-300 p-6 w-full max-w-3xl"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0 }}
      >
        {/* swirl gradient behind */}
        <motion.div
          className="absolute w-[506px] h-[506px] rounded-full blur-[100px] pointer-events-none"
          style={{
            top: "-180px",
            background: "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
          }}
          initial={{ opacity: 0.4 }}
          animate={{ opacity: 0.4 }}
          transition={{ duration: 0 }}
        />
        {/* top nav */}
        <div className="relative w-full flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Logo size={20} />
            <span
              className="text-base font-normal text-white cursor-pointer"
              onClick={() => (globalThis.location.href = "/")}
            >
              awaken
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <img
              src={user?.image ?? ""}
              alt="User Profile"
              className="w-8 h-8 rounded-full"
            />
          </div>
        </div>

        <Text className="text-2xl font-semibold text-[#FCA311] mb-6">
          Journeys
        </Text>
        {/* main scroll container */}
        <ScrollArea className="w-full flex-1 overflow-auto space-y-4">
          {/* INSIGHT PANEL */}
          <motion.div
            className={`
              bg-orange-500/40 rounded-lg p-4 shadow-md transition-opacity
              ${!insight ? "opacity-60 hover:opacity-100" : ""}
            `}
          >
            <div className="flex items-center justify-between mb-2">
              <Text className="text-base font-semibold">What's awakening in you?</Text>
              <div className="flex gap-2">
                {editingInsight ? null : (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="!text-white hover:text-white/70"
                    onClick={() => setEditingInsight(true)}
                  >
                    Edit
                  </Button>
                )}
              </div>
            </div>
            {editingInsight ? (
              <div>
                <textarea
                  className="w-full bg-[#333] text-white rounded p-2 mb-2"
                  rows={4}
                  placeholder="Enter the insight you want to integrate..."
                  value={insight}
                  onChange={(e) => setInsight(e.target.value)}
                />
                <div className="flex justify-end gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setInsight(awakeningData.insight);
                      setEditingInsight(false);
                    }}
                  >
                    X
                  </Button>
                  <Button
                    size="sm"
                    onClick={async () => {
                      await handleSaveAwakening();
                    }}
                  >
                    Save
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-sm whitespace-pre-wrap">
                {insight || "Click Edit to add an insight..."}
              </div>
            )}
          </motion.div>

          <AnimatePresence>
            {showPanels && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden space-y-4"
              >
                {/* PURPOSE PANEL */}
                <motion.div
                  className={`
                    bg-orange-500/10 rounded-lg p-4 shadow-md transition-opacity
                    ${!purpose ? "opacity-60 hover:opacity-100" : ""}
                  `}
                >
                  <div className="flex items-center justify-between mb-2">
                    <Text className="text-base font-semibold">
                      How does this serve your deepest aspiration?
                    </Text>
                    <div className="flex gap-2">
                      {editingPurpose ? null : (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="!text-white hover:text-white/70"
                          onClick={() => setEditingPurpose(true)}
                        >
                          Edit
                        </Button>
                      )}
                    </div>
                  </div>
                  {editingPurpose ? (
                    <div>
                      <textarea
                        className="w-full bg-[#333] text-white rounded p-2 mb-2"
                        rows={4}
                        placeholder="This matters because..."
                        value={purpose}
                        onChange={(e) => setPurpose(e.target.value)}
                      />
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setPurpose(awakeningData.purpose);
                            setEditingPurpose(false);
                          }}
                        >
                          X
                        </Button>
                        <Button
                          size="sm"
                          onClick={async () => {
                            await handleSaveAwakening();
                          }}
                        >
                          Save
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-sm whitespace-pre-wrap">
                      {purpose || "This matters because..."}
                    </div>
                  )}
                </motion.div>

                {/* PRACTICE PANEL */}
                <motion.div
                  className={`
                    bg-amber-600/30 rounded-lg p-4 shadow-md transition-opacity
                    ${!practiceContent ? "opacity-60 hover:opacity-100" : ""}
                  `}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.25 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <Text className="text-base font-semibold">Daily Practice</Text>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="!text-white hover:text-white/70"
                        onClick={() => handleAiAssist("practice")}
                      >
                        ✨
                      </Button>
                      {editingPractice ? null : (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="!text-white hover:text-white/70"
                          onClick={() => setEditingPractice(true)}
                        >
                          Edit
                        </Button>
                      )}
                    </div>
                  </div>
                  {editingPractice ? (
                    <div>
                      <textarea
                        className="w-full bg-[#333] text-white rounded p-2 mb-2"
                        rows={4}
                        placeholder="your practice..."
                        value={practiceContent}
                        onChange={(e) => setPracticeContent(e.target.value)}
                      />
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setPracticeContent(practice?.content || "");
                            setEditingPractice(false);
                          }}
                        >
                          X
                        </Button>
                        <Button
                          size="sm"
                          onClick={async () => {
                            await updatePanel("practice", practice?.id || "", practiceContent);
                            setEditingPractice(false);
                          }}
                        >
                          Save
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-sm whitespace-pre-wrap">
                      {practiceContent || "your practice..."}
                    </div>
                  )}
                </motion.div>

                {/* COMMITMENT PANEL */}
                <motion.div
                  className={`
                    bg-red-800/30 rounded-lg p-4 shadow-md transition-opacity
                    ${!commitmentContent ? "opacity-60 hover:opacity-100" : ""}
                  `}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.25 }}
                >
                  <div className="flex items-center justify-between mb-2 relative">
                    {commitment?.date && (
                      <span className="absolute left-0 text-white/70 text-xs">
                        {formatCommitmentDate(commitment.date)}
                      </span>
                    )}
                    <Text className="mx-auto text-base font-semibold">
                      What action are you committed to?
                    </Text>
                    <div className="absolute right-0 flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="!text-white hover:text-white/70"
                        onClick={() => handleAiAssist("commitment")}
                      >
                        ✨
                      </Button>
                      {editingCommitment ? null : (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="!text-white hover:text-white/70"
                          onClick={() => setEditingCommitment(true)}
                        >
                          Edit
                        </Button>
                      )}
                    </div>
                  </div>
                  {editingCommitment ? (
                    <div>
                      <textarea
                        className="w-full bg-[#333] text-white rounded p-2 mb-2"
                        rows={4}
                        placeholder="I commit to..."
                        value={commitmentContent}
                        onChange={(e) => setCommitmentContent(e.target.value)}
                      />
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setCommitmentContent(commitment?.content || "");
                            setEditingCommitment(false);
                          }}
                        >
                          X
                        </Button>
                        <Button
                          size="sm"
                          onClick={async () => {
                            await updatePanel("commitment", commitment?.id || "", commitmentContent);
                            setEditingCommitment(false);
                          }}
                        >
                          Save
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-sm whitespace-pre-wrap">
                      {commitmentContent || "I commit to..."}
                    </div>
                  )}
                </motion.div>

                {/* NOTES PANEL */}
                <motion.div
                  className={`
                    bg-gray-900/80 rounded-lg p-4 shadow-md transition-opacity
                    ${!notesContent ? "opacity-60 hover:opacity-100" : ""}
                  `}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.25 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <Text className="text-base font-semibold">Notes</Text>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="!text-white hover:text-white/70"
                        onClick={() => handleAiAssist("notes")}
                      >
                        ✨
                      </Button>
                      {editingNotes ? null : (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="!text-white hover:text-white/70"
                          onClick={() => setEditingNotes(true)}
                        >
                          Edit
                        </Button>
                      )}
                    </div>
                  </div>
                  {editingNotes ? (
                    <div>
                      <textarea
                        className="w-full bg-[#333] text-white rounded p-2 mb-2"
                        rows={6}
                        placeholder="your reflections, insights, and notes"
                        value={notesContent}
                        onChange={(e) => setNotesContent(e.target.value)}
                      />
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setNotesContent(notes?.content || "");
                            setEditingNotes(false);
                          }}
                        >
                          X
                        </Button>
                        <Button
                          size="sm"
                          onClick={async () => {
                            await updatePanel("notes", notes?.id || "", notesContent);
                            setEditingNotes(false);
                          }}
                        >
                          Save
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-sm whitespace-pre-wrap">
                      {notesContent || "your reflections, insights, and notes"}
                    </div>
                  )}
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </ScrollArea>
      </motion.div>
    </div>
  );
};