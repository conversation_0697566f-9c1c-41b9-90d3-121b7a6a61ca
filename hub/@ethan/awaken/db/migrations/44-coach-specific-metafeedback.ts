import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // 1. Add meta_feedback column
  try {
    await db.schema
      .alterTable("user_coach_attributes")
      .addColumn("meta_feedback", "text", (col) => col.defaultTo(""))
      .execute();
    console.log("Added meta_feedback column to user_coach_attributes");
  } catch (e) {
    if (e.message.includes("duplicate column")) {
      console.log("meta_feedback column already exists, skipping");
    } else {
      throw e;
    }
  }

  // 2. Add messages_till_feedback column
  try {
    await db.schema
      .alterTable("user_coach_attributes")
      .addColumn("messages_till_feedback", "integer", (col) => col.defaultTo(4))
      .execute();
    console.log("Added messages_till_feedback column to user_coach_attributes");
  } catch (e) {
    if (e.message.includes("duplicate column")) {
      console.log("messages_till_feedback column already exists, skipping");
    } else {
      throw e;
    }
  }

  // 3. Populate data for all coaches users have access to
  try {
    const allUsers = await db
      .selectFrom("user")
      .select(["channel_id", "coach_access"])
      .where("coach_access", "is not", null)
      .execute();

    console.log(`Processing coach access for ${allUsers.length} users`);

    for (const user of allUsers) {
      try {
        const coachAccess = typeof user.coach_access === 'string' 
          ? JSON.parse(user.coach_access) 
          : user.coach_access;
        
        for (const [coachName, hasAccess] of Object.entries(coachAccess)) {
          if (hasAccess) {
            await db
              .insertInto("user_coach_attributes")
              .values({
                channel_id: user.channel_id,
                coach_name: coachName,
                data_sharing_enabled: 0, // SQLite boolean as integer
                coach_notes: "",
                meta_feedback: "", // Start with blank metafeedback
                messages_till_feedback: 4
              })
              .onConflict((oc) => oc.doNothing())
              .execute();
          }
        }
      } catch (error) {
        console.log(`Error processing coach access for user ${user.channel_id}:`, error);
      }
    }
  } catch (error) {
    console.error("Error populating data for coach access:", error);
    // Don't throw - allow migration to continue
  }

  console.log("Migration 44-coach-specific-metafeedback completed");
}

export async function down(db: Kysely<any>): Promise<void> {
  // Drop columns in reverse order
  try {
    await db.schema
      .alterTable("user_coach_attributes")
      .dropColumn("messages_till_feedback")
      .execute();
    console.log("Dropped messages_till_feedback column");
  } catch (e) {
    console.log("Failed to drop messages_till_feedback column:", e.message);
  }

  try {
    await db.schema
      .alterTable("user_coach_attributes")
      .dropColumn("meta_feedback")
      .execute();
    console.log("Dropped meta_feedback column");
  } catch (e) {
    console.log("Failed to drop meta_feedback column:", e.message);
  }
}