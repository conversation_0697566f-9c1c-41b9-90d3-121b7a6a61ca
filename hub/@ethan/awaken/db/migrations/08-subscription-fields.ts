import { Kysely } from "npm:kysely";

export async function up(db: Kysely<unknown>): Promise<void> {
  // Add subscription-related columns to user table
  await db.schema
    .alterTable("user")
    .addColumn("current_plan", "text", (col) => col.defaultTo('free_plan'))
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("plan_start_date", "timestamp")
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("messages_this_period", "integer", (col) => col.defaultTo(0))
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("call_mins_this_period", "integer", (col) => col.defaultTo(0))
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("total_messages", "integer", (col) => col.defaultTo(0))
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("total_call_mins", "integer", (col) => col.defaultTo(0))
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("stripe_customer_id", "text")
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("stripe_subscription_id", "text")
    .execute();

  // Create subscriptions table
  await db.schema
    .createTable("subscription")
    .addColumn("id", "text", (col) => col.primaryKey().notNull())
    .addColumn("user_email", "text", (col) => col.notNull())
    .addColumn("plan_id", "text", (col) => col.notNull())
    .addColumn("status", "text", (col) => col.notNull())
    .addColumn("created_at", "timestamp", (col) => col.notNull())
    .addColumn("current_period_start", "timestamp", (col) => col.notNull())
    .addColumn("current_period_end", "timestamp", (col) => col.notNull())
    .addColumn("canceled_at", "timestamp")
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("subscription").execute();
  
  // Remove subscription columns from user table
  const columns = [
    "current_plan",
    "plan_start_date", 
    "messages_this_period",
    "call_mins_this_period",
    "total_messages",
    "total_call_mins",
    "stripe_customer_id",
    "stripe_subscription_id"
  ];

  for (const column of columns) {
    await db.schema.alterTable("user").dropColumn(column).execute();
  }
} 