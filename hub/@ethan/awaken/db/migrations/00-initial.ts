import { Kysely, sql } from "npm:kysely";

const createIndex = async (
  db: Kysely<unknown>,
  table: string,
  indices: string[][],
) => {
  for (const index of indices) {
    await db.schema
      .createIndex(`idx_${table}_${index.join("_")}`)
      .on(table)
      .columns(index)
      .execute();
  }
};

export async function up(db: Kysely<unknown>): Promise<void> {
  // await db.schema
  //   .createTable("tenant")
  //   .addColumn("id", "uuid", (col) => col.primaryKey().notNull())
  //   .addColumn("name", "text", (col) => col.notNull())
  //   .addColumn("created_at", "timestamp", (col) => col.notNull())
  //   .execute();

  // await createIndex(db, "tenant", [
  //   ["id"],
  // ]);

  await db.schema
    .createTable("user")
    .addColumn("email", "text", (col) => col.primaryKey().notNull())
    .addColumn("password", "text", (col) => col.notNull())
    .addColumn("created_at", "timestamp", (col) => col.notNull())
    .addColumn("channel_id", "integer", (col) => col.notNull())
    .execute();

  await createIndex(db, "user", [
    ["email"],
  ]);

  // await db.schema
  //   .createTable("org")
  //   .addColumn("id", "uuid", (col) => col.primaryKey().notNull())
  //   .addColumn("name", "text", (col) => col.notNull())
  //   .addColumn("owner_id", "uuid", (col) => col.notNull())
  //   .addColumn("created_at", "timestamp", (col) => col.notNull())
  //   .execute();

  // await createIndex(db, "org", [
  //   ["id"],
  //   ["owner_id"],
  // ]);
}

export async function down(db: Kysely<any>): Promise<void> {}
