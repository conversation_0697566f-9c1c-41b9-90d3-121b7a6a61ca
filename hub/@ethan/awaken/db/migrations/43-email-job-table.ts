import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Create the email_job table
  await db.schema
    .createTable("email_job")
    .addColumn("id", "text", (col) => col.primaryKey())
    .addColumn("user_id", "text", (col) => col.notNull())
    .addColumn("coach_id", "text", (col) => col.notNull())
    .addColumn("stage", "integer", (col) => col.notNull().check(sql`stage BETWEEN 1 AND 3`))
    .addColumn("type", "text", (col) => col.notNull())
    .addColumn("send_at", "timestamp", (col) => col.notNull())
    .addColumn("sent_at", "timestamp")
    .addColumn("cancelled", "boolean", (col) => col.notNull().defaultTo(false))
    .addColumn("created_at", "timestamp", (col) => col.notNull())
    .execute();

  // Create index for finding due jobs
  await db.schema
    .createIndex("job_due_idx")
    .on("email_job")
    .columns(["send_at", "cancelled", "sent_at"])
    .execute();

  // Create unique index to ensure only one pending email per user
  await db.schema
    .createIndex("one_live")
    .on("email_job")
    .column("user_id")
    .unique()
    .where("sent_at", "is", null)
    .where("cancelled", "=", false)
    .execute();

  console.log("Created email_job table with indexes");
}

export async function down(db: Kysely<any>): Promise<void> {
  // Drop indexes first
  await db.schema.dropIndex("one_live").execute();
  await db.schema.dropIndex("job_due_idx").execute();
  
  // Drop the table
  await db.schema.dropTable("email_job").execute();
  
  console.log("Dropped email_job table and indexes");
}