import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  console.log("Starting migration: Swap user_email for user_channel_id in user_cards");

  // 1. Drop old indices referencing user_email
  const oldIndices = [
    "uc_user_offering_visible_sort_idx",
    "uc_user_offering_type_visible_idx",
  ];
  for (const indexName of oldIndices) {
    try {
      await db.schema.dropIndex(indexName).ifExists().execute();
      console.log(`Dropped old index: ${indexName}`);
    } catch (e) {
      console.warn(`Could not drop index ${indexName}, might not exist:`, e.message);
    }
  }

  // 2. Alter the table: Drop user_email, Add user_channel_id
  await db.schema
    .alterTable("user_cards")
    .dropColumn("user_email")
    .execute();
  console.log("Dropped user_email column from user_cards");

  await db.schema
    .alterTable("user_cards")
    // Assuming user.channelId is the primary way users are identified and is likely an integer
    .addColumn("user_channel_id", "integer", (col) =>
      col.notNull()
      // Add foreign key constraint referencing user table's channelId
      // Note: Ensure 'user' table and 'channelId' column exist and channelId is suitable as a foreign key target (e.g., unique or primary key)
      // Kysely/SQLite might handle foreign key definitions differently, adjust if needed.
      // .references("user.channelId") // <-- Add this if FK constraint is desired and supported
    )
    .execute();
  console.log("Added user_channel_id column to user_cards");
   
  // Add FK constraint separately if needed, depends on SQLite version and setup
  // Example (might need adjustment):
  // try {
  //   await sql`ALTER TABLE user_cards ADD CONSTRAINT fk_user_channel_id FOREIGN KEY (user_channel_id) REFERENCES user(channelId);`.execute(db);
  //   console.log("Added foreign key constraint user_channel_id -> user.channelId");
  // } catch (e) {
  //   console.error("Failed to add foreign key constraint:", e.message);
  //   // Decide how to handle failure (e.g., throw error, log warning)
  // }


  // 3. Recreate indices using user_channel_id
  await db.schema.createIndex("uc_channel_offering_visible_sort_idx")
    .on("user_cards")
    .columns(["user_channel_id", "coach_offering_id", "is_visible", "sort_order"])
    .execute();
  console.log("Created index: uc_channel_offering_visible_sort_idx");

  await db.schema.createIndex("uc_channel_offering_type_visible_idx")
    .on("user_cards")
    .columns(["user_channel_id", "coach_offering_id", "type", "is_visible"])
    .execute();
  console.log("Created index: uc_channel_offering_type_visible_idx");

  console.log("Finished migration: Swap user_email for user_channel_id");
}

export async function down(db: Kysely<any>): Promise<void> {
  console.log("Reverting migration: Swap user_channel_id back to user_email in user_cards");

  // 1. Drop new indices referencing user_channel_id
  const newIndices = [
    "uc_channel_offering_visible_sort_idx",
    "uc_channel_offering_type_visible_idx",
  ];
  for (const indexName of newIndices) {
    try {
      await db.schema.dropIndex(indexName).ifExists().execute();
      console.log(`Dropped new index: ${indexName}`);
    } catch (e) {
      console.warn(`Could not drop index ${indexName}, might not exist:`, e.message);
    }
  }

  // Drop FK constraint if it was added
  // Example (might need adjustment):
  // try {
  //   // SQLite often requires recreating the table to drop FK constraints.
  //   // This is complex. Alternatively, manage constraints manually or omit them.
  //   // If constraints were added via ALTER TABLE, a specific DROP CONSTRAINT might work on some DBs.
  //   // await sql`ALTER TABLE user_cards DROP CONSTRAINT fk_user_channel_id;`.execute(db);
  //   // console.log("Dropped foreign key constraint fk_user_channel_id");
  // } catch (e) {
  //    console.warn("Could not drop foreign key constraint fk_user_channel_id:", e.message);
  // }


  // 2. Alter the table: Drop user_channel_id, Add user_email
  try {
    await db.schema
      .alterTable("user_cards")
      .dropColumn("user_channel_id")
      .execute();
    console.log("Dropped user_channel_id column from user_cards");
  } catch (e) {
     console.warn("Could not drop user_channel_id column, might not exist:", e.message);
  }

  try {
    await db.schema
      .alterTable("user_cards")
      .addColumn("user_email", "text", (col) =>
        col.notNull()
        // Add back reference if it was there originally
        // .references("user.email") // <-- Add this if FK constraint is desired
      )
      .execute();
    console.log("Added user_email column back to user_cards");
  } catch (e) {
    console.error("Failed to add user_email column back:", e.message);
    // This is more critical, might indicate a problem.
  }


  // 3. Recreate original indices using user_email
  try {
    await db.schema.createIndex("uc_user_offering_visible_sort_idx")
      .on("user_cards")
      .columns(["user_email", "coach_offering_id", "is_visible", "sort_order"])
      .execute();
    console.log("Recreated original index: uc_user_offering_visible_sort_idx");
  } catch (e) {
     console.warn("Could not recreate index uc_user_offering_visible_sort_idx:", e.message);
  }

  try {
    await db.schema.createIndex("uc_user_offering_type_visible_idx")
      .on("user_cards")
      .columns(["user_email", "coach_offering_id", "type", "is_visible"])
      .execute();
    console.log("Recreated original index: uc_user_offering_type_visible_idx");
  } catch (e) {
     console.warn("Could not recreate index uc_user_offering_type_visible_idx:", e.message);
  }

  console.log("Finished reverting migration");
}