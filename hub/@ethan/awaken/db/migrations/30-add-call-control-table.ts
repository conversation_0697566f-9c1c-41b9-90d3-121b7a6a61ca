import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  try {
    await db.schema
      .createTable("call_control")
      .ifNotExists() // Prevent error if table somehow exists
      .addColumn("call_id", "text", (col) => col.primaryKey()) // Vapi call IDs are strings (UUIDs)
      .addColumn("control_url", "text", (col) => col.notNull()) // The control URL itself
      .addColumn("channel_id", "text", (col) => col.notNull()) // Associated user channel ID
      .addColumn("created_at", "timestamp", (col) => // Timestamp for tracking/cleanup
        col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
      )
      .execute();
    console.log("Successfully created 'call_control' table.");
  } catch (e: any) {
    // Catch potential errors during table creation, though ifNotExists should handle the common case
    console.error("Error creating 'call_control' table:", e.message);
    // Re-throw if it's not a simple "already exists" scenario, although ifNotExists should prevent that.
    // This basic catch is mostly for logging unexpected issues.
    throw e;
  }
}

export async function down(db: Kysely<any>): Promise<void> {
  try {
    await db.schema.dropTable("call_control").ifExists().execute(); // Use ifExists for safety
    console.log("Successfully dropped 'call_control' table.");
  } catch (e: any) {
    // Catch potential errors during table drop
    console.error("Error dropping 'call_control' table:", e.message);
    throw e; // Re-throw to indicate migration failure
  }
} 