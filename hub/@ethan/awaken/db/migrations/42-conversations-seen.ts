import { <PERSON>ys<PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema.alterTable("conversation").addColumn("seen_by_user", "integer", (col) => col.defaultTo(0)).execute();
  await db.schema.alterTable("conversation").addColumn("seen_by_coach", "integer", (col) => col.defaultTo(0)).execute();

  console.log("Added seen_by_user and seen_by_coach columns to conversation table");
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.alterTable("conversation")
    .dropColumn("seen_by_user")
    .dropColumn("seen_by_coach")
    .execute();
  
  console.log("Dropped seen_by_user and seen_by_coach columns from conversation table");
} 