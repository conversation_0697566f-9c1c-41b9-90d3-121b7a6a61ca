import { Kysely, sql } from "npm:kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("conversation")
    .addColumn("id", "text", (col) => col.primaryKey())
    .addColumn("channel_id", "integer", (col) => col.notNull())
    .addColumn("sender", "text", (col) => col.notNull())
    .addColumn("content", "text", (col) => col.notNull())
    .addColumn("date", "text", (col) => col.notNull())
    .addColumn("status", "text", (col) => col.defaultTo("Default"))
    .addColumn("message_type", "text", (col) => col.defaultTo("message"))
    .addColumn("audio", "text")
    .addColumn("airtable_id", "text")
    .addColumn("created_at", "text", (col) => 
      col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
    )
    .execute();

  // Create separate indexes after table creation
  await db.schema
    .createIndex("conversation_channel_idx")
    .on("conversation")
    .column("channel_id")
    .execute();
    
  await db.schema
    .createIndex("conversation_date_idx")
    .on("conversation")
    .column("date")
    .execute();

  // Add composite index on channelId and date for faster queries
  await db.schema
    .createIndex("conversation_channel_date_idx")
    .on("conversation")
    .columns(["channel_id", "date"])
    .execute();

  // Add index on status for faster starred message queries
  await db.schema
    .createIndex("conversation_status_idx")
    .on("conversation")
    .column("status")
    .execute();

  console.log("Created conversation table and indexes");
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropIndex("conversation_channel_idx").execute();
  await db.schema.dropIndex("conversation_date_idx").execute();
  await db.schema.dropIndex("conversation_channel_date_idx").execute();
  await db.schema.dropIndex("conversation_status_idx").execute();
  await db.schema.dropTable("conversation").execute();
  
  console.log("Dropped conversation table and indexes");
} 