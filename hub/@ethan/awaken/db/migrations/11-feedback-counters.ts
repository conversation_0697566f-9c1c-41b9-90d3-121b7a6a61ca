import { <PERSON>ys<PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  // Add messages_till_feedback column to user table
  await db.schema
    .alterTable("user")
    .addColumn("messages_till_feedback", "integer", (col) => col.defaultTo(4))
    .execute();

  // Add feedback_life column to user table
  await db.schema
    .alterTable("user")
    .addColumn("feedback_life", "integer", (col) => col.defaultTo(2))
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  // Remove messages_till_feedback and feedback_life columns from user table
  const columns = [
    "messages_till_feedback",
    "feedback_life"
  ];

  for (const column of columns) {
    await db.schema.alterTable("user").dropColumn(column).execute();
  }
}