import { Kysely, sql } from "npm:kysely";
import { v4 as uuidv4 } from "npm:uuid";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("coaches")
    .addColumn("id", "text", (col) => col.primaryKey().notNull())
    .addColumn("name", "text", (col) => col.notNull().unique())
    .addColumn("description", "text")
    .addColumn("type", "text")
    .addColumn("default_coach", "boolean", (col) => col.defaultTo(false))
    .addColumn("opening_prompt", "text")
    .addColumn("message_prompt", "text", (col) => col.notNull())
    .addColumn("call_prompt", "text", (col) => col.notNull())
    .addColumn("goal_call_prompt", "text", (col) => col.notNull())
    .addColumn("metadata", "json", (col) => col.notNull().defaultTo("{}"))
    .addColumn("created_at", "timestamp", (col) => col.notNull().defaultTo(sql`CURRENT_TIMESTAMP`))
    .execute();

  // Create index on default_coach for faster queries
  await db.schema
    .createIndex("coaches_default_idx")
    .on("coaches")
    .column("default_coach")
    .execute();

  // Create index on name for faster lookups
  await db.schema
    .createIndex("coaches_name_idx")
    .on("coaches")
    .column("name")
    .execute();

  // Generate a UUID for Kokoro using the same approach as in conversation-db.ts
  const kokoroId = uuidv4();

  // Seed the initial Kokoro coach as the default
  await db.insertInto("coaches")
    .values({
      id: kokoroId,
      name: "Kokoro",
      description: "A wise and intuitive transformational coach.",
      type: "General",
      default_coach: true,
      opening_prompt: "Create an opening message to your client",
      message_prompt: "Coach your client via text messages",
      call_prompt: "Coach your client on a call",
      goal_call_prompt: "Coach your client on a goal-oriented call",
      metadata: JSON.stringify({
        voices: [
          {
            voice_name: "KokoroMale",
            voice_id: "OPp59xqerF9pc477FJM8",
            language: "en-US",
            vendor: "ElevenLabs",
            model_id: "eleven_flash_v2",
            voice_settings: {
              stability: 0.75,
              similarity_boost: 0.9
            }
          }
        ]
      })
    })
    .execute();

  console.log("Created coaches table, indexes, and seeded Kokoro coach");
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropIndex("coaches_name_idx").execute();
  await db.schema.dropIndex("coaches_default_idx").execute();
  await db.schema.dropTable("coaches").execute();
  
  console.log("Dropped coaches table and indexes");
} 