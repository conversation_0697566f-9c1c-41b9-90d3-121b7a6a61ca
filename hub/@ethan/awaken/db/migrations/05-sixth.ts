import { <PERSON><PERSON><PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON><PERSON><PERSON><unknown>): Promise<void> {
    // add onboarding column to user table
    await db.schema
        .alterTable("user")
        .addColumn("onboarding", "boolean")
        .execute();
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
    // remove onboarding column from user table
    await db.schema
        .alterTable("user")
        .dropColumn("onboarding")
        .execute();
}