import { <PERSON><PERSON><PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
    // add image column to user table
    await db.schema
        .alterTable("user")
        .addColumn("image", "text")
        .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
    // remove image column from user table
    await db.schema
        .alterTable("user")
        .dropColumn("image")
        .execute();
}