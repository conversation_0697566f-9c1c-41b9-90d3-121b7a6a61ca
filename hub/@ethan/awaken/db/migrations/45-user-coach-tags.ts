import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Create user_coach_tags table
  await db.schema
    .createTable("user_coach_tags")
    .addColumn("channel_id", "integer", (col) => col.notNull())
    .addColumn("coach_name", "text", (col) => col.notNull())
    .addColumn("tag", "text", (col) => col.notNull())
    .addPrimaryKeyConstraint("user_coach_tags_pkey", ["channel_id", "coach_name", "tag"])
    .addColumn("created_at", "text", (col) => 
      col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
    )
    .execute();

  // Create indexes for efficient tag queries
  await db.schema
    .createIndex("user_coach_tags_channel_coach_idx")
    .on("user_coach_tags")
    .columns(["channel_id", "coach_name"])
    .execute();

  await db.schema
    .createIndex("user_coach_tags_tag_idx")
    .on("user_coach_tags")
    .column("tag")
    .execute();

  console.log("Created user_coach_tags table with indexes");
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropIndex("user_coach_tags_channel_coach_idx").execute();
  await db.schema.dropIndex("user_coach_tags_tag_idx").execute();
  await db.schema.dropTable("user_coach_tags").execute();
  
  console.log("Dropped user_coach_tags table and indexes");
}