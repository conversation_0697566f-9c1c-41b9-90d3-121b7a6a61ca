import { <PERSON><PERSON><PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  // add a new column called first_message of boolean type to the user table
  await db.schema
    .alterTable("user")
    .addColumn("first_message", "boolean")
    .execute();
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema.alterTable("user").dropColumn("first_message").execute();
}
