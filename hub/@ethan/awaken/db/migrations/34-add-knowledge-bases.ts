import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Create knowledge_bases table
  try {
    await db.schema
      .createTable("knowledge_bases")
      .ifNotExists()
      .addColumn("id", "integer", (col) => col.primaryKey().autoIncrement())
      .addColumn("identifier", "text", (col) => col.notNull().unique()) // Unique ID for the KB
      .addColumn("title", "text") // Optional human-readable title
      .addColumn("content", "text", (col) => col.notNull()) // Full KB text
      // Optional FK: .addColumn("coach_id", "integer", (col) => col.references("coaches.id").onDelete("set null"))
      .addColumn("created_at", "text", (col) =>
        col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
      )
      .addColumn("updated_at", "text", (col) =>
        col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
      )
      .execute();
    console.log("Successfully created 'knowledge_bases' table.");
  } catch (e: any) {
    console.error("Error creating 'knowledge_bases' table:", e.message);
    throw e;
  }

  // Create index on identifier
  try {
    await db.schema
      .createIndex("knowledge_bases_identifier_idx")
      .on("knowledge_bases")
      .column("identifier")
      .execute();
    console.log(
      "Successfully created index 'knowledge_bases_identifier_idx' on 'knowledge_bases' table."
    );
  } catch (e: any) {
    // Kysely doesn't have native ifNotExists for index, check error message
    if (e.message?.includes("already exists")) {
      console.log(
        "Index 'knowledge_bases_identifier_idx' already exists. Skipping."
      );
    } else {
      console.error(
        "Error creating index 'knowledge_bases_identifier_idx':",
        e.message
      );
      throw e;
    }
  }

  // Create trigger for updated_at
  try {
    // Note: Raw SQL is often needed for triggers
    await sql`
      CREATE TRIGGER IF NOT EXISTS knowledge_bases_updated_at_trigger
      AFTER UPDATE ON knowledge_bases
      FOR EACH ROW
      BEGIN
        UPDATE knowledge_bases
        SET updated_at = CURRENT_TIMESTAMP
        WHERE id = OLD.id;
      END;
    `.execute(db);
    console.log("Successfully created 'knowledge_bases_updated_at_trigger'.");
  } catch (e: any) {
    console.error(
      "Error creating 'knowledge_bases_updated_at_trigger':",
      e.message
    );
    throw e;
  }
}

export async function down(db: Kysely<any>): Promise<void> {
  // Drop the trigger
  try {
    await sql`DROP TRIGGER IF EXISTS knowledge_bases_updated_at_trigger;`.execute(db);
    console.log("Successfully dropped trigger 'knowledge_bases_updated_at_trigger'.");
  } catch (e: any) {
    console.error(
      "Error dropping trigger 'knowledge_bases_updated_at_trigger':",
      e.message
    );
    throw e;
  }

  // Drop the index
  try {
    // Kysely doesn't have dropIndex(...).ifExists()
    await db.schema.dropIndex("knowledge_bases_identifier_idx").execute();
    console.log("Successfully dropped index 'knowledge_bases_identifier_idx'.");
  } catch (e: any) {
     // Check common error messages for "does not exist" across different SQLite versions/libs
     if (e.message?.includes("no such index") || e.message?.includes("index does not exist")) {
        console.log("Index 'knowledge_bases_identifier_idx' does not exist. Skipping drop.");
    } else {
        console.error("Error dropping index 'knowledge_bases_identifier_idx':", e.message);
        throw e;
    }
  }

  // Drop the table
  try {
    await db.schema.dropTable("knowledge_bases").ifExists().execute();
    console.log("Successfully dropped 'knowledge_bases' table.");
  } catch (e: any) {
    console.error("Error dropping 'knowledge_bases' table:", e.message);
    throw e;
  }
} 