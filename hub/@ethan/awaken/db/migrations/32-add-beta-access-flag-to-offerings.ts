import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  try {
    await db.schema
      .alterTable("coach_card_offerings")
      .addColumn("requires_beta_access", "integer", (col) =>
        col.notNull().defaultTo(0) // Default to false (0)
      )
      .execute();
    console.log(
      "Successfully added 'requires_beta_access' column to 'coach_card_offerings' table."
    );
  } catch (e: any) {
    if (e.message?.includes("duplicate column name")) {
      console.log(
        "'requires_beta_access' column already exists in 'coach_card_offerings'. Skipping."
      );
    } else {
      console.error(
        "Error adding 'requires_beta_access' column to 'coach_card_offerings' table:",
        e.message
      );
      throw e;
    }
  }
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  try {
    // Note: Dropping columns might not be supported in all SQLite versions directly
    // or might have limitations. <PERSON><PERSON><PERSON> attempts the standard ALTER TABLE DROP COLUMN.
    await db.schema
      .alterTable("coach_card_offerings")
      .dropColumn("requires_beta_access")
      .execute();
    console.log(
      "Successfully dropped 'requires_beta_access' column from 'coach_card_offerings' table."
    );
  } catch (e: any) {
    console.error(
      "Error dropping 'requires_beta_access' column from 'coach_card_offerings' table:",
      e.message
    );
    // Consider manual migration steps if DROP COLUMN is not supported in your environment
    throw e;
  }
} 