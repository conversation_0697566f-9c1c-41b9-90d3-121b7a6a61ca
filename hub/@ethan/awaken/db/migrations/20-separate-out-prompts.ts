import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // 1. Create the new `prompts` table with an extra column promptJSON
  try {
    await db.schema
      .createTable("prompts")
      .addColumn("name", "text", (col) => col.primaryKey().notNull())
      .addColumn("description", "text")
      .addColumn("promptText", "text", (col) => col.notNull())
      .addColumn("promptJSON", "text", (col) => col.notNull()) // New column for additional prompt JSON data
      .addColumn("created_at", "timestamp", (col) =>
        col.notNull().defaultTo(sql`CURRENT_TIMESTAMP`)
      )
      .execute();
    console.log("Created prompts table");
  } catch (e) {
    if (e.message.includes("already exists")) {
      console.log("prompts table already exists, skipping creation");
    } else {
      console.error("Error creating prompts table:", e.message);
      throw e;
    }
  }

  // 2. Add new `prompts` column to coaches table (as J<PERSON><PERSON> with default value '{}')
  try {
    await db.schema
      .alterTable("coaches")
      .addColumn("prompts", "json", (col) => col.notNull().defaultTo("{}"))
      .execute();
    console.log("Added prompts column to coaches table");
  } catch (e) {
    if (e.message.includes("duplicate column")) {
      console.log(
        "prompts column already exists in coaches table, skipping creation"
      );
    } else {
      console.error("Error adding prompts column to coaches table:", e.message);
      throw e;
    }
  }

  // 3. Drop the old prompt columns from coaches table (no data migration)
  const columns = [
    "message_prompt",
    "call_prompt",
    "goal_call_prompt",
    "opening_prompt"
  ];

  for (const column of columns) {
    try {
      await db.schema
        .alterTable("coaches")
        .dropColumn(column)
        .execute();
      console.log(`Dropped ${column} column from coaches table`);
    } catch (e) {
      console.log(
        `Failed to drop ${column} column from coaches table, may not exist:`,
        e.message
      );
    }
  }

  console.log("Migration completed for prompts table and coaches table update");
}

export async function down(db: Kysely<any>): Promise<void> {
  // 1. Re-add the old prompt columns to coaches table
  try {
    await db.schema
      .alterTable("coaches")
      .addColumn("message_prompt", "text")
      .addColumn("call_prompt", "text")
      .addColumn("goal_call_prompt", "text")
      .addColumn("opening_prompt", "text")
      .execute();
    console.log("Re-added old prompt columns to coaches table");
  } catch (e) {
    console.error(
      "Error re-adding old prompt columns to coaches table:",
      e.message
    );
    throw e;
  }

  // 2. Drop the new prompts column from coaches table
  try {
    await db.schema.alterTable("coaches").dropColumn("prompts").execute();
    console.log("Dropped prompts column from coaches table");
  } catch (e) {
    console.log(
      "Failed to drop prompts column from coaches table, may not exist:",
      e.message
    );
  }

  // 3. Drop the prompts table
  try {
    await db.schema.dropTable("prompts").execute();
    console.log("Dropped prompts table");
  } catch (e) {
    console.log("Failed to drop prompts table, may not exist:", e.message);
  }

  console.log(
    "Down migration completed for prompts table and coaches table update"
  );
} 