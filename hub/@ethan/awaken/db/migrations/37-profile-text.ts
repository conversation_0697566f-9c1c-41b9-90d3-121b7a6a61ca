import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  await db.schema
    .createTable("profile_text")
    .addColumn("channel_id", "integer", (col) => col.primaryKey())
    .addColumn("content", "text")
    .addColumn("created_at", "timestamp", (col) =>
      col.defaultTo(sql`CURRENT_TIMESTAMP`)
    )
    .execute();

  await db.schema
    .createTable("profile_text_dump")
    .addColumn("channel_id", "integer")
    .addColumn("content", "text")
    .addColumn("created_at", "timestamp", (col) =>
      col.defaultTo(sql`CURRENT_TIMESTAMP`)
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("profile_text").execute();
  await db.schema.dropTable("profile_text_dump").execute();
} 