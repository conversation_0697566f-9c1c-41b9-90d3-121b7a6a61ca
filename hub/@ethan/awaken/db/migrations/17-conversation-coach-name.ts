import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  try {
    // Add coach_name column to conversation table with default value 'Kokoro'
    await db.schema
      .alterTable("conversation")
      .addColumn("coach_name", "text", (col) => 
        col.defaultTo("Kokoro").notNull()
      )
      .execute();
    console.log("Added coach_name column to conversation table");
  } catch (e) {
    // If error contains "duplicate column", the column already exists
    if (e.message.includes("duplicate column")) {
      console.log("coach_name column already exists, skipping creation");
    } else {
      // Otherwise, there's a different problem
      console.error("Error adding column:", e.message);
      throw e;
    }
  }

  // Check if index exists
  try {
    // Try to create the index - will fail if it already exists
    await db.schema
      .createIndex("conversation_coach_name_idx")
      .on("conversation")
      .column("coach_name")
      .execute();
    console.log("Created index on coach_name column");
  } catch (e) {
    // If error contains "already exists", that's fine
    if (e.message.includes("already exists")) {
      console.log("Index already exists, skipping creation");
    } else {
      // Otherwise, there's a different problem
      console.error("Error creating index:", e.message);
      throw e;
    }
  }

  console.log("Migration completed for coach_name column and index");
}

export async function down(db: Kysely<any>): Promise<void> {
  // Try to drop the index
  try {
    await db.schema
      .dropIndex("conversation_coach_name_idx")
      .execute();
    console.log("Dropped index on coach_name column");
  } catch (e) {
    console.log("Failed to drop index, may not exist:", e.message);
  }

  // Try to drop the column
  try {
    await db.schema
      .alterTable("conversation")
      .dropColumn("coach_name")
      .execute();
    console.log("Dropped coach_name column");
  } catch (e) {
    console.log("Failed to drop column, may not exist:", e.message);
  }
} 