import { Kysely, sql } from "npm:kysely";

const createIndex = async (
  db: Kysely<unknown>,
  table: string,
  indices: string[][],
) => {
  for (const index of indices) {
    await db.schema
      .createIndex(`idx_${table}_${index.join("_")}`)
      .on(table)
      .columns(index)
      .execute();
  }
};

export async function up(db: Kysely<unknown>): Promise<void> {
        
    await db.schema
        .createTable("summary")
        .addColumn("id", "uuid", (col) => col.primaryKey().notNull())
        .addColumn("channel_id", "integer", (col) => col.notNull())
        .addColumn("summary_sent", "boolean", (col) => col.notNull())
        .execute();
    await createIndex(db, "summary", [
        ["id"],
    ]);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {}
