import { Kysely, sql } from "npm:kysely";
import { v4 as uuidv4 } from "npm:uuid";

export async function up(db: Kysely<any>): Promise<void> {
  // channel coach primary key pair
  await db.schema
    .createTable("user_coach_attributes")
    .addColumn("channel_id", "integer", (col) => col.notNull())
    .addColumn("coach_name", "text", (col) => col.notNull())
    .addPrimaryKeyConstraint("user_coach_attributes_pkey", ["channel_id", "coach_name"])
    .addColumn("data_sharing_enabled", "boolean", (col) => col.notNull().defaultTo(false))
    .addColumn("coach_notes", "text", (col) => col.notNull().defaultTo(""))
    .execute();

  console.log("Created user_coach_attributes table, indexes, and seeded Kokoro coach");
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema.dropTable("user_coach_attributes").execute();
  console.log("Dropped user_coach_attributes table");
} 