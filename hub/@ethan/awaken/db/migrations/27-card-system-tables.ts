import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Create card_definitions table
  await db.schema
    .createTable("card_definitions")
    .addColumn("id", "text", (col) => col.primaryKey())
    .addColumn("type", "text", (col) => col.notNull().unique())
    .addColumn("default_title", "text", (col) => col.notNull())
    .addColumn("default_description", "text")
    .addColumn("default_ui_config", "json", (col) => col.notNull().defaultTo('{}'))
    .addColumn("is_system_standard", "integer", (col) => col.notNull().defaultTo(1)) // Using integer for boolean
    .addColumn("created_at", "text", (col) =>
      col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
    )
    .addColumn("updated_at", "text", (col) =>
      col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
    )
    .execute();

  // Create coach_card_offerings table
  await db.schema
    .createTable("coach_card_offerings")
    .addColumn("id", "text", (col) => col.primaryKey())
    .addColumn("coach_name", "text", (col) => col.notNull().references("coaches.name"))
    .addColumn("card_definition_id", "text", (col) => col.notNull().references("card_definitions.id"))
    .addColumn("type", "text", (col) => col.notNull()) // Redundant type column
    .addColumn("title_override", "text")
    .addColumn("description_override", "text")
    .addColumn("ui_config_override", "json")
    .addColumn("sort_order", "integer", (col) => col.notNull().defaultTo(0))
    .addColumn("created_at", "text", (col) =>
      col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
    )
    .addColumn("updated_at", "text", (col) =>
      col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
    )
    .execute();

  // Add indices for coach_card_offerings
  // Note: Spec mentioned (coach_name, is_active, sort_order), but is_active is not in the table definition. Creating indices based on defined columns.
  await db.schema.createIndex("cco_coach_sort_idx")
    .on("coach_card_offerings")
    .columns(["coach_name", "sort_order"])
    .execute();
  await db.schema.createIndex("cco_coach_type_idx")
    .on("coach_card_offerings")
    .columns(["coach_name", "type"])
    .execute();
  await db.schema.createIndex("cco_type_idx")
    .on("coach_card_offerings")
    .column("type")
    .execute();


  // Create user_cards table
  await db.schema
    .createTable("user_cards")
    .addColumn("id", "text", (col) => col.primaryKey())
    .addColumn("user_email", "text", (col) => col.notNull().references("user.email"))
    .addColumn("coach_offering_id", "text", (col) => col.notNull().references("coach_card_offerings.id"))
    .addColumn("type", "text", (col) => col.notNull()) // Redundant type column
    .addColumn("title_override", "text")
    .addColumn("description_override", "text")
    .addColumn("data", "json", (col) => col.notNull().defaultTo('{}'))
    .addColumn("status", "text", (col) => col.defaultTo("AVAILABLE"))
    .addColumn("is_visible", "integer", (col) => col.notNull().defaultTo(1)) // Using integer for boolean
    .addColumn("sort_order", "integer", (col) => col.notNull().defaultTo(0))
    .addColumn("last_used_at", "text")
    .addColumn("created_at", "text", (col) =>
      col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
    )
    .addColumn("updated_at", "text", (col) =>
      col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
    )
    .execute();

  // Add indices for user_cards
  await db.schema.createIndex("uc_user_offering_visible_sort_idx")
    .on("user_cards")
    .columns(["user_email", "coach_offering_id", "is_visible", "sort_order"])
    .execute();
  await db.schema.createIndex("uc_user_offering_type_visible_idx")
    .on("user_cards")
    .columns(["user_email", "coach_offering_id", "type", "is_visible"])
    .execute();
  await db.schema.createIndex("uc_type_idx")
    .on("user_cards")
    .column("type")
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  // Drop user_cards indices
  const userCardIndices = [
    "uc_type_idx",
    "uc_user_offering_type_visible_idx",
    "uc_user_offering_visible_sort_idx",
  ];
  for (const indexName of userCardIndices) {
    try {
      await db.schema.dropIndex(indexName).execute();
      console.log(`Dropped user_cards index: ${indexName}`);
    } catch (e) {
      console.log(`Failed to drop user_cards index ${indexName}, may not exist:`, e.message);
    }
  }

  // Drop user_cards table
  try {
    await db.schema.dropTable("user_cards").execute();
    console.log("Dropped user_cards table");
  } catch (e) {
    console.log("Failed to drop user_cards table, may not exist:", e.message);
  }

  // Drop coach_card_offerings indices
  const coachOfferingIndices = [
    "cco_type_idx",
    "cco_coach_type_idx",
    "cco_coach_sort_idx",
  ];
  for (const indexName of coachOfferingIndices) {
    try {
      await db.schema.dropIndex(indexName).execute();
      console.log(`Dropped coach_card_offerings index: ${indexName}`);
    } catch (e) {
      console.log(`Failed to drop coach_card_offerings index ${indexName}, may not exist:`, e.message);
    }
  }

  // Drop coach_card_offerings table
  try {
    await db.schema.dropTable("coach_card_offerings").execute();
    console.log("Dropped coach_card_offerings table");
  } catch (e) {
    console.log("Failed to drop coach_card_offerings table, may not exist:", e.message);
  }

  // Drop card_definitions table
  try {
    await db.schema.dropTable("card_definitions").execute();
    console.log("Dropped card_definitions table");
  } catch (e) {
    console.log("Failed to drop card_definitions table, may not exist:", e.message);
  }

  console.log("Down migration completed for card system tables.");
} 