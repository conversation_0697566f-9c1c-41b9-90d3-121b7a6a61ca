import { Kysely } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  // Add daily tracking columns to user table
  await db.schema
    .alterTable("user")
    .addColumn("call_count_today", "integer", (col) => col.defaultTo(0))
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("call_minutes_today", "decimal", (col) => col.defaultTo(0))
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("message_count_today", "integer", (col) => col.defaultTo(0))
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("last_daily_reset", "timestamp")
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  // Remove daily tracking columns from user table
  const columns = [
    "call_count_today",
    "call_minutes_today", 
    "message_count_today",
    "last_daily_reset"
  ];

  for (const column of columns) {
    await db.schema.alterTable("user").dropColumn(column).execute();
  }
} 