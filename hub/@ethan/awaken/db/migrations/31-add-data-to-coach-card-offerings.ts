import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON><PERSON>ely<any>): Promise<void> {
  try {
    await db.schema
      .alterTable("coach_card_offerings")
      .addColumn("data", "json", (col) => col.notNull().defaultTo('{}')) // Add the data column
      .execute();
    console.log("Successfully added 'data' column to 'coach_card_offerings' table.");
  } catch (e: any) {
    console.error("Error adding 'data' column to 'coach_card_offerings' table:", e.message);
    throw e;
  }
}

export async function down(db: Kysely<any>): Promise<void> {
  try {
    // Note: Dropping columns might not be supported in all SQLite versions directly
    // or might have limitations. This is the standard Kysely way.
    await db.schema
      .alterTable("coach_card_offerings")
      .dropColumn("data")
      .execute();
    console.log("Successfully dropped 'data' column from 'coach_card_offerings' table.");
  } catch (e: any) {
    console.error("Error dropping 'data' column from 'coach_card_offerings' table:", e.message);
    // If altering to drop isn't supported, manual migration steps might be needed
    // for older databases or specific setups (e.g., create new table, copy data, drop old, rename new).
    throw e;
  }
} 