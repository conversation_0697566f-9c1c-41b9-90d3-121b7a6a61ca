import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Add coach_access JSON column to users table with default Kokoro access
  await db.schema
    .alterTable("user")
    .addColumn("coach_access", "json", (col) => 
      col.defaultTo('{"Kokoro": true}')
    )
    .execute();

  // Add coach_access JSON column to subscriptions table with empty default
  await db.schema
    .alterTable("subscription")
    .addColumn("coach_access", "json", (col) => 
      col.defaultTo('{}')
    )
    .execute();

  console.log("Added coach_access JSON columns to users and subscriptions tables");
}

export async function down(db: Kysely<any>): Promise<void> {
  // Remove coach_access column from users table
  await db.schema
    .alterTable("user")
    .dropColumn("coach_access")
    .execute();

  // Remove coach_access column from subscriptions table
  await db.schema
    .alterTable("subscription")
    .dropColumn("coach_access")
    .execute();
  
  console.log("Removed coach_access JSON columns from users and subscriptions tables");
} 