import { <PERSON><PERSON><PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  // Add verified_token column to user table
  await db.schema
    .alterTable("user")
    .addColumn("verified_token", "text")
    .execute();
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Remove verified_token column from user table
  await db.schema
    .alterTable("user")
    .dropColumn("verified_token")
    .execute();
}