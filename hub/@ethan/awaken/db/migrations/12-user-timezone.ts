import { Kysely } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  // Add time zone column to user table
  await db.schema
    .alterTable("user")
    .addColumn("timezone", "varchar(64)", (col) => col.defaultTo("Europe/London"))
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  // Remove time zone column from user table
  await db.schema
    .alterTable("user")
    .dropColumn("timezone")
    .execute();
} 