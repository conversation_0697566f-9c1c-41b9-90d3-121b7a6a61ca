import { Kysely, sql } from "npm:kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Add the requires_user_instance column to card_definitions
  await db.schema
    .alterTable("card_definitions")
    .addColumn("requires_user_instance", "integer", (col) => // Use integer for boolean (0=false, 1=true)
      col.notNull().defaultTo(0) // Default to FALSE
    )
    .execute();

  console.log("Added requires_user_instance column to card_definitions");

  // Update existing definitions based on their type
  // Set TRUE (1) for types that should only appear if a user instance exists
  await db
    .updateTable("card_definitions")
    .set({ requires_user_instance: 1 }) // TRUE
    .where("type", "=", "GUIDED_SESSION") // Add other types here if needed later
    .execute();

  console.log("Set requires_user_instance=TRUE for GUIDED_SESSION");

  // Explicitly set FALSE (0) for types that should appear by default if offered
  await db
    .updateTable("card_definitions")
    .set({ requires_user_instance: 0 }) // FALSE
    .where("type", "in", ["EXPLORE", "GOAL", "CREATE_GUIDED"]) // Add others like REFLECTION if applicable
    .execute();

  console.log("Set requires_user_instance=FALSE for EXPLORE, GOAL, CREATE_GUIDED");
}

export async function down(db: Kysely<any>): Promise<void> {
  // Drop the requires_user_instance column
  try {
    await db.schema.alterTable("card_definitions").dropColumn("requires_user_instance").execute();
    console.log("Dropped requires_user_instance column from card_definitions");
  } catch (e) {
    console.log("Failed to drop requires_user_instance column, may not exist:", e.message);
  }
} 