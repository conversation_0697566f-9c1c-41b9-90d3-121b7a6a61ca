import { <PERSON>ysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
    await db.schema
    .alterTable("user")
    .addColumn("status", "text", (col) => 
      col.defaultTo("active").notNull()
    ).execute();

    await db.schema.alterTable("user").addColumn("agent_name", "text").execute();
    await db.schema.alterTable("user").addColumn("agent_id", "text").execute();
    await db.schema.alterTable("user").addColumn("coach_id", "text").execute();
    await db.schema.alterTable("user").addColumn("coach_name", "text").execute();
    await db.schema.alterTable("user").addColumn("coach_mode", "text").execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema
    .alterTable("user")
    .dropColumn("status")
    .dropColumn("agent_name")
    .dropColumn("agent_id")
    .dropColumn("coach_id")
    .dropColumn("coach_name")
    .dropColumn("coach_mode")
    .execute();
}