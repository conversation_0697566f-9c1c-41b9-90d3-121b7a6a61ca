import { <PERSON><PERSON><PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
    // add name column to user table
    await db.schema
        .alterTable("user")
        .addColumn("name", "text")
        .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
    // remove name column from user table
    await db.schema
        .alterTable("user")
        .dropColumn("name")
        .execute();
}