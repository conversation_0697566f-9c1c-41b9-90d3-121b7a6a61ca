import { <PERSON><PERSON><PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  // Add meta_feedback column to user table
  await db.schema
    .alterTable("user")
    .addColumn("meta_feedback", "text")
    .execute();
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Remove meta_feedback column from user table
  await db.schema
    .alterTable("user")
    .dropColumn("meta_feedback")
    .execute();
} 