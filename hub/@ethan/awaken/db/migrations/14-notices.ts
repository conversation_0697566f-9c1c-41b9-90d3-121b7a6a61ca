import { Kysely, sql } from 'npm:kysely';

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Create the notices table
  await db.schema
    .createTable('notice')
    .addColumn('id', 'text', (col) => col.primaryKey().notNull())
    .addColumn('message', 'text', (col) => col.notNull())
    .addColumn('created_at', 'text', (col) => col.notNull())
    .addColumn('created_by', 'text', (col) => col.notNull())
    .execute();

  // Add lastNoticeSeenAt to user table
  await db.schema
    .alterTable('user')
    .addColumn('last_notice_seen_at', 'text')
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  // Drop the notices table
  await db.schema
    .dropTable('notice')
    .execute();

  // Remove last_notice_seen_at from user table
  await db.schema
    .alterTable('user')
    .dropColumn('last_notice_seen_at')
    .execute();
} 