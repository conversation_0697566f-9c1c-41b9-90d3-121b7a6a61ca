import { Kysely } from "npm:kysely";

export async function up(db: Kysely<unknown>): Promise<void> {
  await db.schema
    .alterTable("user")
    .addColumn("monthly_essence_balance", "real", (col) => 
      col.defaultTo(0).notNull()
    )
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("added_essence_balance", "real", (col) => 
      col.defaultTo(0).notNull()
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  const columns = [
    "monthly_essence_balance",
    "added_essence_balance"
  ];

  for (const column of columns) {
    await db.schema
      .alterTable("user")
      .dropColumn(column)
      .execute();
  }
} 