import { <PERSON>ys<PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
    await db.schema
    .alterTable("user")
    .addColumn("profile_initiated", "boolean", (col) => 
      col.defaultTo(false).notNull()
    )
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("profile_text", "text",)
    .execute();

  await db.schema
    .alterTable("user")
    .addColumn("messages_till_profile_update", "integer", (col) => 
      col.defaultTo(10).notNull()
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema
    .alterTable("user")
    .dropColumn("profile_initiated")
    .dropColumn("profile_text")
    .dropColumn("messages_till_profile_update")
    .execute();
}