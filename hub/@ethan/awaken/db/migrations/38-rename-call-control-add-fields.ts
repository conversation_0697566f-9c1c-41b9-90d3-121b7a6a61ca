import { <PERSON><PERSON><PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  try {
    // First rename the table from call_control to call_details
    await db.schema
      .alterTable("call_control")
      .renameTo("call_details")
      .execute();
    
    console.log("Successfully renamed 'call_control' table to 'call_details'.");
    
    // Add each new column with a separate ALTER TABLE statement
    await db.schema.alterTable("call_details").addColumn("status", "text").execute();
    await db.schema.alterTable("call_details").addColumn("summary", "text").execute();
    await db.schema.alterTable("call_details").addColumn("call_duration", "integer").execute();
    await db.schema.alterTable("call_details").addColumn("cost", "real").execute();
    await db.schema.alterTable("call_details").addColumn("essence_cost", "real").execute();
    await db.schema.alterTable("call_details").addColumn("started_at", "timestamp").execute();
    await db.schema.alterTable("call_details").addColumn("ended_at", "timestamp").execute();
    
    console.log("Successfully added new columns to 'call_details' table.");
  } catch (e: any) {
    console.error("Error altering 'call_control' table:", e.message);
    throw e;
  }
}

export async function down(db: Kysely<any>): Promise<void> {
  try {
    // Remove each column with a separate ALTER TABLE statement
    await db.schema.alterTable("call_details").dropColumn("status").execute();
    await db.schema.alterTable("call_details").dropColumn("summary").execute();
    await db.schema.alterTable("call_details").dropColumn("call_duration").execute();
    await db.schema.alterTable("call_details").dropColumn("cost").execute();
    await db.schema.alterTable("call_details").dropColumn("essence_cost").execute();
    await db.schema.alterTable("call_details").dropColumn("started_at").execute();
    await db.schema.alterTable("call_details").dropColumn("ended_at").execute();
    
    console.log("Successfully removed columns from 'call_details' table.");
    
    // Then rename the table back to call_control
    await db.schema
      .alterTable("call_details")
      .renameTo("call_control")
      .execute();
    
    console.log("Successfully renamed 'call_details' table back to 'call_control'.");
  } catch (e: any) {
    console.error("Error reverting changes to 'call_details' table:", e.message);
    throw e;
  }
}
