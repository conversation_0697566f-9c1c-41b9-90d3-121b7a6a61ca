import { Kysely } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  await db.schema
    .alterTable("user")
    .addColumn("email_daily", "boolean", (col) => col.defaultTo(true))
    .execute();

  console.log("Migration: Added email_daily column to user table (default: true)");
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema
    .alterTable("user")
    .dropColumn("email_daily")
    .execute();

  console.log("Migration: Removed email_daily column from user table");
} 