import { Kysely } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  try {
    await db.schema
      .alterTable("user")
      .addColumn("selected_coach", "text", (col) => col.defaultTo("Kokoro"))
      .execute();

    console.log("Migration: Added selected_coach column to user table (default: Kokoro)");
  } catch (error) {
    console.error("Error adding selected_coach column:", error);
    // Only rethrow if it's not a "column already exists" error
    if (!String(error).includes("already exists")) {
      throw error;
    } else {
      console.log("selected_coach column already exists, skipping");
    }
  }
}

export async function down(db: Kysely<any>): Promise<void> {
  try {
    await db.schema
      .alterTable("user")
      .dropColumn("selected_coach")
      .execute();

    console.log("Migration: Removed selected_coach column from user table");
  } catch (error) {
    console.error("Error dropping selected_coach column:", error);
    throw error;
  }
}
