import { <PERSON><PERSON><PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Add the slug column with a temporary default value
  await db.schema
    .alterTable("coaches")
    .addColumn("email", "text")
    .execute();

  console.log("Added email column to coaches table");
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema
    .alterTable("coaches")
    .dropColumn("email")
    .execute();
    
  console.log("Dropped email column from coaches table");
}
