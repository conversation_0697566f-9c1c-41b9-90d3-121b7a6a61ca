import { <PERSON><PERSON><PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  await db.schema
    .alterTable("user")
    .addColumn("last_daily_awakening_date", "text")
    .execute();

  console.log("Migration: Added last_daily_awakening_date to user table");
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema
    .alterTable("user")
    .dropColumn("last_daily_awakening_date")
    .execute();

  console.log("Migration: Removed last_daily_awakening_date from user table");
} 