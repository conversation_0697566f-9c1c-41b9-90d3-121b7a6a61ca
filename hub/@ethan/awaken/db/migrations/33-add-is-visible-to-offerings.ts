import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON><PERSON>ely<any>): Promise<void> {
  try {
    await db.schema
      .alterTable("coach_card_offerings")
      // Add is_visible column, defaulting to true (1)
      .addColumn("is_visible", "integer", (col) =>
        col.notNull().defaultTo(1)
      )
      .execute();
    console.log(
      "Successfully added 'is_visible' column to 'coach_card_offerings' table."
    );
  } catch (e: any) {
    if (e.message?.includes("duplicate column name")) {
      console.log(
        "'is_visible' column already exists in 'coach_card_offerings'. Skipping."
      );
    } else {
      console.error(
        "Error adding 'is_visible' column to 'coach_card_offerings' table:",
        e.message
      );
      throw e;
    }
  }
}

export async function down(db: Kysely<any>): Promise<void> {
  try {
    await db.schema
      .alterTable("coach_card_offerings")
      .dropColumn("is_visible")
      .execute();
    console.log(
      "Successfully dropped 'is_visible' column from 'coach_card_offerings' table."
    );
  } catch (e: any) {
    console.error(
      "Error dropping 'is_visible' column from 'coach_card_offerings' table:",
      e.message
    );
    // Consider manual migration steps if DROP COLUMN is not supported in your environment
    throw e;
  }
} 