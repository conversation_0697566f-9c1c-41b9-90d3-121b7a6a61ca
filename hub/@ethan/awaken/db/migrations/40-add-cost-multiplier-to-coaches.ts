import { <PERSON>ys<PERSON>, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Add the cost_multiplier column with default value of 1.0
  await db.schema
    .alterTable("coaches")
    .addColumn("cost_multiplier", "real", (col) => col.notNull().defaultTo(1.0))
    .execute();

  console.log("Added cost_multiplier column to coaches table");
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema
    .alterTable("coaches")
    .dropColumn("cost_multiplier")
    .execute();
    
  console.log("Dropped cost_multiplier column from coaches table");
} 