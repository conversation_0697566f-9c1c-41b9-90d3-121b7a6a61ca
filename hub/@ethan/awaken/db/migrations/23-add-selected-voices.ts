import { Kysely } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  try {
    await db.schema
      .alterTable("user")
      .addColumn("selected_voices", "json", (col) => 
        col.defaultTo('{"Kokoro":"KokoroMale"}'))
      .execute();

    console.log("Migration: Added selected_voices column to user table");
  } catch (error) {
    console.error("Error adding selected_voices column:", error);
    // Only rethrow if it's not a "column already exists" error
    if (!String(error).includes("already exists")) {
      throw error;
    } else {
      console.log("selected_voices column already exists, skipping");
    }
  }
}

export async function down(db: Kysely<any>): Promise<void> {
  try {
    await db.schema
      .alterTable("user")
      .dropColumn("selected_voices")
      .execute();

    console.log("Migration: Removed selected_voices column from user table");
  } catch (error) {
    console.error("Error dropping selected_voices column:", error);
    throw error;
  }
} 