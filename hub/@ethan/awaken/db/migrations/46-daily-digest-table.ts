import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Create the daily_digest_job table to track digest emails
  await db.schema
    .createTable("daily_digest_job")
    .addColumn("id", "text", (col) => col.primary<PERSON>ey())
    .addColumn("user_id", "text", (col) => col.notNull())
    .addColumn("digest_date", "text", (col) => col.notNull()) // YYYY-MM-DD format
    .addColumn("sent_at", "timestamp")
    .addColumn("message_count", "integer", (col) => col.notNull().defaultTo(0))
    .addColumn("coach_count", "integer", (col) => col.notNull().defaultTo(0))
    .addColumn("created_at", "timestamp", (col) => col.notNull())
    .execute();

  // Create index on user_id and digest_date for efficient lookups
  await db.schema
    .createIndex("daily_digest_user_date_idx")
    .on("daily_digest_job")
    .columns(["user_id", "digest_date"])
    .execute();

  // Create index on digest_date for cleanup operations
  await db.schema
    .createIndex("daily_digest_date_idx")
    .on("daily_digest_job")
    .column("digest_date")
    .execute();

  console.log("Created daily_digest_job table and indexes");
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropIndex("daily_digest_user_date_idx").execute();
  await db.schema.dropIndex("daily_digest_date_idx").execute();
  await db.schema.dropTable("daily_digest_job").execute();
  
  console.log("Dropped daily_digest_job table and indexes");
}
