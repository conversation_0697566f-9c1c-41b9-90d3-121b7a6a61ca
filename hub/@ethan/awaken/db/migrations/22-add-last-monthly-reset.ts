import { Kysely } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  try {
    await db.schema
    .alterTable("user")
    .addColumn("last_monthly_reset", "timestamp")
    .execute();

    console.log("Migration: Added last_monthly_reset column to user table");
  } catch (error) {
    console.error("Error adding last_monthly_reset column:", error);
    // Only rethrow if it's not a "column already exists" error
    if (!String(error).includes("already exists")) {
      throw error;
    } else {
      console.log("last_monthly_reset column already exists, skipping");
    }
  }
}

export async function down(db: Kysely<any>): Promise<void> {
  try {
    await db.schema
      .alterTable("user")
      .dropColumn("last_monthly_reset")
      .execute();

    console.log("Migration: Removed last_monthly_reset column from user table");
  } catch (error) {
    console.error("Error dropping last_monthly_reset column:", error);
    throw error;
  }
}
