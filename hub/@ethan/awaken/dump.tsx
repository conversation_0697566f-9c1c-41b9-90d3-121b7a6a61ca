// const Circle = memo(
//   ({
//     loading,
//     audio,
//     size = "129.02px",
//     isCall = false,
//     isPlaying,
//     isExpanded,
//     audioRef,
//   }) => {
//     console.log("Circle component rendered", isPlaying);

//     const loadingPulseKeyframes = `
//       @keyframes loadingPulse {
//         0%, 100% { transform: scale(1); opacity: 1; }
//         50% { transform: scale(0.8); opacity: 0.5; }
//       }
//     `;
//     const callPulseKeyframes = `
//       @keyframes callPulse {
//         0%, 100% { transform: scale(1); }
//         50% { transform: scale(1.1); }
//       }
//     `;

//     useEffect(() => {
//       const styleSheet = document.styleSheets[0];
//       if (styleSheet) {
//         try {
//           styleSheet.insertRule(
//             loadingPulseKeyframes,
//             styleSheet.cssRules.length
//           );
//           styleSheet.insertRule(callPulseKeyframes, styleSheet.cssRules.length);
//         } catch (error) {
//           console.error("Error inserting keyframe animations:", error);
//         }
//       }
//     }, []);

//     const loadingPulse = {
//       animation: "loadingPulse 1.5s ease-in-out infinite",
//     };
//     const callPulse = { animation: "callPulse 1.5s ease-in-out infinite" };

//     return (
//       <motion.div
//         style={{
//           ...((loading && loadingPulse) || (isCall && callPulse)),
//           position: "absolute",
//           borderRadius: "50%",
//           background: "linear-gradient(270deg, #000000 2.23%, #ECBC6E 235.71%)",
//           border: "2.3px solid",
//           borderImageSlice: 1,
//           boxShadow: "0px 0px 28.8px 3.46px #FCA311",
//           transform: "translateZ(0)",
//           bottom: isExpanded ? "60%" : "30%",
//           width: isExpanded ? "90px" : size,
//           height: isExpanded ? "90px" : size,
//         }}
//       >
//         {audio && isPlaying ? (
//           <StopIcon
//             className="w-8 h-8 text-white fill-white"
//             style={{
//               position: "absolute",
//               top: "50%",
//               left: "50%",
//               transform: "translate(-50%, -50%)",
//               cursor: "pointer",
//             }}
//           />
//         ) : (
//           audio && (
//             <PlayIcon
//               className="w-8 h-8 text-white fill-white"
//               style={{
//                 position: "absolute",
//                 top: "50%",
//                 left: "50%",
//                 transform: "translate(-50%, -50%)",
//                 cursor: "pointer",
//               }}
//             />
//           )
//         )}
//       </motion.div>
//     );
//   },
//   // Only re-render if loading, isCall, or audioRef actually changes
//   (prevProps, nextProps) =>
//     prevProps.loading === nextProps.loading &&
//     prevProps.isCall === nextProps.isCall &&
//     prevProps.audioRef === nextProps.audioRef
// );