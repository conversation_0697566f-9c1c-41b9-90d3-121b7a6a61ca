"use server";

import { saveMessage } from "../../../actions/db/conversation-actions.ts";

/**
 * Save a user reply inside a coach thread. Stored as `coach_message` with sender `user`.
 */
export const sendUserCoachMessage = async (
  channelId: string | number,
  coachName: string,
  content: string,
) => {
  const trimmed = content.trim();
  if (!trimmed) return null;

  const saved = await saveMessage(
    channelId,
    "user",
    trimmed,
    new Date().toISOString(),
    "Default",
    "coach_message",
    coachName,
  );

  return saved;
};