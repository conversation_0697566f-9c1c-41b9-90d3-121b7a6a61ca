"use client";

import { useEffect, useRef, useState, useMemo, useCallback } from "npm:react@canary";
import { <PERSON><PERSON>, ScrollArea, Text } from "@reframe/ui/main.tsx";
import { CircleXIcon } from "@reframe/icons/circle-x.ts";
import { MessageItem } from "./MessageItem.tsx";
import { markCoachThreadMessagesSeenByUser } from "../../../../actions/db/conversation-actions.ts";
import { sendUserCoachMessage } from "../chat-actions.ts";
import { createPortal } from "npm:react-dom@canary";
import { animated, useTransition } from "npm:@react-spring/web@9.7.3";
import { getHumanCoachName, isAIOnlyCoach, getCoachDisplayName } from "../../components/coach-utils.ts";

interface ThreadProps {
  visible: boolean;
  onClose: () => void;
  messages: any[]; // coach_message only
  initialMessageId: string | null;
  coachName: string;
  channelId: number | string;
}

export const CoachThread: React.FC<ThreadProps> = ({
  visible,
  onClose,
  messages,
  initialMessageId,
  coachName,
  channelId,
}) => {
  // Don't render anything on the server
  if (typeof document === "undefined") return null;

  const [threadMsgs, setThreadMsgs] = useState(messages);

  // Mark thread messages as seen when the thread becomes visible
  useEffect(() => {
    if (visible) {
      const hasUnseen = messages.some(m =>
        m.Sender !== "user" && (m.SeenByUser === 0 || m.SeenByUser === null || m.SeenByUser === undefined)
      );
      if (hasUnseen) {
        markCoachThreadMessagesSeenByUser(channelId, coachName).catch(err => console.error("Failed to mark coach thread messages seen", err));
      }
    }
  }, [visible, messages, channelId, coachName]);
  
  // Update messages only when visible and messages change, preserve during exit animation
  useEffect(() => {
    if (visible && messages.length > 0) {
      setThreadMsgs(messages);
    }
  }, [visible, messages]);
  
  const [input, setInput] = useState("");
  const scrollRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Scroll to the highlighted message when thread opens or messages update
  useEffect(() => {
    if (!scrollRef.current) return;
    const scrollArea = scrollRef.current;

    if(initialMessageId && isHighlightEnabled) {
      // Slight delay ensures elements & animations are ready
      const timer = setTimeout(() => {
        const el = scrollArea.querySelector(`[data-thread-id="${initialMessageId}"]`) as HTMLElement | null;
        if (el) {
          el.scrollIntoView({ block: "center" });
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [initialMessageId, threadMsgs]);

  useEffect(() => {
    if (visible && (!initialMessageId || !isHighlightEnabled) && scrollRef.current) {
      const scrollArea = scrollRef.current;
      // Use requestAnimationFrame to scroll after render
      requestAnimationFrame(() => {
        scrollArea.scrollTop = scrollArea.scrollHeight;
      });
    }
  }, [visible, initialMessageId, threadMsgs])

  // Auto-resize textarea on input change
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const newHeight = Math.min(textarea.scrollHeight, 150); // Max height of 150px
      textarea.style.height = `${newHeight}px`;
    }
  }, [input]);

  // Handle enter key to send message (shift+enter for new line)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Check if device is mobile
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent) || window.innerWidth <= 768;
    
    if (e.key === 'Enter') {
      if (isMobile) {
        // On mobile: Enter = new line, Shift+Enter = send
        if (e.shiftKey) {
          e.preventDefault();
          if (input.trim()) {
            handleSend();
          }
        }
        // Let Enter create new line naturally (don't prevent default)
      } else {
        // On desktop: Enter = send, Shift+Enter = new line
        if (!e.shiftKey) {
          e.preventDefault();
          if (input.trim()) {
            handleSend();
          }
        }
        // Let Shift+Enter create new line naturally
      }
    }
  };

  // Highlight state for initial message highlight ring (no pulse)
  const [highlightActive, setHighlightActive] = useState(!!initialMessageId);
  const [isHighlightEnabled, setIsHighlightEnabled] = useState(true);

  useEffect(() => {
    if (initialMessageId) {
      const t = setTimeout(() => setHighlightActive(false), 2000);
      return () => clearTimeout(t);
    }
  }, [initialMessageId]);

  // Slide-in/out animation with faster speed and minimal bounce
  const slideTransition = useTransition(visible, {
    from: {
      transform: "translateY(100%)",
      opacity: 1,
    },
    enter: {
      transform: "translateY(0%)",
      opacity: 1,
      delay: 80, // 80ms delay for button animation timing
    },
    leave: {
      transform: "translateY(100%)",
      opacity: 1,
      delay: 0, // No delay for exit
    },
    config: {
      mass: 0.9,    // Slightly more mass for stability
      tension: 350, // Slightly reduced to minimize bounce
      friction: 35, // Increased friction for much less bounce
      clamp: true,  // Prevent overshoot/bounce
    },
  });

  const handleSend = async () => {
    const trimmed = input.trim();
    if (!trimmed) return;

    if (isHighlightEnabled) {
      setIsHighlightEnabled(false);
    }

    try {
      const saved = await sendUserCoachMessage(channelId, coachName, trimmed);
      if (saved) {
        setThreadMsgs((prev) => [...prev, {
          Id: saved.id,
          Content: trimmed,
          Date: new Date().toISOString(),
          Sender: "user",
          Type: "coach_message",
          CoachName: coachName,
        }]);
        setInput("");
        requestAnimationFrame(() => {
          if (scrollRef.current) {
            scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
          }
        });
      }
    } catch (e) {
      console.error("sendUserCoachMessage error", e);
    }
  };

  // Memoize dummy functions outside the render loop
  const dummyToggle = useCallback(() => {}, []);
  const noop = useCallback(() => {}, []);

  // Wrap the mapping in useMemo to prevent recreating the list JSX when unrelated state changes
  const messageList = useMemo(() => {
    return threadMsgs.map((m, idx) => {
      const isUser = m.Sender === "user";
      const isHighlight = highlightActive && isHighlightEnabled && m.Id === initialMessageId;
      
      return (
        <div
          key={m.Id}
          data-thread-id={m.Id}
          className={`flex flex-col w-full ${isUser ? "items-end" : "items-start"} mb-3`}
        >
          <div className="w-full max-w-[90%]">
            <MessageItem
              message={m}
              index={idx}
              allMessages={threadMsgs}
              contentReady={true}
              currentLetterIndex={0}
              activeAudioId={0}
              setActiveAudioId={noop}
              toggleStar={dummyToggle}
              showCoachPill={false}
              highlight={isHighlight}
              showHumanCoachName={true}
            />
          </div>
        </div>
      );
    });
  }, [threadMsgs, highlightActive, isHighlightEnabled, initialMessageId]);

  const overlay = slideTransition((style, item) =>
    item ? (
      <animated.div
        style={{
          ...style,
          willChange: 'transform',
        }}
        className="fixed inset-x-0 bottom-0 h-[75vh] bg-black/90 backdrop-blur-md border-t border-white/10 rounded-t-3xl flex flex-col z-50"
      >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10 max-w-3xl mx-auto w-full">
        <Text className="text-white font-semibold">From {isAIOnlyCoach(coachName) ? "Awaken's creators" : `the actual ${getHumanCoachName(coachName)}`}</Text>
        <Button
          variant="ghost"
          css="p-2 hover:bg-white/10 rounded-lg"
          onClick={onClose}
        >
          <CircleXIcon className="w-5 h-5 text-white" />
        </Button>
      </div>

      {/* Body */}
      <ScrollArea
        className="flex-1 overflow-y-auto p-4 space-y-4 w-full max-w-3xl mx-auto"
        ref={scrollRef}
      >
        {messageList}
      </ScrollArea>

      {/* Input */}
      <div className="border-t border-white/10 p-4 flex gap-3 max-w-3xl mx-auto w-full">
        <textarea
          ref={textareaRef}
          className="flex-1 bg-white/10 text-white placeholder-gray-400 border border-white/20 rounded-lg px-4 py-2 focus:outline-none focus:border-orange-500 focus:ring-1 focus:ring-orange-500 resize-none overflow-y-auto"
          placeholder={`Reply to ${getCoachDisplayName(coachName)}...`}
          rows={1}
          style={{ 
            minHeight: "48px",
            maxHeight: "150px",
          }}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
        />
        <Button
          onClick={handleSend}
          css={`${
            input.trim() === "" ? "bg-white/10 text-gray-400 cursor-not-allowed" : "bg-orange-600 hover:bg-orange-700 text-white"} px-4 py-2 rounded-lg`}
          disabled={input.trim() === ""}
        >
          Send
        </Button>
      </div>
      </animated.div>
    ) : null
  );

  return createPortal(overlay, document.body);
}; 