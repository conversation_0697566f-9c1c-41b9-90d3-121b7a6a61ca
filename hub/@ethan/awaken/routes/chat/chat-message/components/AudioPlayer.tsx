"use client";

import { useEffect, useRef, useState } from "npm:react@canary";
import { But<PERSON> } from "@reframe/ui/main.tsx";
import { PlayIcon } from "@reframe/icons/play.ts";
import { PauseIcon } from "@reframe/icons/pause-icon.ts";

export interface AudioPlayerProps {
  audio: string;
  index: number;
  activeAudioId: number | null;
  setActiveAudioId: (id: number | null) => void;
}

// Re-export the shared hook so that parent modules can keep importing
// it from this file without change. The actual implementation now
// lives in `routes/chat/hooks/useAudioPlayer.tsx`.
export { useAudioPlayer } from "../../../../lib/hooks/useAudioPlayer.tsx";

/**
 * AudioPlayer component with lazy initialization and proper cleanup
 */
export const AudioPlayer = ({ 
  audio, 
  index, 
  activeAudioId, 
  setActiveAudioId 
}: AudioPlayerProps) => {
  // Track local audio progress
  const [progress, setProgress] = useState<number>(0);
  // We'll only track whether this specific player SHOULD be playing
  const isActive = activeAudioId === index;
  // Reference to functions for cleanup
  const cleanupFnsRef = useRef<{updateFn?: () => void, endFn?: () => void}>({});
  // Reference to the audio element created on demand
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  // Track if audio is actually playing (not just active)  
  const [isPlaying, setIsPlaying] = useState(false);
  // Reference to track any created object URL (for cleanup)
  const objectUrlRef = useRef<string | null>(null);

  // Create or destroy audio element based on active state
  useEffect(() => {
    // Early exit if no audio data
    if (!audio || (typeof audio === 'string' && audio.length === 0)) return undefined;
    
    // Only create audio when this player becomes active
    if (isActive && !audioElementRef.current) {
      // Create audio element but don't auto-play
      // Initialize audio element variable
      const audioElement = new Audio();
    
    
      // Handle regular string URL
      audioElement.src = audio;
      
      // Add error handler for string URLs too
      audioElement.addEventListener('error', (e) => {
        console.error("Error loading audio from URL:", e, audioElement.error);
      }, { once: true });
      
      // Log successful load
      audioElement.addEventListener('canplay', () => {
        console.log("String URL audio loaded successfully");
      }, { once: true });
      
      // Configure the audio element
      audioElement.autoplay = false; // Disable autoplay
      audioElementRef.current = audioElement; // Store the element reference
      
      // Define event handlers
      const updateProgress = () => {
        if (audioElement) {
          const { currentTime, duration } = audioElement;
          if (duration > 0) {
            setProgress((currentTime / duration) * 100);
          }
        }
      };
      
      const handleEnded = () => {
        setProgress(0);
        setIsPlaying(false);
        setActiveAudioId(null);
      };
      
      // Store handlers for later cleanup
      cleanupFnsRef.current.updateFn = updateProgress;
      cleanupFnsRef.current.endFn = handleEnded;
      
      // Add event listeners
      audioElement.addEventListener('timeupdate', updateProgress);
      audioElement.addEventListener('ended', handleEnded);
      audioElement.addEventListener('pause', () => setIsPlaying(false));
      audioElement.addEventListener('play', () => setIsPlaying(true));
      
      // Add debugging for load events
      audioElement.addEventListener('canplay', () => console.log('Audio can play now'));
      audioElement.addEventListener('error', (e) => console.error('Audio error:', e, audioElement.error));
    } 
    // If this player is no longer active, pause and cleanup
    else if (!isActive && audioElementRef.current) {
      try {
        const elem = audioElementRef.current;
        // Only pause if actually playing
        if (!elem.paused) {
          elem.pause();
        }
      } catch (error) {
        console.error('Error pausing audio on deactivation:', error);
      }
    }
    
    // Cleanup function for unmounting or status change
    return () => {
      // Remove event listeners and pause audio
      if (audioElementRef.current) {
        try {
          const elem = audioElementRef.current;
          const { updateFn, endFn } = cleanupFnsRef.current;
          
          if (updateFn) {
            elem.removeEventListener('timeupdate', updateFn);
          }
          
          if (endFn) {
            elem.removeEventListener('ended', endFn);
          }
          
          elem.removeEventListener('pause', () => setIsPlaying(false));
          elem.removeEventListener('play', () => setIsPlaying(true));
          elem.removeEventListener('canplay', () => console.log('Audio can play now'));
          elem.removeEventListener('error', (e) => console.error('Audio error:', e, elem.error));
          
          // Don't pause if we're staying active (prevents AbortError)
          if (!isActive) {
            elem.pause();
          }
        } catch (error) {
          console.error('Error in audio cleanup:', error);
        }
      }
      
      // Revoke any created object URL to prevent memory leaks
      if (objectUrlRef.current) {
        URL.revokeObjectURL(objectUrlRef.current);
        objectUrlRef.current = null;
      }
    };
  }, [isActive, audio, setActiveAudioId]);
  
  // Manual play/pause handler - only attempt to play when user clicks
  const handlePlayPause = () => {
    if (isActive && audioElementRef.current) {
      // This player is already active
      try {
        const audioElem = audioElementRef.current;
        
        if (audioElem.paused) {
          // User wants to play - use an async function to handle the promise
          (async () => {
            try {
              await audioElem.play();
              setIsPlaying(true);
            } catch (error) {
              console.error('Play error:', error);
              setIsPlaying(false);
            }
          })();
        } else {
          // User wants to pause
          audioElem.pause();
          setIsPlaying(false);
          setActiveAudioId(null);
        }
      } catch (error) {
        console.error('Toggle playback error:', error);
        setIsPlaying(false);
      }
    } else {
      // This audio isn't active yet - make it active but don't autoplay
      setProgress(0);
      setActiveAudioId(index);
      // Let the useEffect handle creating the audio - we'll play manually
      setTimeout(() => {
        if (audioElementRef.current) {
          (async () => {
            try {
              await audioElementRef.current.play();
              setIsPlaying(true);
            } catch (error) {
              console.error('Delayed play error:', error);
              setIsPlaying(false);
            }
          })();
        }
      }, 50); // Small delay to ensure audio element is created
    }
  };

  // Only render the player if audio URL exists
  if (!audio) return null;
  
  return (
    <>
      <div className="flex items-center mb-2"> 
        <Button
          variant="outline"
          css="w-8 h-8 p-1 rounded-full border border-[#505050] bg-[#272727] hover:bg-[#333333] hover:border-[#FCA311] flex items-center justify-center"
          onClick={handlePlayPause}
        >
          {isPlaying ? (
            <PauseIcon className="w-4 h-4 text-[#FCA311]" />
          ) : (
            <PlayIcon className="w-4 h-4 text-[#FCA311]" />
          )}
        </Button>
        <div className="ml-2 text-xs text-[#fcb645]">
          {isPlaying ? "Playing" : "Play audio"}
        </div>
      </div>

      {/* Audio progress bar - only show when actively playing */}
      {isActive && (
        <div className="mb-2 h-1 w-full bg-[#505050] rounded-full overflow-hidden"> 
          <div
            className="h-full bg-[#FCA311]"
            style={{ width: `${progress}%` }}
          />
        </div>
      )}
    </>
  );
};
