"use client";

import React, { useRef, useEffect } from "npm:react@canary";
import { Button } from "@reframe/ui/main.tsx";
import { useVoiceRecorder } from "../../../../lib/hooks/useVoiceRecorder.tsx";
import { AudioWaveform } from "../../../../lib/audio-waveform.tsx";

interface VoiceInputProps {
  channelId: string;
  isRecordingMode: boolean;
  setIsRecordingMode: (isRecordingMode: boolean) => void;
  onRecordingComplete: (audioBlob: Blob) => void;
}

export const VoiceInput = ({
  channelId,
  isRecordingMode,
  setIsRecordingMode,
  onRecordingComplete,
}: VoiceInputProps) => {
  // Initialize voice recorder hook
  const {
    audioRef,
    isRecording,
    recordingTime,
    analyser,
    audioBlob,
    isPlaying,
    playbackProgress,
    audioDuration,
    isLoading,
    formatTime,
    startRecording,
    stopRecording,
    cancelRecording,
    submitRecording,
    togglePlayback,
  } = useVoiceRecorder({
    channelId,
    onRecordingComplete: (audioBlob: Blob) => {
      if (audioBlob) {
        onRecordingComplete(audioBlob);
        setIsRecordingMode(false);
      }
    },
  });
  
  console.log("VoiceInput render, isPlaying:", isPlaying);
  
  // Auto-start recording when entering recording mode
  useEffect(() => {
    if (isRecordingMode) {
      // Small delay to ensure component is fully mounted
      const timer = setTimeout(() => {
        startRecording();
      }, 10);
      return () => clearTimeout(timer);
    }
  }, [isRecordingMode]);

  // Render either recording UI or voice input button
  if (isRecordingMode) {
    return (
      <div 
        className="flex-1 bg-[#95959526] text-white text-xs md:text-sm border border-[#FCA311] border-opacity-30 rounded-md px-4 py-3 flex items-center justify-between"
        style={{ 
          minHeight: "48px",
          maxHeight: "96px",
        }}
      >
        {isRecording ? (
          <>
            <div className="flex items-center gap-2 w-full">
              <div className="flex-1 relative">
                <AudioWaveform analyser={analyser} isRecording={isRecording} />
                <div className="absolute top-0 right-0 bg-[#1E1E1E] px-2 py-1 rounded-md text-xs">
                  {formatTime(recordingTime)}
                </div>
              </div>
              <Button
                area-label="stop-recording"
                variant="outline"
                css="h-[36px] w-[36px] rounded-md border-none bg-[#95959526] hover:bg-[#5C3B0633] hover:border-[#FCA311] flex items-center justify-center"
                onClick={stopRecording}
              >
                <span className="w-5 h-5 text-[#FCA311]">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="6" y="6" width="12" height="12" rx="2" fill="currentColor" />
                  </svg>
                </span>
              </Button>
            </div>
          </>
        ) : audioBlob ? (
          <>
            <div className="flex items-center gap-2 w-full">
              {/* Left side: Playback button and progress bar */}
              <div className="flex items-center gap-2 flex-1">
                <Button
                  area-label={isPlaying ? "pause-recording" : "play-recording"}
                  variant="outline"
                  css="h-[36px] w-[36px] rounded-full border-none bg-[#95959526] hover:bg-[#5C3B0633] hover:border-[#FCA311] flex items-center justify-center flex-shrink-0"
                  onClick={() => {
                    console.log("Toggle playback clicked");
                    togglePlayback();
                  }}
                >
                  <span className="w-5 h-5 text-[#FCA311]">
                    {isPlaying ? (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="6" y="5" width="4" height="14" rx="1" fill="currentColor" />
                        <rect x="14" y="5" width="4" height="14" rx="1" fill="currentColor" />
                      </svg>
                    ) : (
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 4L18 12L6 20V4Z" fill="currentColor" />
                      </svg>
                    )}
                  </span>
                </Button>
                
                {/* Progress bar */}
                <div className="flex-grow h-1.5 bg-[#FCA311]/20 rounded-full overflow-hidden relative">
                  <div
                    className="absolute top-0 left-0 h-full bg-gradient-to-r from-amber-400 to-amber-500 rounded-full transition-width duration-75 ease-linear"
                    style={{ width: `${playbackProgress}%` }}
                  />
                </div>
                
                {/* Duration */}
                <span className="text-neutral-400 font-mono text-xs tabular-nums w-12 text-right flex-shrink-0">
                  {formatTime(audioDuration)}
                </span>
              </div>
              
              {/* Right side: Action buttons */}
              <div className="flex gap-2 ml-2 flex-shrink-0">
                <Button
                  area-label="cancel-recording"
                  variant="outline"
                  css="h-[36px] w-[36px] rounded-md border-none bg-[#95959526] hover:bg-[#5C3B0633] hover:border-[#FCA311] flex items-center justify-center"
                  onClick={() => {
                    cancelRecording();
                    setIsRecordingMode(false);
                  }}
                >
                  <span className="w-5 h-5 text-white">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" fill="none" />
                      <path d="M15 9L9 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                      <path d="M9 9L15 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                    </svg>
                  </span>
                </Button>
                <Button
                  area-label="send-recording"
                  variant="outline"
                  css="h-[36px] w-[36px] rounded-md border-none bg-[#95959526] hover:bg-[#5C3B0633] hover:border-[#FCA311] flex items-center justify-center"
                  onClick={submitRecording}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="w-5 h-5 text-[#FCA311]">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="animate-spin">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" strokeOpacity="0.25" />
                        <path d="M12 2C6.47715 2 2 6.47715 2 12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                      </svg>
                    </span>
                  ) : (
                    <span className="w-5 h-5 text-[#FCA311] fill-[#FCA311]">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22 2L11 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    </span>
                  )}
                </Button>
              </div>
            </div>
          </>
        ) : (
          // Show a brief loading indicator while recording is starting
          <div className="flex items-center justify-center w-full">
            <div className="text-[#FCA311] opacity-0 animate-[fadeIn_0.5s_ease-in-out_forwards]">Starting recording...</div>
          </div>
        )}
        <audio ref={audioRef} style={{ display: 'none' }} />
      </div>
    );
  } else {
    return (
      <Button
        area-label="voice-record"
        variant="outline"
        css="h-[48px] w-[48px] rounded-md border-none bg-[#95959526] hover:bg-[#5C3B0633] hover:border-[#FCA311] flex items-center justify-center"
        onClick={() => {
          setIsRecordingMode(true);
          // Recording will auto-start via the useEffect
        }}
      >
        <span className="w-6 h-6 text-[#FCA311]">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 1C11.2044 1 10.4413 1.31607 9.87868 1.87868C9.31607 2.44129 9 3.20435 9 4V12C9 12.7956 9.31607 13.5587 9.87868 14.1213C10.4413 14.6839 11.2044 15 12 15C12.7956 15 13.5587 14.6839 14.1213 14.1213C14.6839 13.5587 15 12.7956 15 12V4C15 3.20435 14.6839 2.44129 14.1213 1.87868C13.5587 1.31607 12.7956 1 12 1Z" stroke="currentColor" strokeWidth="1.5" fill="none" />
            <path d="M19 10V12C19 13.8565 18.2625 15.637 16.9497 16.9497C15.637 18.2625 13.8565 19 12 19C10.1435 19 8.36301 18.2625 7.05025 16.9497C5.7375 15.637 5 13.8565 5 12V10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" fill="none" />
            <path d="M12 19V23" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8 23H16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </span>
      </Button>
    );
  }
};
