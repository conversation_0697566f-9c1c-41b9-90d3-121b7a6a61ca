"use client";

import React, { useRef, useEffect, useState } from "npm:react@canary";
import { Button } from "@reframe/ui/main.tsx";
import { animated, useTransition } from "npm:@react-spring/web";
import { VoiceInput } from "./VoiceInput.tsx";

export const ThinkingDots = () => {
  return (
    <div className="thinking-dots-container">
      <div className="flex items-center justify-center mt-5">
        <div className="thinking-dot thinking-dot-1"></div>
        <div className="thinking-dot thinking-dot-2"></div>
        <div className="thinking-dot thinking-dot-3"></div>
      </div>
    </div>
  );
};

interface SendButtonProps {
  onClick: (e: React.MouseEvent) => void;
  disabled?: boolean;
}

export const SendButton = ({ onClick, disabled = false }: SendButtonProps) => (
  <Button
    area-label="send"
    variant="outline"
    css="h-[48px] w-[48px] rounded-md border-none bg-[#95959526] hover:bg-[#5C3B0633] hover:border-[#FCA311] flex items-center justify-center"
    onClick={onClick}
    onMouseDown={(e) => e.preventDefault()}
    disabled={disabled}
  >
    <span className="w-6 h-6 text-[#FCA311] fill-[#FCA311]">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M22 2L11 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    </span>
  </Button>
);

interface ChatInputProps {
  loading: boolean;
  input: string;
  setInput: (input: string) => void;
  sendMessage: (input: string | Blob) => void;
  placeholder?: string;
  channelId: string;
  showOptionsButton?: boolean;
  onOptionsClick?: () => void;
}

export const ChatInput = ({
  loading,
  input,
  setInput,
  sendMessage,
  placeholder = "Type a message...",
  channelId,
  showOptionsButton = false,
  onOptionsClick,
}: ChatInputProps) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isTextInputFocused, setIsTextInputFocused] = useState(false);
  const [isRecordingMode, setIsRecordingMode] = useState(false);

  // Auto-resize textarea on input change
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea && !isRecordingMode) {
      textarea.style.height = 'auto';
      const newHeight = Math.min(textarea.scrollHeight, 150); // Max height of 150px
      textarea.style.height = `${newHeight}px`;
    }
  }, [input, isRecordingMode]);

  // Handle enter key to send message (shift+enter for new line)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Check if device is mobile
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent) || window.innerWidth <= 768;
    
    if (e.key === 'Enter') {
      if (isMobile) {
        // On mobile: Enter = new line, Shift+Enter = send
        if (e.shiftKey) {
          e.preventDefault();
          if (!loading && input.trim()) {
            sendMessage(input.trim());
            setInput("");
          }
        }
        // Let Enter create new line naturally (don't prevent default)
      } else {
        // On desktop: Enter = send, Shift+Enter = new line
        if (!e.shiftKey) {
          e.preventDefault();
          if (!loading && input.trim()) {
            sendMessage(input.trim());
            setInput("");
          }
        }
        // Let Shift+Enter create new line naturally
      }
    }
  };

  const thinkingTransition = useTransition(loading, {
    from: { opacity: 0 },
    enter: { opacity: 1 },
    leave: { opacity: 0 },
    config: { 
      duration: 150, // Reduce exit duration from 300ms to 150ms for faster disappearance
      tension: 300, // Higher tension for snappier animation
      friction: 20 // Lower friction for quicker movement
    },
    // Add an immediate flag to force completion of exit animation before new content renders
    immediate: (prevLoading: boolean) => prevLoading && !loading,
  });

  // Default input transition
  const defaultInputTransition = useTransition(!loading, {
    from: { opacity: 0 },
    enter: { opacity: 1 },
    leave: { opacity: 0 },
    config: { duration: 0 }
  });
  
  // Reset text input focus when exiting recording mode to ensure mic icon shows
  useEffect(() => {
    if (!isRecordingMode) {
      setIsTextInputFocused(false);
    }
  }, [isRecordingMode]);

  // Clear focus state after processing message to ensure mic icon shows
  useEffect(() => {
    if (!loading) {
      setIsTextInputFocused(false);
    }
  }, [loading]);

  return (
    <div className="relative w-full">
      <div className="w-full mx-auto flex flex-col relative">
          {/* Add separator before input section - moved inside the max-width container */}
          <div className="message-separator -mt-3 mb-3" />

          {/* Input section - Updated positioning */}
          <div className="w-full rounded-lg z-20 flex-shrink-0 input-section">
            <div className="flex justify-between relative pb-2">
              {/* Wrap the thinking animation in a fixed position container to prevent layout shifts */}
              <div className="w-full relative min-h-[48px]">
                {thinkingTransition((style, item) =>
                  item ? (
                    <animated.div 
                      style={{
                        opacity: style.opacity,
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        zIndex: 5
                      }} 
                      className="w-full"
                    >
                      <ThinkingDots />
                    </animated.div>
                  ) : null
                )}

                {!loading && (
                  <>
                    {defaultInputTransition((style, item) =>
                      item ? (
                        <animated.div style={{opacity: style.opacity}} className="flex items-center gap-2 w-full">
                          {/* Options Button */}
                          {showOptionsButton && (
                            <Button
                              variant="ghost"
                              onClick={onOptionsClick}
                              css="p-2 text-[#FCA311] hover:text-[#E59100] flex-shrink-0 h-12 w-12 flex items-center justify-center"
                            >
                              <div className="flex flex-col gap-1">
                                <div className="w-4 h-0.5 bg-[#FCA311]"></div>
                                <div className="w-4 h-0.5 bg-[#FCA311]"></div>
                                <div className="w-4 h-0.5 bg-[#FCA311]"></div>
                              </div>
                            </Button>
                          )}
                          {isRecordingMode ? (
                            <VoiceInput 
                              channelId={channelId}
                              isRecordingMode={isRecordingMode}
                              setIsRecordingMode={setIsRecordingMode}
                              onRecordingComplete={sendMessage}
                            />
                          ) : (
                            <textarea
                              ref={textareaRef}
                              className="flex-1 bg-[#95959526] text-white text-xs md:text-sm border border-[#FCA311] border-opacity-30 rounded-md px-4 py-3 focus:border-2 focus:border-[#FCA311] focus:border-opacity-80 focus:ring-0 resize-none overflow-y-auto"
                              placeholder={placeholder}
                              rows={1}
                              style={{ 
                                minHeight: "48px",
                                maxHeight: "96px",
                              }}
                              onBlur={(e) => {
                                if (
                                  !e.relatedTarget ||
                                  e.relatedTarget.getAttribute("aria-label") !== "send"
                                ) {
                                  setIsTextInputFocused(false);
                                }
                              }}
                              onFocus={() => setIsTextInputFocused(true)}
                              value={input}
                              onChange={(e) => setInput(e.target.value)}
                              onKeyDown={handleKeyDown}
                            />
                          )}
                          {isTextInputFocused ? (
                            <SendButton onClick={(e) => {
                              if (!loading && input.trim()) {
                                sendMessage(input.trim());
                                setInput("");
                              }
                            }} />
                          ) : !isRecordingMode ? (
                            <VoiceInput 
                              channelId={channelId}
                              isRecordingMode={isRecordingMode}
                              setIsRecordingMode={setIsRecordingMode}
                              onRecordingComplete={sendMessage}
                            />
                          ) : null}
                        </animated.div>
                      ) : null
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
    </div>
  );
};
