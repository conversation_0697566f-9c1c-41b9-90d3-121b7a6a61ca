"use client";

import { <PERSON><PERSON>, Image, Text, X, Y } from "@reframe/ui/main.tsx";

import { motion, AnimatePresence } from "npm:framer-motion";
import { PlayIcon } from "@reframe/icons/play.ts";
import { useEffect, useRef, useState } from "npm:react@canary";
import React from "npm:react@canary";
import { Logo } from "../../../../lib/logo.tsx";
import { Music2Icon } from "@reframe/icons/music.ts";
import { MessageSquareIcon } from "@reframe/icons/messages-square.ts";
import { LockKeyholeIcon } from "@reframe/icons/lock-keyhole.ts";

export const WelcomeMessageComponent = (
  { initiate }: { initiate: () => void },
) => {
  return (
    <div className="w-full relative min-h-svh max-h-svh bg-black overflow-hidden root-component touch-none">
      <motion.div
        key={"powerfulMessage"}
        className="relative flex flex-col min-h-svh text-orange-300 p-6 component-b items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 1 }}
      >
        {/* Upper circle */}
        <motion.div
          className="absolute w-[350px] h-[350px] md:w-[506px] md:h-[506px] rounded-full blur-[100px] md:blur-[150px]"
          style={{
            top: "-30%", // Adjusted for mobile
            background:
              "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
          }}
          initial={{ opacity: 0.1 }}
          animate={{ opacity: 0.6 }}
          transition={{ duration: 0 }}
        />

        {/* Bottom "house shape" */}
        <motion.div
          className="absolute bottom-0 w-full h-[400px] md:h-[500px] pointer-events-none"
          initial={{ opacity: 0.1 }}
          animate={{ opacity: 0.6 }}
          transition={{ duration: 0 }}
          style={{
            top: "75%",
            width: "100%", // Make it full width on mobile
            maxWidth: "1030px",
            filter: "blur(100px)",
          }}
        >
          <div
            className="absolute"
            style={{
              width: "100%",
              maxWidth: "1030px",
              height: "100%",
              background:
                "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
              clipPath: "polygon(0% 20%, 50% 0%, 100% 20%, 100% 100%, 0% 100%)",
              left: "50%",
              transform: "translateX(-50%)",
            }}
          />
        </motion.div>

        <div className="flex flex-col justify-between md:justify-center items-center gap-4 md:gap-12 w-full min-h-svh md:min-h-0">
          {/* Top section - logo and title */}
          <div className="flex flex-col items-center mt-20 md:mt-0 gap-4">
            <Logo size={105} />
            <div className="flex flex-col items-center">
              <span className="text-white text-sm md:text-base">
                Welcome to
              </span>
              <Text css="text-[56px] md:text-[56px] font-normal text-[#FCA311]">
                awaken
              </Text>
            </div>
          </div>

          {/* Bottom section - features and button */}
          <div className="flex flex-col items-center gap-4 mb-10 md:mb-0">
            <div className="flex gap-2">
              <Music2Icon size={24} color="#FCA311" />
              <span className="text-white text-sm md:text-base">
                Awaken is an audio-first app
              </span>
            </div>
            <div className="flex gap-2">
              <MessageSquareIcon size={24} color="#FCA311" />
              <span className="text-white text-sm md:text-base">
                Be honest, go deeper, and share specifics
              </span>
            </div>
            <div className="flex gap-2">
              <LockKeyholeIcon size={24} color="#FCA311" />
              <span className="text-white text-sm md:text-base">
                Messages are private and encrypted
              </span>
            </div>
            <div className="flex w-full justify-center mt-2">
              <Button
                variant="outline"
                css="w-full px-6 py-2 bg-gradient-to-r from-[#00000033] to-[#5C3B0633] border-[#FCA3114D] hover:border-[#FCA311] text-white rounded-full flex items-center space-x-2 transition-colors z-10 hover:bg-[#5C3B06] hover:border-[#FCA311]"
                onClick={() => initiate()}
              >
                <span className="text-[#FCA311] font-light text-base">
                  Begin my journey
                </span>
              </Button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
