"use client";

import React from "npm:react@canary";
import { animated } from "npm:@react-spring/web";
import { useTransition } from "npm:@react-spring/web";
import { But<PERSON>, ScrollArea, Text } from "@reframe/ui/main.tsx";
import { Circle } from "../../components/Circle.tsx";
import { FireSvg } from "../../components/FireSvg.tsx";
import { CoachSelector } from "../../components/CoachSelector.tsx";
import { AudioLines } from "@reframe/icons/audio-lines.ts";
import { SquareMessageIcon } from "../../../../lib/icons.tsx";

// Utility to get coach portrait URL - duplicated from chat.tsx
const getCoachPortraitUrl = (coachName: string): string => {
  const fileName = coachName.toLowerCase().replace(/\s+/g, "-") + "-portrait.png";
  return `https://storage.googleapis.com/awaken-audio-files/${fileName}`;
};

interface FirstMessageProps {
  onboarded: boolean;
  loading: boolean;
  reset: () => void;
  setOnboarded: (v: boolean) => void;
  setFirstMessage: (v: boolean) => void;
  setShowFirstMessage: (v: boolean) => void;
  setIsVoiceCall: (v: boolean) => void;
  coachName: string | null;
  originalOutput: string;
  availableCoaches: any[];
  setCoachName: (name: string) => void;
  user: any;
  audio: any;
  isPlaying: boolean;
  rootAudioRef: any;
  setIsPlaying: (v: boolean) => void;
  resetOutputSentence: () => void;
  setOutput: (s: string) => void;
  setIsComplete: (v: boolean) => void;
  stopAudio: () => void;
  audioScale: number;
  onboardingSpringProps: any;
  setShowCoachOverlay: (v: boolean) => void;
  handleTouchStart?: (e: React.TouchEvent<HTMLDivElement>) => void;
  handleTouchEnd?: (e: React.TouchEvent<HTMLDivElement>) => void;
}

export const FirstMessageComponent: React.FC<FirstMessageProps> = ({
  onboarded,
  loading,
  reset,
  setOnboarded,
  setFirstMessage,
  setShowFirstMessage,
  setIsVoiceCall,
  coachName,
  originalOutput,
  availableCoaches,
  setCoachName,
  user,
  audio,
  isPlaying,
  rootAudioRef,
  setIsPlaying,
  resetOutputSentence,
  setOutput,
  setIsComplete,
  stopAudio,
  audioScale,
  onboardingSpringProps,
  setShowCoachOverlay,
  handleTouchStart,
  handleTouchEnd,
}) => {
  // Animated transition for coach image
  const coachImageTransition = useTransition(coachName, {
    from: { opacity: 0, scale: 0.9 },
    enter: { opacity: 1, scale: 1 },
    leave: { opacity: 0, scale: 0.9 },
    config: { tension: 210, friction: 20 },
  });

  return (
    <>
      {/* Circle and fire visualization */}
      <animated.div
              /* CONFIG: Adjust 'mt-40' to move the whole component down. */
        className="relative flex flex-col items-center justify-start h-40 mt-24"
        
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
      >
        {/* Click overlay over coach portrait */}
                {/* Click overlay to open coach details. Positioned over the coach portrait. */}
        <div
          className="absolute cursor-pointer"
          style={{
            width: "120px",
            height: "120px",
            top: "5px",
            left: "50%",
            transform: "translate(-50%, 0)",
            zIndex: 30,
            borderRadius: "50%",
          }}
          onClick={() => coachName && setShowCoachOverlay(true)}
        />

        <Circle
          loading={loading}
          audio={audio}
          isPlaying={isPlaying}
          rootAudioRef={rootAudioRef}
          setIsPlaying={setIsPlaying}
          resetOutputSentence={resetOutputSentence}
          setOutput={setOutput}
          originalOutput={originalOutput}
          setIsComplete={setIsComplete}
          stopAudio={stopAudio}
          initAudio={null}
          audioScale={audioScale}
          coachName={coachName || undefined}
          isNormal={false}
        />

        {coachName && (
          /* CONFIG: 'top' moves the coach portrait down. */
          <div
            className="absolute"
            style={{
              width: "120px",
              height: "120px",
              top: "5px", // Nudges portrait down
              left: "50%",
              transform: "translate(-50%, 0)", // Centers horizontally
              zIndex: 11,
              pointerEvents: "none",
            }}
          >
            {coachImageTransition((style, name) =>
              name ? (
                <animated.img
                  src={getCoachPortraitUrl(name)}
                  alt={`${name} Portrait`}
                  style={{
                    ...style,
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    objectFit: "contain",
                    pointerEvents: "none",
                  }}
                />
              ) : null,
            )}
          </div>
        )}

        {/* CONFIG: 'top' moves the fire down. */}
        <FireSvg loading={loading} className="z-[5] absolute -top-[25px]" height="180" />

        {availableCoaches.length > 1 && coachName && (
          <div className="absolute top-0 left-0 right-0 text-center translate-y-[-150%] pb-1 z-50">            <CoachSelector
              coaches={availableCoaches}
              selectedCoach={coachName}
              onCoachChange={setCoachName}
              userChannelId={user?.channelId || ""}
            />
          </div>
        )}
      </animated.div>

      {/* Text display */}
      {!onboarded && (
        <ScrollArea
          css="w-full text-sm text-center !items-center md:text-base overflow-auto max-h-[50vh] relative z-30"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          <animated.div className="relative z-30 bg-transparent max-w-2xl mx-auto px-8">
            <Text
              className="text-sm md:text-base font-normal"
              style={{
                fontFamily: "Rethink Sans, sans-serif",
                color: "white",
                zIndex: 30,
              }}
            >
              {originalOutput}
            </Text>
          </animated.div>
        </ScrollArea>
      )}

      {/* Onboarding buttons */}
      {!onboarded && (
        <animated.div
          className="absolute flex bottom-0 left-0 right-0 flex-col mt-6 gap-2 items-center justify-center p-4 w-full md:max-w-4xl mx-auto touch-none"
          style={onboardingSpringProps}
        >
          <div className="w-full md:w-3/5 flex flex-col justify-between gap-2">
            <Button
              variant="ghost"
              onClick={async () => {
                reset();
                setOnboarded(true);
                setFirstMessage(false);
                setShowFirstMessage(false);
                setIsVoiceCall(true);
              }}
              disabled={loading}
              css="flex gap-2 items-center bg-[#FCA311] h-12 p-4 text-white font-semibold rounded-full shadow-[0_0_28px_rgba(252,163,17,0.9)] transition-all duration-300 ease-in-out hover:shadow-[0_0_32px_rgba(252,163,17,1)] hover:bg-[#FCA311] focus:outline-none focus:ring-2 focus:ring-[#FCA311] focus:ring-opacity-50 active:bg-[#E59100] w-full"
            >
              <AudioLines className="w-6 h-6" />
              <span>Call {coachName || "Kokoro"}</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => {
                setOnboarded(true);
                setFirstMessage(false);
                setShowFirstMessage(false);
              }}
              disabled={loading}
              css="justify-center text-sm disabled:opacity-50 disabled:pointer-events-none h-12 p-4 flex gap-2 items-center bg-[#B57300] text-white font-semibold rounded-full transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-[#FCA311] hover:bg-[#9E6400] focus:ring-opacity-50 shadow-[0_0_20px_rgba(181,115,0,0.5)] w-full"
            >
              <SquareMessageIcon className="w-5 h-5" />
              <span>Text {coachName || "Kokoro"}</span>
            </Button>
          </div>
        </animated.div>
      )}
    </>
  );
};