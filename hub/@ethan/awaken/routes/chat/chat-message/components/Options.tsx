"use client";

import React, { useState, useEffect, useRef } from "npm:react@canary";
import { But<PERSON>, ScrollArea, Text, Textarea } from "@reframe/ui/main.tsx";
import { animated, useTransition } from "npm:@react-spring/web@9.7.3";
import { createPortal } from "npm:react-dom@canary";
import { CircleXIcon } from "@reframe/icons/circle-x.ts";
import { MicIcon, SeedlingIcon, ChevronRightIcon } from "../../../../lib/icons.tsx";
import { AudioLines } from "@reframe/icons/audio-lines.ts";
import { UserCardView, getUserCardsForCoachAction } from "../../../../actions/db/coach-actions.ts";
import { VoiceNote } from "../../../../lib/VoiceNote.tsx";
import { generateGuidedSessionPlanAction } from "../../../../actions/ai/session-plan-actions.ts";

interface OptionsProps {
  isVisible: boolean;
  onClose: () => void;
  coachName: string;
  channelId: string;
  user: any;
  sendMessage: (message: string | Blob, isVoice: boolean, isAudio: boolean) => void;
  toggleVoiceCall: (card: UserCardView | null) => void;
}

export const Options: React.FC<OptionsProps> = ({
  isVisible,
  onClose,
  coachName,
  channelId,
  user,
  sendMessage,
  toggleVoiceCall,
}) => {
  // Voice Note overlay state
  const [voiceNoteCard, setVoiceNoteCard] = useState<UserCardView | null>(null);

  // ----------------------------------------------
  // Card-related states and logic (moved from Chat)
  // ----------------------------------------------

  // State for cards related to the current coach
  const [displayedCards, setDisplayedCards] = useState<UserCardView[]>([]);
  const [cardsLoading, setCardsLoading] = useState<boolean>(false);

  // Ref used for polling while cards are in GENERATING state
  const refreshIntervalRef = useRef<number | null>(null);

  // Helper to fetch cards for the current coach & channel
  const fetchCards = async () => {
    if (!channelId || !coachName || coachName === "") {
      console.log("[CARDS_FETCH] Skipping: Missing channelId or coach name");
      return;
    }

    // Show loader only on first load
    if (displayedCards.length === 0) {
      setCardsLoading(true);
    }

    try {
      const cards = await getUserCardsForCoachAction(Number(channelId), coachName);
      setDisplayedCards(cards);
      console.log("[CARDS_FETCH] Fetched cards:", cards);
    } catch (err) {
      console.error("[CARDS_FETCH] Error:", err);
      setDisplayedCards([]);
    } finally {
      if (displayedCards.length === 0) {
        setCardsLoading(false);
      }
    }
  };

  // Initial fetch whenever coach/channel changes
  useEffect(() => {
    if (coachName && channelId) {
      fetchCards();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [coachName, channelId]);

  // Polling while any card is still generating
  useEffect(() => {
    const isGenerating = displayedCards.some(c => c.instance_status === "GENERATING");

    if (isGenerating && refreshIntervalRef.current === null) {
      refreshIntervalRef.current = setInterval(fetchCards, 5000);
    } else if (!isGenerating && refreshIntervalRef.current !== null) {
      clearInterval(refreshIntervalRef.current);
      refreshIntervalRef.current = null;
    }

    return () => {
      if (refreshIntervalRef.current !== null) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [displayedCards]);

  // === New: refs to control scrolling ===
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const createGuidedHeaderRef = useRef<HTMLDivElement>(null);
  const customHeaderRef = useRef<HTMLDivElement>(null);
  const programsHeaderRef = useRef<HTMLDivElement>(null);

  // Create Guided Session collapsible & input state
  const [showCreateGuided, setShowCreateGuided] = useState(false);
  const [createGuidedInput, setCreateGuidedInput] = useState("");
  const [isSubmittingCreate, setIsSubmittingCreate] = useState(false);

  // Custom Sessions & Programs collapsible state
  const [showCustomSessions, setShowCustomSessions] = useState(false);
  const [showPrograms, setShowPrograms] = useState(false);

  // Auto-scroll behaviours – use precise math based on bounding-rects so we do not overshoot

  const scrollHeaderIntoView = (header: HTMLDivElement | null) => {
    if (!header || !scrollAreaRef.current) return;

    const container = scrollAreaRef.current;
    const containerRect = container.getBoundingClientRect();
    const headerRect = header.getBoundingClientRect();

    // Distance of the header from the visible top of the scroll container
    const offsetFromTop = headerRect.top - containerRect.top;

    container.scrollTo({
      // Maintain current scroll and just add the delta so header touches the top exactly
      top: container.scrollTop + offsetFromTop - 8, // slight offset for breathing room
      behavior: "smooth",
    });
  };

  // 1. For Create-Guided section
  useEffect(() => {
    if (showCreateGuided) {
      scrollHeaderIntoView(createGuidedHeaderRef.current);
    }
  }, [showCreateGuided]);

  // 2. For Custom Sessions section
  useEffect(() => {
    if (showCustomSessions) {
      scrollHeaderIntoView(customHeaderRef.current);
    }
  }, [showCustomSessions]);

  // 3. For Programs section
  useEffect(() => {
    if (showPrograms) {
      scrollHeaderIntoView(programsHeaderRef.current);
    }
  }, [showPrograms]);

  // Categorize the cards for rendering order
  const voiceNoteCards = displayedCards.filter(c => c.uiConfig?.interactionType === "voiceNote");
  const createGuidedCards = displayedCards.filter(c => c.type === "CREATE_GUIDED_SESSION");

  const [customSessionCards, setCustomSessionCards] = useState<UserCardView[]>([]);
  
  useEffect(() => {
    const latestCustomSessions = displayedCards.filter(
      (c) => c.type === "GUIDED_SESSION" && c.user_card_id,
    );
    setCustomSessionCards(latestCustomSessions);
  }, [displayedCards]);

  // Split GUIDED_SESSION cards into custom sessions (user created) and default programs
  const programCards = displayedCards.filter(c => c.type === "GUIDED_SESSION" && !c.user_card_id);

  // Slide-in/out animation with faster speed and minimal bounce
  const slideTransition = useTransition(isVisible, {
    from: {
      transform: "translateY(100%)",
      opacity: 1,
    },
    enter: {
      transform: "translateY(0%)",
      opacity: 1,
      delay: 80, // 80ms delay for button animation timing
    },
    leave: {
      transform: "translateY(100%)",
      opacity: 1,
      delay: 0, // No delay for exit
    },
    config: {
      mass: 0.9,    // Slightly more mass for stability
      tension: 350, // Slightly reduced to minimize bounce
      friction: 35, // Increased friction for much less bounce
      clamp: true,  // Prevent overshoot/bounce
    },
  });

  if (typeof document === "undefined") {
    return null;
  }

  // --- Add the handleCardAction function ---
  const handleOptionAction = async (option: UserCardView, inputValue?: string | Uint8Array | Blob) => {

    // Ensure user and coachName are available
    if (!user?.channelId || !coachName) {
      console.error("[Card Action] Missing user channelId or coachName. Aborting.");
      // TODO: Optionally show an error message to the user
      return;
    }

    const interactionType = option.uiConfig?.interactionType;
    console.log("[Card Action] Interaction Type:", interactionType);
    console.log("[Card Action] Input Value:", inputValue);

    if (interactionType === 'liveCall') {
      // For 'EXPLORE' or other simple live calls
      console.log("[Card Action] Initiating liveCall");
      toggleVoiceCall(option);
    } else if (interactionType === 'liveCallWithInput') {
      // For 'GOAL' or similar interactions needing input
      console.log("[Card Action] Initiating liveCallWithInput with input:", inputValue);
      if (!inputValue || (typeof inputValue === 'string' && inputValue.trim() === '')) {
         console.warn("[Card Action] liveCallWithInput triggered without valid input.");
         // TODO: Show an error to the user
         return;
      }
      toggleVoiceCall({...option, goalInput: inputValue as string});

    // --- Add handler for generateDynamicCard ---
    } else if (interactionType === 'generateDynamicCard') {
      console.log("[Card Action] Initiating generateDynamicCard with input:", inputValue);
      if (!inputValue || (typeof inputValue === 'string' && inputValue.trim() === '')) {
        console.warn("[Card Action] generateDynamicCard triggered without valid input.");
        // TODO: Show an error message to the user
        return;
      }
      // Call the generation action *without* await
      generateGuidedSessionPlanAction(user.channelId, inputValue as string, coachName);

      // // Immediately trigger a fetch after a short delay to start polling
      setTimeout(fetchCards, 10000);
    // --- End Add ---
    }
    else if (interactionType === 'voiceNote') {
      console.log("[Card Action] Initiating voiceNote");

      await sendMessage(inputValue as Blob, true, true);
    }
    else {
      console.warn(`[Card Action] Unhandled interaction type: ${interactionType}`);
      // Handle other types or show an error
    }
  };
  // --- End of handleCardAction function ---

  // Main options panel - render directly without portal for proper containment
  const optionsPanel = slideTransition((style, item) =>
    item ? (
      <animated.div
        style={{
          ...style,
          willChange: 'transform',
        }}
        className="absolute bottom-0 inset-x-0 md:left-0 md:right-auto md:w-auto md:min-w-[400px] md:max-w-[500px] h-[50vh] bg-gradient-to-b from-[#0B0B0B] via-[#161616] to-[#0B0B0B] backdrop-blur-md border-t md:border border-[#FCA31133] rounded-t-3xl flex flex-col z-50 md:shadow-2xl"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-[#FCA31133] w-full">
          <Text className="text-[#FCA311] font-semibold">What would serve you?</Text>
          <Button
            variant="ghost"
            css="p-2 hover:bg-[#FCA311]/10 rounded-lg"
            onClick={onClose}
          >
            <CircleXIcon className="w-5 h-5 text-[#FCA311]" />
          </Button>
        </div>

        {/* Body */}
        <ScrollArea ref={scrollAreaRef} className="flex-1 overflow-y-auto w-full">
          <div className="divide-y divide-[#FCA31133]">

            {/* Voice Note Option */}
            {voiceNoteCards.map((card) => {
              const title = (card.effectiveTitle || "Voice Reflection").replace("{{COACH_NAME}}", coachName || "Coach");
              const description = (card.effectiveDescription || "Record a voice reflection").replace("{{COACH_NAME}}", coachName || "Coach");
              return (
                <div key={card.user_card_id || card.coach_offering_id} className="flex items-center justify-between px-5 py-4">
                  <div className="flex flex-col flex-1 mr-4">
                    <Text className="text-sm font-semibold text-white mb-1">{title}</Text>
                    <Text className="text-xs text-white/70">{description}</Text>
                  </div>
                  <Button
                    variant="ghost"
                    css="bg-[#FCA311] hover:bg-[#FCA311]/80 text-black rounded-full p-3"
                    onClick={() => {
                      setVoiceNoteCard(card);
                      onClose(); // Close options panel when voice note is opened
                    }}
                  >
                    <MicIcon className="w-5 h-5" />
                  </Button>
                </div>
              );
            })}

            {/* Create Guided Session */}
            {createGuidedCards.map((card) => {
              const title = (card.effectiveTitle || "Create Guided Session").replace("{{COACH_NAME}}", coachName || "Coach");
              const description = (card.effectiveDescription || "Create a personalised guided session").replace("{{COACH_NAME}}", coachName || "Coach");
              const inputPlaceholder = card.uiConfig?.inputPlaceholder || "Enter details...";
              const buttonText = card.uiConfig?.buttonText?.replace("{{COACH_NAME}}", coachName || "Coach") || "Create";
              return (
                <div key={card.user_card_id || card.coach_offering_id} className="flex flex-col">
                  <div 
                    ref={createGuidedHeaderRef}
                    className="flex items-center justify-between px-5 py-4 cursor-pointer" 
                    onClick={() => setShowCreateGuided(!showCreateGuided)}
                  >
                    <div className="flex flex-col flex-1 mr-4">
                      <Text className="text-sm font-semibold text-white mb-1">{title}</Text>
                      <Text className="text-xs text-white/70">{description}</Text>
                    </div>
                    <ChevronRightIcon className={`w-6 h-6 text-[#FCA311] transition-transform ${showCreateGuided ? 'rotate-90' : ''}`} />
                  </div>
                  {showCreateGuided && (
                    <div className="px-4 pb-4 space-y-3">
                      <textarea
                        value={createGuidedInput}
                        onChange={(e) => setCreateGuidedInput(e.target.value)}
                        placeholder={inputPlaceholder}
                        className="w-full bg-[#5C3B0633] border border-[#FCA311]/20 !text-white placeholder:!text-white/50 rounded-lg p-3 resize-none focus:outline-none focus:border-2 focus:border-[#FCA311] focus:border-opacity-80 focus:ring-0"
                        rows={3}
                      />
                      <Button
                        css="w-full flex items-center justify-center bg-gradient-to-r from-amber-300 to-amber-500 hover:from-amber-500 hover:to-amber-600 text-black rounded-full py-2.5 text-sm font-semibold"
                        disabled={isSubmittingCreate || createGuidedInput.trim() === ""}
                        onClick={() => {
                          if (isSubmittingCreate) return;
                          setIsSubmittingCreate(true);
                          try {
                            handleOptionAction(card, createGuidedInput);

                            // Append a temporary GENERATING card so the user sees immediate feedback
                            const fakeCard: UserCardView = {
                              ...card,
                              user_card_id: `temp-${Date.now()}`,
                              instance_status: "GENERATING",
                              effectiveTitle: createGuidedInput,
                              effectiveDescription: "",
                            };

                            setCustomSessionCards(prev => [...prev, fakeCard]);
                            setCreateGuidedInput("");
                            setShowCreateGuided(false);
                            // Ensure the custom sessions section is expanded
                            setShowCustomSessions(true);

                            // Smoothly scroll to the bottom of the list
                            requestAnimationFrame(() => {
                              if (scrollAreaRef.current) {
                                scrollAreaRef.current.scrollTo({ top: scrollAreaRef.current.scrollHeight, behavior: "smooth" });
                              }
                            });
                          } finally {
                            setIsSubmittingCreate(false);
                          }
                        }}
                      >
                        {isSubmittingCreate ? 'Creating...' : (
                          <>
                            <SeedlingIcon className="inline-block w-4 h-4 mr-2" />
                            {buttonText}
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              );
            })}

            {/* Custom Sessions Collapsible */}
            {customSessionCards.length > 0 && (
              <div className="flex flex-col">
                <div
                  ref={customHeaderRef}
                  className="flex items-center justify-between px-5 py-4 cursor-pointer"
                  onClick={() => setShowCustomSessions(!showCustomSessions)}
                >
                  <Text className="text-sm font-semibold text-white">Custom Sessions</Text>
                  <ChevronRightIcon className={`w-6 h-6 text-[#FCA311] transition-transform ${showCustomSessions ? 'rotate-90' : ''}`} />
                </div>
                {showCustomSessions && (
                  <div className="flex flex-col space-y-1 pl-3 pr-2 pb-2 divide-y divide-white/10">
                    {/* Regular custom session cards (not generating) */}
                    {customSessionCards
                      .filter(card => card.instance_status !== "GENERATING")
                      .map((card) => {
                        const title = (card.effectiveTitle || "Session").replace("{{COACH_NAME}}", coachName || "Coach");
                        const description = (card.effectiveDescription || "").replace("{{COACH_NAME}}", coachName || "Coach");
                        return (
                          <div key={card.user_card_id || card.coach_offering_id} className="flex items-center justify-between ml-1 py-2 group">
                            <div className="flex flex-col max-w-[70%]">
                              <Text className="text-sm font-medium text-[#FCA311] group-hover:text-[#FCA311] mb-1">{title}</Text>
                              <Text className="text-xs text-white/50 group-hover:text-white/70">{description}</Text>
                            </div>
                            <Button
                              variant="ghost"
                              css="p-3 rounded-full bg-[#FCA311] hover:bg-[#D38F04] text-black mr-1 aspect-square"
                              onClick={() => {
                                handleOptionAction(card, undefined);
                                onClose(); // Close options panel when session is selected
                              }}
                            >
                              <AudioLines className="w-5 h-5" />
                            </Button>
                          </div>
                        );
                      })}
                    {/* Generating cards (always at the bottom) */}
                    {customSessionCards
                      .filter(card => card.instance_status === "GENERATING")
                      .map((card, index) => (
                        <div key={card.user_card_id || card.coach_offering_id} className="relative flex items-center justify-between ml-1 py-2 group overflow-hidden">
                          {/* Shimmer effect across entire card */}
                          <div 
                            className={`absolute inset-0 w-full h-full shimmer-${index}`}
                            style={{
                              background: 'linear-gradient(90deg, transparent 0%, rgba(252, 163, 17, 0.25) 50%, transparent 100%)',
                              animation: `shimmerMove 2s infinite linear`,
                              transform: 'translateX(-100%)',
                            }}
                          />
                          <div className="flex flex-col max-w-[90%] relative z-10 pl-4">
                            <Text className="text-sm font-medium text-[#FCA311] mb-1 italic">Generating...</Text>
                            <Text className="text-xs text-white/70">{coachName || "Coach"} is tailoring a custom session as requested</Text>
                          </div>
                          <div className="relative p-3 rounded-full mr-1 aspect-square overflow-hidden bg-[#FCA311]/20">
                            <div className="relative w-5 h-5 bg-[#FCA311]/30 rounded-full animate-pulse" />
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </div>
            )}

            {/* Programs Collapsible */}
            {programCards.length > 0 && (
              <div className="flex flex-col">
                <div
                  ref={programsHeaderRef}
                  className="flex items-center justify-between px-5 py-4 cursor-pointer"
                  onClick={() => setShowPrograms(!showPrograms)}
                >
                  <Text className="text-sm font-semibold text-white">Programs</Text>
                  <ChevronRightIcon className={`w-6 h-6 text-[#FCA311] transition-transform ${showPrograms ? 'rotate-90' : ''}`} />
                </div>
                {showPrograms && (
                  <div className="flex flex-col space-y-1 pl-3 pr-2 pb-2 divide-y divide-white/10">
                    {programCards.map((card) => {
                      const title = (card.effectiveTitle || "Program").replace("{{COACH_NAME}}", coachName || "Coach");
                      const description = (card.effectiveDescription || "").replace("{{COACH_NAME}}", coachName || "Coach");
                      return (
                        <div key={card.user_card_id || card.coach_offering_id} className="flex items-center justify-between ml-1 py-2 group">
                          <div className="flex flex-col max-w-[70%]">
                            <Text className="text-sm font-medium text-[#FCA311] group-hover:text-[#FCA311] mb-1">{title}</Text>
                            <Text className="text-xs text-white/50 group-hover:text-white/70">{description}</Text>
                          </div>
                          <Button
                            variant="ghost"
                            css="p-3 rounded-full bg-[#FCA311] hover:bg-[#D38F04] text-black mr-1 aspect-square"
                            onClick={() => {
                              handleOptionAction(card, undefined);
                              onClose(); // Close options panel when program is selected
                            }}
                          >
                            <AudioLines className="w-5 h-5" />
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            )}

          </div>
        </ScrollArea>
      </animated.div>
    ) : null
  );

  // Render voice note overlay (if active)
  const voiceNoteOverlay = () => {
    if (!voiceNoteCard) return null;

    return createPortal(
      <>
        {/* Backdrop */}
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm z-40" />

        {/* Voice Note Component */}
        <VoiceNote
          channelId={channelId}
          coachName={coachName}
          isOpen
          onClose={() => setVoiceNoteCard(null)}
          onRecordingComplete={(audioBlob: Blob) => {
            handleOptionAction(voiceNoteCard, audioBlob);
            setVoiceNoteCard(null);
          }}
        />
      </>,
      document.body,
    );
  };

  return (
    <>
      {/* Add shimmer animation keyframes */}
      <style>
        {`
          @keyframes shimmerMove {
            0% {
              transform: translateX(-100%);
            }
            100% {
              transform: translateX(300%);
            }
          }
        `}
      </style>
      {optionsPanel}
      {voiceNoteOverlay()}
    </>
  );
};