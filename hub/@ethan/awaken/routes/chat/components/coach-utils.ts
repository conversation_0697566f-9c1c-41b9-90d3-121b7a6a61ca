"use client";

import { userHasCoachAccessAction, getDefaultCoachAction } from "../../../actions/db/coach-actions.ts";
import { updateSelectedCoach } from "../../../actions/db/user-actions.ts";
import { getUserData } from "../../../lib/db.ts";

/**
 * Simple cache to avoid unnecessary localStorage/server calls
 */
type CoachCache = {
  selectedCoach: string | null;
  userChannelId: string | null;
};

const cache: CoachCache = {
  selectedCoach: null,
  userChannelId: null
};

/**
 * Initialize coach selection system for current user
 * Call this once when user logs in or when app initializes
 * 
 * @param channelId - The channel ID of the current user
 */
export const initCoachSelection = (channelId: number | string): void => {
  const channelIdStr = String(channelId);
  cache.userChannelId = channelIdStr;
  
  // Get from localStorage if available
  if (typeof window !== "undefined") {
    const storedCoach = localStorage.getItem(`selectedCoach-${channelIdStr}`);
    if (storedCoach) {
      cache.selectedCoach = storedCoach;
    }
  }
};

/**
 * Get the currently selected coach for a user
 * Checks localStorage first, then falls back to provided default
 * 
 * @param channelId - The channel ID of the user
 * @param defaultCoach - The default coach name if no selection is found (defaults to "Kokoro")
 * @returns string - The name of the selected coach
 */
export const getSelectedCoach = (
  channelId: number | string,
  defaultCoach: string = "Kokoro"
): string => {
  const channelIdStr = String(channelId);
  
  // Check localStorage first (only in browser context)
  if (typeof window !== "undefined") {
    const storedCoach = localStorage.getItem(`selectedCoach-${channelIdStr}`);
    if (storedCoach) {
      return storedCoach;
    }
  }
  
  // Fall back to default
  return defaultCoach;
};

/**
 * Fetch the selected coach from the database and sync with localStorage
 * Use this to ensure consistency between devices
 * 
 * @param channelId - The channel ID of the user
 * @param setCoachName - State setter function to update the coach name in the component
 * @returns Promise<void>
 */
export const fetchAndSyncSelectedCoach = async (
  channelId: number | string,
  setCoachName: (name: string) => void
): Promise<void> => {
  try {
    // Get the user's data including selectedCoach
    const userData = await getUserData(String(channelId));
    
    if (userData && userData.selectedCoach) {
      // Update state with the database value
      setCoachName(userData.selectedCoach);
      
      // Store in localStorage for future reference
      if (typeof window !== "undefined") {
        localStorage.setItem(`selectedCoach-${channelId}`, userData.selectedCoach);
      }
    }
  } catch (error) {
    console.error("Error fetching selected coach:", error);
  }
};

/**
 * Set the selected coach for a user
 * Updates both localStorage and database
 * 
 * @param channelId - The channel ID of the user
 * @param coachName - The name of the selected coach
 * @param skipAccessCheck - Skip the access check (useful when coaches are pre-filtered)
 * @returns Promise<boolean> - True if the update was successful
 */
export const setSelectedCoach = async (
  channelId: number | string,
  coachName: string,
  skipAccessCheck: boolean = false
): Promise<boolean> => {
  const channelIdStr = String(channelId);
  
  // Verify user has access to this coach (unless skipped)
  if (!skipAccessCheck) {
    const hasAccess = await userHasCoachAccessAction(channelIdStr, coachName);
    if (!hasAccess) {
      console.error(`User does not have access to coach ${coachName}`);
      return false;
    }
  }
  
  // Update localStorage immediately for UI responsiveness
  if (typeof window !== "undefined") {
    localStorage.setItem(`selectedCoach-${channelIdStr}`, coachName);
  }
  
  // Update database
  try {
    const channelIdNum = Number(channelId);
    return await updateSelectedCoach(channelIdNum, coachName);
  } catch (error) {
    console.error("Error updating selected coach:", error);
    return false;
  }
};

/**
 * Clear the selected coach from localStorage
 * Useful when user logs out
 * 
 * @param channelId - The channel ID of the user
 */
export const clearSelectedCoach = (channelId: number | string): void => {
  const channelIdStr = String(channelId);
  if (typeof window !== "undefined") {
    localStorage.removeItem(`selectedCoach-${channelIdStr}`);
  }
};

/**
 * Get the default coach from the server
 * Useful when user doesn't have a selected coach or when resetting
 * 
 * @returns Promise<string> - The default coach name
 */
export const getDefaultCoach = async (): Promise<string> => {
  const defaultCoach = await getDefaultCoachAction();
  return defaultCoach || "Kokoro";
};

/**
 * Get the human coach name from an AI coach name
 * Removes the " AI" suffix if present
 * 
 * @param coachName - The coach name to convert
 * @returns string - The human coach name
 */
export const getHumanCoachName = (coachName: string): string => {
  // Remove " AI" suffix if present
  return coachName.endsWith(' AI') ? coachName.slice(0, -3) : coachName;
};

/**
 * List of AI-only coaches (not linked to humans)
 */
const AI_ONLY_COACHES = ['Kokoro', 'Grace', 'Nexus'];

/**
 * Check if a coach is AI-only (not linked to a human)
 * 
 * @param coachName - The coach name to check
 * @returns boolean - True if the coach is AI-only
 */
export const isAIOnlyCoach = (coachName: string): boolean => {
  return AI_ONLY_COACHES.includes(coachName);
};

/**
 * Get the appropriate display name for coach-related messages
 * For AI-only coaches, returns "Awaken's founders"
 * For human-linked coaches, returns the human name (without " AI")
 * 
 * @param coachName - The coach name
 * @returns string - The display name for messages
 */
export const getCoachDisplayName = (coachName: string): string => {
  if (isAIOnlyCoach(coachName)) {
    return "Awaken's creators";
  }
  return getHumanCoachName(coachName);
};
