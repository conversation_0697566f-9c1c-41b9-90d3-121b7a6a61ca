"use client";

import React from "npm:react@canary";
import { motion, AnimatePresence } from "npm:framer-motion";
import { <PERSON><PERSON> } from "@reframe/ui/main.tsx";
import { XIcon } from "@reframe/icons/x.ts";
import { BellIcon } from "../../../lib/icons.tsx";
import { Logo } from "../../../lib/logo.tsx";

// Updated interface to align with our database schema
interface Notice {
  id?: string;
  Message: string;
  Date: string; // This is now the createdAt field from the database, but kept as Date for backward compatibility
  CreatedBy?: string;
}

interface NoticeOverlayProps {
  notice: Notice;
  onDismiss: () => void;
}

export const NoticeOverlay: React.FC<NoticeOverlayProps> = ({ notice, onDismiss }) => {
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="fixed top-0 left-0 right-0 z-50 flex justify-center items-start p-4 pointer-events-none"
      >
        <motion.div 
          className="
            relative max-w-md w-full p-6 rounded-2xl pointer-events-auto
            bg-gradient-to-r from-[#1E1E1E] to-[#1E1E1E]/90
            border border-[#FCA3114D] backdrop-blur-lg
            shadow-[0_8px_32px_rgba(252,163,17,0.15)]
          "
          initial={{ scale: 0.95 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0.95 }}
        >
          {/* Top gradient line */}
          <div 
            className="absolute top-0 left-0 right-0 h-[1px] rounded-t-2xl"
            style={{
              background: "linear-gradient(90deg, transparent, #FCA311, transparent)"
            }}
          />
          
          {/* Header with Logo */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Logo size={24} />
              <span className="text-[#FCA311] text-sm font-medium">awaken</span>
            </div>
            <button
              onClick={onDismiss}
              className="
                flex-shrink-0 rounded-full p-1.5
                text-white/50 hover:text-white
                transition-colors duration-200
                hover:bg-white/10
              "
            >
              <XIcon className="w-4 h-4" />
            </button>
          </div>
          
          <div className="flex items-start gap-4">
            {/* Icon container with gradient background */}
            <div className="
              flex-shrink-0 w-10 h-10 rounded-full 
              bg-gradient-to-br from-[#FCA311]/20 to-[#FF5727]/20
              flex items-center justify-center
            ">
              <BellIcon size={20} color="#FCA311" />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <p className="text-[#FCA311] text-sm font-medium mb-1">
                New Update
              </p>
              <p className="text-white/90 text-sm leading-relaxed whitespace-pre-line">
                {notice.Message}
              </p>
              <p className="text-white/50 text-xs mt-2">
                {new Date(notice.Date).toLocaleDateString(undefined, {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-6 flex flex-col gap-3">
            <Button
              onClick={onDismiss}
              css="w-full bg-[#FCA311] hover:bg-[#FCA311]/90 text-black font-medium transition-colors"
            >
              Continue to Chat
            </Button>
            <Button
              onClick={() => globalThis.location.href = '/changelog'}
              css="w-full bg-transparent border border-[#FCA3114D] hover:border-[#FCA311] text-[#FCA311] font-medium transition-colors hover:bg-[#FCA31133] hover:text-white"
            >
              View Update History
            </Button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}; 