"use client";

import React, { useState, useRef, useEffect } from "npm:react@canary";
import { useSpring, config } from "npm:@react-spring/web";
import { animated } from "npm:@react-spring/web";

// Utility to detect mobile devices
const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    typeof navigator !== 'undefined' ? navigator.userAgent : ''
  );
}

interface CircleProps {
    loading: boolean;
    size?: string;
    isCall?: boolean;
    isPlaying: boolean;
    isNormal?: boolean;
    assistantVolume?: number;
    userVolume?: number;
    isCoachSpeaking?: boolean; // Add new prop to detect when coach is speaking
    stopAudio?: () => void;
    initAudio?: (src: string, init: boolean) => void;
    audioScale?: number;
    coachName?: string; // Add coachName to trigger pulse on change
  }
  
export const Circle: React.FC<CircleProps> = ({
    loading,
    size = "129.02px",
    isCall = false,
    isPlaying,
    isNormal = true,
    assistantVolume = 0,
    userVolume = 0,
    isCoachSpeaking = false, // Default to false
    coachName,
  }) => {
    const [showStopButton, setShowStopButton] = useState(true);
    const stopButtonTimeout = useRef<number | null>(null);
    // Track if we need to show the initial load animation
    const [initialLoadAnimationComplete, setInitialLoadAnimationComplete] = useState(false);
    const [initialShrinkComplete, setInitialShrinkComplete] = useState(false);
    const wasLoading = useRef(loading);
    
    // Pulse effect state - only for non-call main chat view
    const [showPulse, setShowPulse] = useState(false);
    const pulseIntervalRef = useRef<number | null>(null);
    
    
    // Calculate actual size values for animations
    const baseSize = parseInt(size.replace("px", ""));
    
    // New size definitions based on spiritual/meditative feel
    const expandedSizeMax = baseSize * 0.9; // 90% for gentle expansion (when call completes)
    const loadingMaxSize = baseSize * 0.9; // 90% max size during loading
    const loadingMinSize = baseSize * 0.8; // 80% min size during loading
    const userSpeakingMinSize = baseSize * 0.8; // 60% min when user is speaking (for obvious breathing)
    const userSpeakingMaxSize = baseSize * 1.0; // 100% max when user is speaking
    
    // Assistant speaking size based on volume (85-115% range - much more noticeable)
    // Volume typically ranges 0-1, we'll clamp and smooth it
    const normalizedVolume = Math.min(Math.max(assistantVolume || 0, 0), 1);
    // Use a smoother curve for less reactivity
    const volumeCurve = Math.pow(normalizedVolume, 0.8); // Less aggressive curve for smoother response
    const speakingSizeMin = baseSize * 0.85; // 85% minimum
    const speakingSizeMax = baseSize * 1.0; // 115% maximum
    const currentSpeakingSize = speakingSizeMin + (speakingSizeMax - speakingSizeMin) * volumeCurve;
    
    // User volume-based sizing for listening state (60-100% range - very noticeable)
    const normalizedUserVolume = Math.min(Math.max(userVolume || 0, 0), 1);
    const userVolumeCurve = Math.pow(normalizedUserVolume, 1.2); // More aggressive curve to filter out quiet noises
    const currentUserSize = userSpeakingMinSize + (userSpeakingMaxSize - userSpeakingMinSize) * userVolumeCurve;
    
    // Determine animation phase for proper animation choice
    const getAnimationPhase = () => {
      // We need to handle the transition from loading to call active specially
      if (wasLoading.current && !loading && !initialLoadAnimationComplete) {
        return "callStart";
      }
      if (loading) return "loading";
      if (isCall && isCoachSpeaking) return "speaking"; 
      if (isCall) return "listening";
      return "default";
    };

    const animationPhase = getAnimationPhase();

    // Update the wasLoading ref whenever loading changes
    useEffect(() => {
      wasLoading.current = loading;
      
      // Reset initial shrink state when loading starts
      if (loading) {
        setInitialShrinkComplete(false);
      }
    }, [loading]);

    // Pulse effect - every 8 seconds when not in call and not loading
    useEffect(() => {
      if (!isCall && !loading) {
        const startPulseTimer = () => {
          if (pulseIntervalRef.current) {
            clearInterval(pulseIntervalRef.current);
          }
          pulseIntervalRef.current = setInterval(() => {
            setShowPulse(true);
            setTimeout(() => setShowPulse(false), 1000); // Pulse lasts 800ms
          }, 8000); // Every 8 seconds
        };
        
        startPulseTimer();
        
        return () => {
          if (pulseIntervalRef.current) {
            clearInterval(pulseIntervalRef.current);
          }
        };
      }
    }, [isCall, loading]);

    // Trigger pulse immediately when coach changes
    useEffect(() => {
      if (!isCall && !loading && coachName) {
        setShowPulse(true);
        setTimeout(() => setShowPulse(false), 1000);
        
        
        // Reset the timer to avoid double-pulsing
        if (pulseIntervalRef.current) {
          clearInterval(pulseIntervalRef.current);
          pulseIntervalRef.current = setInterval(() => {
            setShowPulse(true);
            setTimeout(() => setShowPulse(false), 1000);
          }, 8000);
        }
      }
    }, [coachName, isCall, loading]);

    // Configure animations based on the current phase
    const animatedStyles = useSpring({
      from: {
        opacity: 1,
        scale: 1, // Always start at 100%
        borderWidth: 4,
        width: `${baseSize}px`, // Always start at baseSize
        height: `${baseSize}px`, // Always start at baseSize
        bottom: isNormal ? "30%" : "none",
      },
      to: async (next) => {
        // Different animations for different phases
        switch (animationPhase) {
          case "loading":
            // Only do initial shrink if not already done
            if (!initialShrinkComplete) {
              // First shrink from 100% to the smaller loading size - gentle and slow
              await next({ 
                width: `${loadingMinSize}px`, 
                height: `${loadingMinSize}px`, 
                scale: 0.8,
                opacity: 0.85,
                config: { tension: 80, friction: 20, duration: 800 }
              });
              setInitialShrinkComplete(true);
            }
            
            // Now pulse like deep meditation breathing - 4s in, 4s out
            while (loading) {
              await next({ 
                width: `${loadingMaxSize}px`, 
                height: `${loadingMaxSize}px`, 
                scale: 0.9, 
                opacity: 0.95,
                config: { tension: 60, friction: 25, duration: 4000 } // 4s inhale
              });
              await next({ 
                width: `${loadingMinSize}px`, 
                height: `${loadingMinSize}px`, 
                scale: 0.8, 
                opacity: 0.85,
                config: { tension: 60, friction: 25, duration: 4000 } // 4s exhale
              });
            }
            break;
            
          case "callStart":
            // Call just finished loading - gentle expansion like opening a lotus
            await next({ 
              width: `${expandedSizeMax}px`, 
              height: `${expandedSizeMax}px`, 
              scale: 0.9,
              opacity: 1,
              config: { tension: 120, friction: 20, duration: 1200 }
            });
            setInitialLoadAnimationComplete(true);
            break;
            
          case "speaking":
            // Assistant speaking - size scales with volume (85-115%)
            // Animated to smoothly reflect changes in assistantVolume
            await next({ 
              width: `${currentSpeakingSize}px`, 
              height: `${currentSpeakingSize}px`,
              scale: 0.85 + (volumeCurve * 0.2), // Reactive scale (0.85 to 1.05) - reduced range
              opacity: 1,
              // Smoother spring physics for less jumpy feel
              config: { mass: 1.2, tension: 140, friction: 24, clamp: true } 
            });
            break;
            
          case "listening":
            // User speaking/assistant listening - size responds to user volume (60-100%)
            const currentUserScale = 0.75 + (userVolumeCurve * 0.4); // 0.6 to 1.0 scale
            await next({ 
              width: `${currentUserSize}px`,
              height: `${currentUserSize}px`,
              scale: currentUserScale,
              opacity: 0.92 + (userVolumeCurve * 0.08), // Opacity also responds to volume
              config: { mass: 1.5, tension: 120, friction: 26, clamp: true } // Much smoother, less jumpy
            });
            
            // No automatic pulsing - size responds to user volume in real-time
            // The animation will update when userVolume prop changes
            break;
            
          default:
            // Default state - stable with slight breathing
            await next({
              opacity: 1,
              scale: 1,
              borderWidth: 4,
              width: `${baseSize}px`,
              height: `${baseSize}px`,
              bottom: isNormal ? "30%" : "none",
              config: { duration: 500 }
            });
        }
      },
      // Make animation immediate on initial render
      immediate: false,
      config: { ...config.gentle }
    });
  
    // when the showStopButton changes, set the timeout to hide the stop button
    useEffect(() => {
      if (isPlaying && showStopButton) {
        stopButtonTimeout.current = window.setTimeout(() => {
          setShowStopButton(false);
        }, 4000);
      } else {
        if (stopButtonTimeout.current) {
          clearTimeout(stopButtonTimeout.current);
        }
      }

      return () => {
        if (stopButtonTimeout.current) {
          clearTimeout(stopButtonTimeout.current);
        }
      };
    }, [isPlaying]);
  
    return (
      <>
        {/* Pulse effect - only visible in main chat view, positioned to match circle exactly */}
        {showPulse && !isCall && (
          <animated.div
            className="rounded-full border-4 border-orange-400/100 animate-ping pointer-events-none"
            style={{
              position: "absolute",
              borderRadius: "50%",
              width: `${baseSize * 0.64}px`,
              height: `${baseSize * 0.64}px`,
              top: "22px",
              bottom: isNormal ? "30%" : "none",
              zIndex: 5,
              animationDuration: "1400ms",
              animationIterationCount: 1,
              // Center the smaller pulse relative to the circle - mathematically perfect centering
              marginLeft: `${baseSize * 0}px`,
              marginBottom: `${baseSize * 0.2}px`,
            }}
          />
        )}
        
        
        <animated.div
          className="cursor-default"
          style={{
            position: "absolute",
            borderRadius: "50%",
            // Conditionally set background based on isCall
            background: isCall ? "linear-gradient(270deg, #000000 2.23%, #ECBC6E 235.71%)" : "#000000",
            border: "2.3px solid #FCA311",
            // Enhanced glow effect, more vibrant when speaking - softer spiritual glow
            boxShadow: isCoachSpeaking 
              ? "0px 0px 40px 8px rgba(252, 163, 17, 0.6)" 
              : "0px 0px 32px 6px rgba(252, 163, 17, 0.4)",
            zIndex: 10, // Ensures it's in front
            cursor: "default",
            willChange: "transform, opacity, width, height", // Optimize for animation
            ...animatedStyles,
          }}
          onTouchEnd={(e) => {
            e.preventDefault();
          }}
          onTouchMove={(e) => {
            e.preventDefault();
          }}
        >
        </animated.div>
        
      </>
    );
};