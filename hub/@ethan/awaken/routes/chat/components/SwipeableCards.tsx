"use client";

import React, { useState, useRef, useEffect, useCallback } from "npm:react@canary";
import { <PERSON><PERSON>, Text, Loader, Textarea } from "@reframe/ui/main.tsx";
import { type UserCardView } from "../../../actions/db/coach-actions.ts";
import { AudioLines } from "@reframe/icons/audio-lines.ts";
import { SeedlingIcon } from "../../../lib/icons.tsx";
import { VoiceNote } from "../../../lib/VoiceNote.tsx";

// Restore debounce function
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: number | null = null;
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      func(...args);
    };
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(later, wait);
  };
}

// Add this throttle utility function near the debounce function
function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

interface SwipeableCardsProps {
  channelId: string;
  cards: UserCardView[];
  coachName: string | null;
  isLoading: boolean;
  onCardAction: (card: UserCardView, inputValue?: string | Blob) => void;
}

// Basic card component (can be moved to its own file later if needed)
interface CardProps {
  card: UserCardView;
  channelId: string;
  coachName: string | null;
  isSelected: boolean;
  isDesktop: boolean;
  onAction: (card: UserCardView, inputValue?: string | Blob) => void;
}

// Update Card component to accept CardProps
const Card: React.FC<CardProps> = ({ card, channelId, coachName, isSelected, isDesktop, onAction }) => {
  const [inputValue, setInputValue] = useState("");
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [isInitiatingCreation, setIsInitiatingCreation] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const buttonText = card.uiConfig?.buttonText?.replace("{{COACH_NAME}}", coachName || "Coach") || "Interact";
  const title = (card.effectiveTitle || "Card").replace("{{COACH_NAME}}", coachName || "Coach");
  const description = (card.effectiveDescription || "").replace("{{COACH_NAME}}", coachName || "Coach");
  const needsInput = card.uiConfig?.interactionType === 'liveCallWithInput' || card.uiConfig?.interactionType === 'generateDynamicCard';
  const isLiveCall = card.uiConfig?.interactionType === 'liveCall' || card.uiConfig?.interactionType === 'liveCallWithInput';
  const isVoiceNote = card.uiConfig?.interactionType === 'voiceNote';
  const inputPlaceholder = card.uiConfig?.inputPlaceholder || "Enter text...";

  // --- Add Generating State Check ---
  const isGenerating = card.instance_status === 'GENERATING';
  // --- End Add ---

  // Auto-focus logic
  useEffect(() => {
    if (isSelected && needsInput && isDesktop && textareaRef.current) {
      // Use a timeout to ensure focus happens after potential layout shifts/renders
      const timer = setTimeout(() => {
        textareaRef.current?.focus();
      }, 50); // Small delay
      return () => clearTimeout(timer);
    }
  }, [isSelected, needsInput, isDesktop]);

  // --- Effect to reset local initiation state ---
  useEffect(() => {
    // Only reset if we were initiating locally and the backend state is now NOT generating
    if (!isGenerating && isInitiatingCreation) {
      setIsInitiatingCreation(false);
      setInputValue("");
    }
    // Add isInitiatingCreation dependency as per React rules (we read it inside)
  }, [isGenerating, isInitiatingCreation]);
  // --- End Add ---

  // Add the onClick handler to the button
  const handleActionClick = () => {
     // Don't trigger action if already generating (from backend) or initiation just started
    if (isGenerating || isInitiatingCreation) return;

    // If it's the type that generates a new card, set local state immediately
    if (card.uiConfig?.interactionType === 'generateDynamicCard') {
      setIsInitiatingCreation(true);
    }

    // For voice notes, show the recorder instead of calling action directly
    if (isVoiceNote) {
      setShowVoiceRecorder(true);
      return; // Don't proceed to call onAction yet for voice notes
    }
    
    // Call the onAction prop passed from SwipeableCards
    // Pass the card object and the current input value if the card needs input
    onAction(card, needsInput ? inputValue : undefined);
  };

  // Handle voice recorder completion
  const handleVoiceRecorderComplete = (audioBlob: Blob) => {
    onAction(card, audioBlob);
  };

  return (
    <div
      className={`relative flex-shrink-0 w-[85vw] max-w-sm rounded-xl p-6 flex flex-col justify-between snap-center transition-all duration-300 origin-center select-none border
        ${isSelected
          ? "h-72 sm:h-72 border-2 border-amber-400/90 shadow-lg shadow-amber-400/50"
          : "h-72 sm:h-72 border-amber-500/40"
        }
        ${isGenerating ? 'opacity-70 cursor-not-allowed' : 'cursor-pointer'} // Keep base generating state style, removed border animation
        h-60 max-h-[60vh]
        ${globalThis.innerHeight <= 600 ? 'bottom-3' : 'bottom-12'}
      `}
      style={{
        transformOrigin: "center center", // Center transform origin
      }}
    >
      {/* --- Add Tailored Tag (for User-Specific Guided Sessions) --- */}
      {card.type === 'GUIDED_SESSION' && card.user_card_id !== null && (
        <div className="absolute top-0 left-1/2 -translate-x-1/2 bg-amber-800/90 text-amber-100 text-[10px] font-medium px-2.5 py-0.5 rounded-bl-xl rounded-br-xl shadow-sm">
           CREATED FOR YOU
        </div>
      )}
      {/* --- Add Default Guided Tag --- */}
      {card.type === 'GUIDED_SESSION' && card.user_card_id === null && (
        <div className="absolute top-0 left-1/2 -translate-x-1/2 bg-neutral-800/90 text-neutral-300 text-[10px] font-medium px-2.5 py-0.5 rounded-bl-xl rounded-br-xl shadow-sm">
           GUIDED
        </div>
      )}
      {/* --- End Add --- */}

      {/* Main content area */}
      <div className="flex-grow flex flex-col justify-center">
        {/* --- Apply conditional font size based on card type and user_card_id --- */}
        <Text className={`
          ${card.type === 'GUIDED_SESSION' && card.user_card_id !== null ? 'text-xl' : 'text-2xl'} 
          font-semibold text-center mb-3 text-neutral-100 ${isSelected ? "text-neutral-100" : "text-neutral-100"}
        `}>{title}</Text>
        {card.type !== 'GOAL' && !needsInput && (
          <Text className={`text-base text-center text-neutral-300 ${isSelected ? "text-neutral-300" : "text-neutral-300"}`}>{description}</Text>
        )}

        {needsInput && (
          <div className="w-full p-4 rounded-xl bg-[#5C3B0633] border border-[#FCA311]/20 mb-2 focus-within:border-[#FCA311] transition-colors">
            {/* Add fixed-height container to physically constrain the textarea */}
            <div className="h-[72px] overflow-hidden relative">
              <Textarea
                ref={textareaRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={inputPlaceholder}
                disabled={isGenerating} // Disable textarea if generating
                className="w-full bg-transparent border-none focus:outline-none resize-none h-18 max-h-18 overflow-y-auto scrollbar-thin scrollbar-thumb-[#FCA311]/40 scrollbar-track-transparent"
                rows={1}
                css={`
                  appearance: none;
                  color: white !important;
                  height: 72px !important;
                  max-height: 72px !important;
                  overflow-y: auto;
                  scrollbar-width: thin;
                  border: none !important;
                  outline: none !important;
                  box-shadow: none !important;

                  &::-webkit-scrollbar {
                    width: 4px;
                  }
                  &::-webkit-scrollbar-track {
                    background: transparent;
                  }
                  &::-webkit-scrollbar-thumb {
                    background-color: rgba(252, 163, 17, 0.4);
                    border-radius: 4px;
                  }
                  &::placeholder {
                    color: rgba(34, 34, 34, 0.5) !important;
                    opacity: 0.5 !important;
                  }
                  &:focus {
                    box-shadow: none !important;
                    border: none !important;
                    outline: none !important;
                    color: white !important;
                    height: 72px !important;
                    max-height: 72px !important;
                  }
                  &:focus-visible {
                    outline: none !important;
                    border: none !important;
                    box-shadow: none !important;
                  }
                  &, & * {
                    color: white !important;
                  }
                `}
                style={{
                  color: 'white',
                  height: '72px',
                  maxHeight: '72px',
                  overflowY: 'auto',
                  border: 'none',
                  outline: 'none',
                  boxShadow: 'none'
                }}
                onFocus={(e) => e.target.select()}
                onClick={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
              />
            </div>
          </div>
        )}
      </div>
      {/* Apply conditional opacity and pointer-events */}
      <Button
        css={`w-full mt-4 text-black rounded-full py-2.5 text-sm font-semibold transition-all duration-300 shadow-md shadow-amber-400/30
          bg-gradient-to-r from-amber-300 to-amber-500 hover:from-amber-500 hover:to-amber-600
          ${isSelected && !isGenerating ? 'opacity-100' : 'opacity-0 pointer-events-none'} // Hide if not selected OR generating
          flex items-center justify-center
          ${isInitiatingCreation ? 'cursor-wait' : ''} // Add cursor style when initiating
        `}
        onClick={handleActionClick}
        disabled={isGenerating || !isSelected || isInitiatingCreation} // Disable button if generating, not selected, or initiating locally
      >
        {/* --- Conditionally render based on isInitiatingCreation --- */}
        {isInitiatingCreation ? (
          <>
            Creating...
          </>
        ) : (
          <> {/* Original Content */}
            {isLiveCall && (
              <AudioLines className="inline-block w-5 h-5 mr-2 text-black" />
            )}
            {isVoiceNote && (
              <svg className="inline-block w-4 h-4 mr-2" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1C11.2044 1 10.4413 1.31607 9.87868 1.87868C9.31607 2.44129 9 3.20435 9 4V12C9 12.7956 9.31607 13.5587 9.87868 14.1213C10.4413 14.6839 11.2044 15 12 15C12.7956 15 13.5587 14.6839 14.1213 14.1213C14.6839 13.5587 15 12.7956 15 12V4C15 3.20435 14.6839 2.44129 14.1213 1.87868C13.5587 1.31607 12.7956 1 12 1Z" fill="black"/>
                <path d="M19 10V12C19 13.8565 18.2625 15.637 16.9497 16.9497C15.637 18.2625 13.8565 19 12 19C10.1435 19 8.36301 18.2625 7.05025 16.9497C5.7375 15.637 5 13.8565 5 12V10" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 19V23" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M8 23H16" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            )}
            {card.type === 'CREATE_GUIDED_SESSION' && (
              <SeedlingIcon className="inline-block w-5 h-5 mr-2 text-black" />
            )}
            {buttonText}
          </> 
        )}
        {/* --- End Conditional Render --- */}
      </Button>

      {/* Generating Overlay */} 
      {isGenerating && (
        // Remove border-*, apply new shadow animation class
        <div className="absolute inset-0 bg-black/100 rounded-xl flex items-center justify-center z-10 animate-pulse-overlay-shadow">
          {/* Inner div: Stacks loader and text vertically and centers them */}
          <div className="flex flex-col items-center">
          <Text className="text-[#FCA311] text-lg mb-4 text-center">
            Your tailored session
            <br />
            will be ready
            <br />
            in a minute...  
          </Text>
          </div>
        </div>
      )}

      {/* Voice note component */}
      {isVoiceNote && showVoiceRecorder && (
        <VoiceNote
          channelId={channelId}
          coachName={coachName}
          isOpen={showVoiceRecorder}
          onClose={() => setShowVoiceRecorder(false)}
          onRecordingComplete={handleVoiceRecorderComplete}
        />
      )}
    </div>
  );
};

export const SwipeableCards: React.FC<SwipeableCardsProps> = ({ cards, coachName, isLoading, onCardAction, channelId }) => {
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const cardElementsRef = useRef<Map<Element, number>>(new Map()); // Map element to index
  const selectedIndexRef = useRef<number>(selectedIndex); // Ref to track current index inside observer
  const [isDesktop, setIsDesktop] = useState(false); // State to track desktop
  // --- Add Ref to track scrolling to generating card --- 
  const didScrollToGeneratingRef = useRef(false);
  // --- End Add ---

  // Detect desktop on mount and resize
  useEffect(() => {
    const checkDesktop = () => {
      // Ensure window is defined (for SSR safety, though likely not needed here)
      if (typeof window !== 'undefined') {
        setIsDesktop(globalThis.matchMedia("(min-width: 768px)").matches);
      }
    };
    checkDesktop(); // Initial check
    globalThis.addEventListener("resize", checkDesktop);
    return () => globalThis.removeEventListener("resize", checkDesktop);
  }, []);

  // Keep the ref updated with the latest state
  useEffect(() => {
    selectedIndexRef.current = selectedIndex;
  }, [selectedIndex]);

  // Create debounced version of setSelectedIndex
  const debouncedSetSelectedIndex = useCallback(
    debounce((index: number) => {
      setSelectedIndex(index);
    }, 50), // Slightly increased debounce for smoother updates
    [] // Empty dependency array: debounce function itself doesn't change
  );

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container || cards.length === 0) return;

    // --- Shared: Calculate initial center index ---
    const initialCardElements = Array.from(container.children).filter(
      (child): child is HTMLElement => child instanceof HTMLElement && child.tagName.toLowerCase() !== 'style'
    );

    const calculateAndSetInitialCenter = () => {
        if (!container || initialCardElements.length === 0) return; // Guard against no elements

        const containerRect = container.getBoundingClientRect();
        // Use scrollLeft for calculations relative to the scrollable content
        const containerScrollAreaWidth = container.scrollWidth;
        // Visual center calculation needs to consider the padding effect on desktop
        const containerVisibleWidth = container.offsetWidth;
        const visualCenter = isDesktop
             ? container.scrollLeft + containerVisibleWidth / 2
             : container.scrollLeft + containerVisibleWidth / 2; // Same logic for mobile initially

        let initialCenterIndex = 0;
        let minDistance = Infinity;

        initialCardElements.forEach((el, index) => {
          const elRect = el.getBoundingClientRect(); // Use this for consistent coordinates if needed
          // Calculate center based on offsetLeft relative to the container's scroll position
          const elCenter = el.offsetLeft + el.offsetWidth / 2;
          const distance = Math.abs(elCenter - visualCenter);

          // console.log(`[Initial Center] Idx: ${index}, ElCenter: ${elCenter.toFixed(1)}, VisCenter: ${visualCenter.toFixed(1)}, Dist: ${distance.toFixed(1)}`);

          if (distance < minDistance) {
            minDistance = distance;
            initialCenterIndex = index;
          }
        });

        // console.log(`[Initial Center] Final MinDist: ${minDistance.toFixed(1)}, Setting Index: ${initialCenterIndex}`);
        if (initialCenterIndex !== selectedIndexRef.current) {
           setSelectedIndex(initialCenterIndex);
           selectedIndexRef.current = initialCenterIndex;
        }
    }
    // Run initial calculation after a short delay to allow layout to stabilize
    const initialTimeout = setTimeout(calculateAndSetInitialCenter, 50);
    // --- End Initial State Handling ---


    // --- Branching Logic ---
    let scrollListenerCleanup: (() => void) | null = null;
    let observerCleanup: (() => void) | null = null;

    if (isDesktop) {
      // --- Desktop: Use Throttled Scroll Event Listener ---
      // console.log("[SwipeableCards] Setting up Desktop Scroll Listener");

      const handleScroll = () => {
          if (!container) return;

          const containerVisibleWidth = container.offsetWidth;
          const visualCenter = container.scrollLeft + containerVisibleWidth / 2;

          let closestIndex = 0;
          let minDistance = Infinity;

          const currentCardElements = Array.from(container.children).filter(
             (child): child is HTMLElement => child instanceof HTMLElement && child.tagName.toLowerCase() !== 'style'
          );

          currentCardElements.forEach((el, index) => {
              const elCenter = el.offsetLeft + el.offsetWidth / 2;
              const distance = Math.abs(elCenter - visualCenter);
              if (distance < minDistance) {
                  minDistance = distance;
                  closestIndex = index;
              }
          });

          if (closestIndex !== selectedIndexRef.current) {
              // console.log(`[Scroll Handler] New closest index: ${closestIndex}`);
              // Use the existing debounced setter for consistency, though direct set might also work
              setSelectedIndex(closestIndex); // Use direct set for faster feedback on scroll end
              selectedIndexRef.current = closestIndex;
          }
      };

      // Throttle the scroll handler
      const throttledScrollHandler = throttle(handleScroll, 100); // Run at most every 100ms

      container.addEventListener('scroll', throttledScrollHandler, { passive: true });

      scrollListenerCleanup = () => {
          // console.log("[SwipeableCards] Cleaning up Desktop Scroll Listener");
          container.removeEventListener('scroll', throttledScrollHandler);
      };

    } else {
      // --- Mobile: Use Intersection Observer ---
      // console.log("[SwipeableCards] Setting up Mobile Intersection Observer");

      // Clear previous observer if switching from desktop
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      cardElementsRef.current.clear();

      const cardElements = Array.from(container.children).filter(
        (child): child is HTMLElement => child instanceof HTMLElement && child.tagName.toLowerCase() !== 'style'
      );
      cardElements.forEach((el, index) => cardElementsRef.current.set(el, index));


      const observerCallback = (entries: IntersectionObserverEntry[]) => {
          let mostVisibleEntry: IntersectionObserverEntry | null = null;
          for (const entry of entries) {
              if (entry.isIntersecting) {
                  if (!mostVisibleEntry || entry.intersectionRatio > mostVisibleEntry.intersectionRatio) {
                      mostVisibleEntry = entry;
                  }
              }
          }
          if (mostVisibleEntry) {
              const elementIndex = cardElementsRef.current.get(mostVisibleEntry.target);
              if (elementIndex !== undefined && elementIndex !== selectedIndexRef.current) {
                  debouncedSetSelectedIndex(elementIndex); // Keep debounce for mobile IO
              }
          }
      };

      const observerOptions: IntersectionObserverInit = {
        root: container,
        rootMargin: "0px", // Mobile uses standard margin
        threshold: 0.75 // Mobile uses higher threshold
      };

      observerRef.current = new IntersectionObserver(observerCallback, observerOptions);
      cardElements.forEach(el => observerRef.current?.observe(el));

      observerCleanup = () => {
          // console.log("[SwipeableCards] Cleaning up Mobile Intersection Observer");
          if (observerRef.current) {
            observerRef.current.disconnect();
            observerRef.current = null;
          }
          cardElementsRef.current.clear();
      };
    }

    // --- Cleanup ---
    return () => {
      clearTimeout(initialTimeout);
      scrollListenerCleanup?.();
      observerCleanup?.();
    };
  // Re-run setup if cards array, isDesktop state changes, or coachName changes (might imply card changes)
  }, [cards, debouncedSetSelectedIndex, isDesktop, coachName]); // Removed selectedIndex, handled internally by ref/initial calc

  // --- Update useEffect for auto-scrolling: Scroll RIGHT if GENERATING card exists --- 
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // Check if a generating card exists using .some()
    const isGenerating = cards.some(card => card.instance_status === 'GENERATING');

    if (isGenerating && !didScrollToGeneratingRef.current) {
      // Found a generating card and haven't scrolled yet
      console.log(`[SCROLL_GEN] GENERATING card detected. Scrolling container to end.`);
      
      // Scroll to the far right of the container
      container.scrollTo({
        left: container.scrollWidth, // Scroll to the total width
        behavior: 'smooth'
      });

      didScrollToGeneratingRef.current = true; // Mark as scrolled for this cycle
    } else if (!isGenerating && didScrollToGeneratingRef.current) {
      // No generating cards found, reset the flag for the next cycle
      console.log("[SCROLL_GEN] No GENERATING cards found. Resetting scroll flag.");
      didScrollToGeneratingRef.current = false;
    }
  }, [cards]); // Dependency: run whenever the cards array changes
  // --- End Update ---

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-60 sm:h-72 w-full"> {/* Modified height to be responsive */}
        {/* Use className for color and add parent pulse */}
        <Loader className="[&>*]:bg-[#FCA311] animate-pulse" />
      </div>
    );
  }

  if (!cards || cards.length === 0) {
    return (
      <div className="flex justify-center items-center h-60 sm:h-72 w-full"> {/* Modified height to be responsive */}
        <Text className="text-white/50">No interactions available right now.</Text>
      </div>
    );
  }

  return (
    <div
      ref={scrollContainerRef}
      data-swipeable-cards-root
      className={`relative w-full flex overflow-x-auto space-x-4 px-4 py-12 scroll-smooth snap-x snap-mandatory scrollbar-hide ${isDesktop ? 'bottom-12' : 'bottom-0'}`}
      style={{
        WebkitOverflowScrolling: "touch",
        height: 'calc(min(18rem, 60vh) + 2rem)', // Changed to use min function to adapt to screen height
        overflowY: 'hidden',
        touchAction: 'pan-x',
      }}
    >
      <style>
        {`
          /* Hide scrollbar styles */
          .scrollbar-hide::-webkit-scrollbar {
            display: none;
          }
          .scrollbar-hide {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
          }

          /* Desktop centering and scrollbar visibility */
          @media (min-width: 768px) {
            .snap-mandatory::-webkit-scrollbar {
              display: block;
              height: 8px;
            }
            .snap-mandatory {
              scrollbar-width: auto;
              scrollbar-color: rgba(252, 163, 17, 0.4) transparent;
              /* The padding creates the visual centering. 11rem is half the card's max-width (sm = 24rem / 2 = 12rem, slightly less for visual fit) */
              padding-left: calc(50% - 11rem);
              padding-right: calc(50% - 11rem);
            }
            .snap-mandatory::-webkit-scrollbar-thumb {
              background-color: rgba(252, 163, 17, 0.4);
              border-radius: 4px;
            }
             .snap-mandatory::-webkit-scrollbar-track {
              background: transparent;
            }
          }

          @media screen and (max-height: 600px) {
            /* Adjust card heights */
            .h-72 {
              height: 16rem !important; /* 240px instead of 288px */
            }
            
            /* Adjust textarea container for smaller cards */
            .h-18 {
              height: 64px !important; /* Reduce from 72px */
            }
            
            /* Adjust padding and spacing */
            .p-6 {
              padding: 1.20rem !important; /* 20px instead of 24px */
            }
            
            /* Make text smaller */
            .text-2xl {
              font-size: 1.50rem !important; /* Slightly smaller title */
            }
            
            .text-base {
              font-size: 0.975rem !important; /* Smaller description */
            }
          }

          /* --- Pulse Overlay Box Shadow Animation --- */
          @keyframes pulse-overlay-shadow {
            0%, 100% {
              /* Use box-shadow, inset makes it appear inside the rounded corners */
              /* Increased alpha from 0.4->0.6, spread 2px->3px, blur 6px->10px */
              box-shadow: inset 0 0 10px 3px rgba(252, 163, 17, 0.6); 
            }
            50% {
               /* Increased alpha from 0.7->0.9, spread 3px->4px, blur 10px->15px */
              box-shadow: inset 0 0 15px 4px rgba(252, 163, 17, 0.9); 
            }
          }

          .animate-pulse-overlay-shadow {
            animation: pulse-overlay-shadow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
          }

        `}
      </style>
      {cards.map((card: UserCardView, index: number) => (
        <Card
          key={card.user_card_id || card.coach_offering_id || `card-${index}`} // Fallback key
          channelId={channelId}
          card={card}
          coachName={coachName}
          isSelected={selectedIndex === index}
          isDesktop={isDesktop} // Pass down isDesktop state
          onAction={onCardAction} // Pass down the action handler
        />
      ))}
    </div>
  );
}; 