"use client";

import React, { useState, useRef, useEffect } from "npm:react@canary";
import { setSelectedCoach } from "./coach-utils.ts";

interface CoachSelectorProps {
  coaches: string[];
  selectedCoach: string;
  onCoachChange: (coachName: string) => void;
  userChannelId: string | number;
  showMiniPortrait?: boolean;
  onAvatarClick?: (coachName: string) => void;
}

// Function to convert coach name to portrait image URL
const getCoachPortraitUrl = (coachName: string): string => {
  const fileName = coachName.toLowerCase().replace(/\s+/g, '-') + '-portrait.png';
  return `https://storage.googleapis.com/awaken-audio-files/${fileName}`;
};

export const CoachSelector = ({ coaches, selectedCoach, onCoachChange, userChannelId, showMiniPortrait = false, onAvatarClick }: CoachSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleCoachSelection = async (coachName: string) => {
    // Update local state immediately for responsiveness
    onCoachChange(coachName);
    setIsOpen(false);

    // Update database in the background (don't await)
    setSelectedCoach(userChannelId, coachName)
      .then((success) => {
        if (!success) {
          console.error(`[COACH_SELECTOR] Failed to update coach to ${coachName} in DB.`);
          // Optional: Add logic here to potentially revert or notify user if needed
        }
      })
      .catch((err) => console.error("[COACH_SELECTOR] Error updating coach in DB:", err));
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Coach selector with small avatar */}
      <div className="flex justify-center">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`flex items-center space-x-2 text-[#FCA311] hover:text-amber-200 transition-colors ${showMiniPortrait ? '-translate-x-3' : ''}`}
        >
          {showMiniPortrait && (
            <div
              className="relative w-9 h-9 rounded-full overflow-hidden border border-amber-500/50 flex-shrink-0"
              onClick={(e) => {
                e.stopPropagation();
                onAvatarClick?.(selectedCoach);
              }}
            >
              <img
                src={getCoachPortraitUrl(selectedCoach)}
                alt={selectedCoach}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/default-coach-avatar.png';
                }}
              />
            </div>
          )}
          <span className="font-medium text-sm border-b border-amber-500/50 hover:border-amber-300 pb-0.5">
            {selectedCoach}
          </span>
        </button>
      </div>

      {/* Centered dropdown selector */}
      {isOpen && (
        <div className="absolute left-1/2 transform -translate-x-1/2 top-full mt-2 w-64 max-h-80 overflow-y-auto bg-amber-800/90 backdrop-blur-md rounded-xl border border-amber-500/30 shadow-lg z-40">
          <div className="p-2 grid grid-cols-2 gap-2">
            {coaches.map((coach) => (
              <button
                key={coach}
                className={`flex flex-col items-center p-3 rounded-lg transition-colors ${
                  selectedCoach === coach ? "bg-amber-600/50 border border-amber-500/50" : "hover:bg-amber-700/50"
                }`}
                onClick={() => handleCoachSelection(coach)}
              >
                <div className="relative w-12 h-12 rounded-full overflow-hidden mb-2">
                  <img 
                    src={getCoachPortraitUrl(coach)} 
                    alt={coach} 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/default-coach-avatar.png';
                    }}
                  />
                </div>
                <span className="text-white text-sm font-medium">{coach}</span>
                {/* You can add specialty info here if available in the coach data */}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
