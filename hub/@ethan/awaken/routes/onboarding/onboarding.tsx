"use client";

import { AnimatePresence } from "npm:framer-motion";
import { ParticleField } from "../../lib/particles.tsx";
import { useEffect, useRef, useState } from "npm:react@canary";
import React from "npm:react@canary";
import { Steps } from "./components/steps.tsx";


const rethinkSansFontUrl =
  "https://fonts.googleapis.com/css2?family=Rethink+Sans:ital,wght@0,400..800;1,400..800&display=swap";

const rethinkSansFontStyles = `
  @import url('${rethinkSansFontUrl}');
  .root-component {
    font-family: "Rethink Sans", sans-serif;
  }
`;

export const Onboard = ({ user }) => {
  console.log("user ", user);
  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = rethinkSansFontStyles;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div className="relative min-h-svh max-h-svh touch-none bg-black overflow-hidden root-component">
      <ParticleField />
      <AnimatePresence mode="wait">
        <Steps key="onboarding-steps" user={user} />
      </AnimatePresence>
    </div>
  );
};
