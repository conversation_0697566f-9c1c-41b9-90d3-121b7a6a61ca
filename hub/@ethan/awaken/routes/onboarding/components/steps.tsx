"use client";

import { useEffect, useState } from "npm:react@canary";
import React from "npm:react@canary";
import {
  Button,
  Input,
  Textarea,
} from "@reframe/ui/main.tsx";

import { motion, AnimatePresence } from "npm:framer-motion";

import { ArrowRightIcon } from "@reframe/icons/arrow-right.ts";
import { CheckIcon } from "@reframe/icons/check.ts";
import { ArrowLeftIcon } from "@reframe/icons/arrow-left.ts";
import { Questions as QuestionsConstant, Testimonials } from "../../../lib/constants.ts";

// Cast Questions data with the correct type for numeric indexing
const QuestionsData = QuestionsConstant as unknown as QuestionsType;
import { LoaderIcon } from "../../../../../@reframe/icons/loader.ts";
import { completeOnboarding } from "../../../action.ts";

// Type definitions
// Define Questions map type to match actual implementation in constants.ts
type QuestionsType = {
  [key: number]: {
    title: string;
    answers: string[];
    multiple?: boolean;
  };
};

type QuestionType = {
  title: string;
  answers: string[];
  multiple?: boolean;
};

type QuestionAnswerType = {
  [key: number]: string[];
};

type TestimonialType = {
  text: string;
  author: string;
};

type QuestionComponentProps = {
  question: QuestionType;
  questionNo: number;
  setQuestionNo: React.Dispatch<React.SetStateAction<number>>;
  setQuestion: React.Dispatch<React.SetStateAction<QuestionType>>;
  channelId: string;
};

type QuestionTitleProps = {
  questionNo: number;
  title: string;
};

type StepsProps = {
  user: {
    channelId: string;
    [key: string]: unknown; // Using unknown instead of any for better type safety
  };
};

const rethinkSansFontUrl =
  "https://fonts.googleapis.com/css2?family=Rethink+Sans:ital,wght@0,400..800;1,400..800&display=swap";

const rethinkSansFontStyles = `
  @import url('${rethinkSansFontUrl}');

  /* Use the font-family */
  .component-a {
    font-family: "Rethink Sans", sans-serif;
  }
`;

const QuestionCompoent = ({
  question,
  questionNo,
  setQuestionNo,
  setQuestion,
  channelId,
}: QuestionComponentProps) => {
  const [isExiting, setIsExiting] = useState(false);

  const [loading, setLoading] = useState<boolean>(false);

  const [questionAnswer, setQuestionAnswer] = useState<QuestionAnswerType>({
    1: [],
    2: [],
    3: [],
    4: [],
    5: [],
    6: [],
    7: [],
    8: [],
  });

  const handleNextQuestion = async () => {
    if (questionNo === Math.max(...Object.keys(QuestionsData).map(Number))) {
      setLoading(true);
      console.log("QuestionAnswer", questionAnswer);

      // get the base url
      const baseUrl = globalThis.location.origin;

      const saveQuestionAnswer = await completeOnboarding(channelId, questionAnswer);

      console.log("saveQuestionAnswer ", saveQuestionAnswer);

      if (saveQuestionAnswer.success) globalThis.location.replace(`${baseUrl}/chat`);

      return;
    }

    // setIsExiting(true); // Start exit animation
    setTimeout(() => {
      setQuestionNo((prev) => prev + 1); // Increment after animation completes
      setQuestion(QuestionsData[questionNo + 1] as QuestionType); // Update the question
      setIsExiting(false); // Reset the exit state
    }, 1); // Delay matches exit animation duration
  };

  const handlePreviousQuestion = () => {
    setIsExiting(true); // Start exit animation
    setTimeout(() => {
      setQuestionNo((prev) => prev - 1); // Decrement after animation completes
      setQuestion(QuestionsData[questionNo - 1] as QuestionType); // Update the question
      setIsExiting(false); // Reset the exit state
    }, 1); // Delay matches exit animation duration
  };

  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // Only measure screen size after component has mounted (client-side only)
  useEffect(() => {
    // Safe check to ensure we're in browser environment
    if (typeof globalThis !== 'undefined') {
      setIsSmallScreen(globalThis.innerWidth <= 768);
      
      const handleResize = () => {
        setIsSmallScreen(globalThis.innerWidth <= 768);
      };
      
      globalThis.addEventListener('resize', handleResize);
      return () => globalThis.removeEventListener('resize', handleResize);
    }
  }, []);

  // Use React CSSProperties type for the style object
  const stickyButtonStyle: React.CSSProperties = isSmallScreen
    ? {
        position: questionNo === 3 ? "fixed" : "sticky",
        bottom: "0",
        backgroundColor: "black", 
        width: "100%",
        zIndex: 50,
        textAlign: "center", 
        padding: questionNo === 3 ? "1rem" : "0",
      }
    : {};
  return (
    <>
      {!isExiting && (
        <motion.div
          key={questionNo}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
          exit={{ opacity: 0 }}
          className="flex flex-col items-center"
        >
          <QuestionTitle questionNo={questionNo} title={question.title} />
          <div className="flex flex-col lg:flex-row lg:flex-wrap justify-center gap-2 mb-4 w-full px-4 md:px-0">
            {question.answers.length > 0 ? (
              question.answers.map((answer) => (
                <button
                  type="button"
                  key={answer}
                  className={`p-4 rounded-full text-xs md:text-sm font-light transition-colors duration-300 ease-in-out flex items-center justify-center ${
                    questionAnswer[questionNo].includes(answer)
                      ? "bg-[#FCA311]  text-black"
                      : "bg-[#00000033] hover:bg-[#00000044] text-white"
                  }`}
                  onClick={() => {
                    if (questionAnswer[questionNo].includes(answer)) {
                      setQuestionAnswer({
                        ...questionAnswer,
                        [questionNo]: questionAnswer[questionNo].filter(
                          (ans) => ans !== answer
                        ),
                      });
                    } else {
                      if (QuestionsData[questionNo]?.multiple) {
                        setQuestionAnswer({
                          ...questionAnswer,
                          [questionNo]: [...questionAnswer[questionNo], answer],
                        });
                      } else {
                        setQuestionAnswer({
                          ...questionAnswer,
                          [questionNo]: [answer],
                        });
                      }
                    }
                  }}
                >
                  {answer}
                  {questionAnswer[questionNo].includes(answer) &&
                    QuestionsData[questionNo]?.multiple && (
                      <span className="ml-2">
                        {/* @ts-ignore - ignoring component type mismatch */}
                        <CheckIcon className="w-4 h-4 stroke-2" />
                      </span>
                    )}
                </button>
              ))
            ) : questionNo == 3 ? (
              <Textarea
                placeholder="Write your thoughts"
                css="bg-[#00000033] min-h-[105px] text-white rounded-lg p-3 w-full md:w-3/5 focus:outline-none"
                value={questionAnswer[questionNo][0]}
                outline={false}
                bordered={false}
                onChange={(e) => {
                  setQuestionAnswer({
                    ...questionAnswer,
                    [questionNo]: [e.target.value],
                  });
                }}
              />
            ) : (
              <Input
                placeholder="Type here..."
                css="bg-[#00000033] text-white rounded-full p-3 w-[250px] focus:outline-none"
                value={questionAnswer[questionNo][0]}
                outline={false}
                bordered={false}
                onChange={(e) => {
                  setQuestionAnswer({
                    ...questionAnswer,
                    [questionNo]: [e.target.value],
                  });
                }}
              />
            )}
          </div>

          {/* Navigation buttons */}
          <div style={stickyButtonStyle}>
            <div className="flex gap-4 mt-8 justify-center">
              {questionNo === 1 ? null : (
                <Button
                  disabled={questionNo === 1 || loading}
                  variant="outline"
                  css="mt-2 px-6 py-2 bg-gradient-to-r from-[#00000033] to-[#5C3B0633] border-[#FCA311] text-white rounded-full flex items-center space-x-2 transition-colors z-10 hover:bg-[#5C3B0633] hover:border-[#FCA311] opacity-50 hover:opacity-100"
                  onClick={() => {
                    handlePreviousQuestion();
                  }}
                >
                  {/* @ts-ignore - ignoring component type mismatch */}
                  <ArrowLeftIcon className="w-4 h-4 stroke-2" />
                </Button>
              )}
              <Button
                variant="outline"
                disabled={questionAnswer[questionNo].length === 0 || loading || questionAnswer[questionNo][0] === ""}
                css="mt-2 px-6 py-2 bg-gradient-to-r from-[#00000033] to-[#5C3B0633] border-[#FCA311] text-white rounded-full flex items-center space-x-2 transition-colors z-10 hover:bg-[#5C3B06] hover:border-[#FCA311]"
                onClick={async () => {
                  await handleNextQuestion();
                }}
              >
                {loading ? (
                  /* @ts-ignore - ignoring component type mismatch */
                  <LoaderIcon size={24} className="animate-spin" />
                ) : (
                  <>
                    <span>Continue</span>
                    {/* @ts-ignore - ignoring component type mismatch */}
                    <ArrowRightIcon className="w-4 h-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </motion.div>
      )}
    </>
  );
};

const QuestionTitle = ({ questionNo, title }: QuestionTitleProps) => (
  <div className="mb-4 mt-10 md:mt-0 lg:text-sm text-xs p-4">
    <span className="text-[#FCA311]">Step {questionNo} / </span>{" "}
    <span className="text-white">
      {" "}
      {Math.max(...Object.keys(QuestionsData).map(Number))}
    </span>
    <h2 className="lg:text-2xl text-lg font-normal">{title}</h2>
  </div>
);

export const Steps = ({ user }: StepsProps) => {
  const [questionNo, setQuestionNo] = useState<number>(1);
  const [question, setQuestion] = useState<QuestionType>(QuestionsData[1] as QuestionType);
  const [currentTestimonial, setCurrentTestimonial] = useState<number>(0);

  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = rethinkSansFontStyles;
    document.head.appendChild(style);
    
    const testimonialInterval = setInterval(() => {
      setCurrentTestimonial((prevIndex) =>
        prevIndex === Testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 7500); // 5 seconds
    return () => {
      document.head.removeChild(style);
      clearInterval(testimonialInterval);
    };
  }, []);

  return (
    <motion.div
      key="onboard"
      className="relative flex flex-col items-center min-h-svh text-white p-4 gap-12 lg:gap-28 overflow-auto"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }} // Ensures smooth fade-out
      transition={{ duration: 1 }}
    >
      {/* Upper part with logo and testimonial */}
      <div className="items-center flex flex-col mt-12 gap-8">
        {/* Testimonial */}
        <div className="absolute w-full flex flex-col items-center justify-center">
          {/* SVG absolutely positioned in the background */}
          <div className="absolute inset-0 flex justify-center items-center">
            <svg
              width="90"
              height="100"
              viewBox="0 0 108 96"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                opacity="0.1"
                d="M48.4034 66L75.0252 0H96L80.9412 66H48.4034ZM0 66L26.3529 0H47.3277L32.5378 66H0Z"
                fill="#FCA311"
              />
            </svg>
          </div>

          {/* Testimonial text in front of SVG */}

          <AnimatePresence mode="wait" initial={false}>
            <motion.div
              key={`testimonial-${currentTestimonial}`}
              initial={{ opacity: 0, x: -50 }} // Fade in with slight slide from left
              animate={{ opacity: 1, x: 0 }} // Fully visible and centered
              exit={{ opacity: 0, x: 80 }} // Fade out with slight slide to the right
              transition={{ duration: 1.5, ease: "easeInOut" }} // Smooth transition without delay to fix mount issues
              className="absolute mb-4 flex flex-col justify-center items-center gap-1 rounded-full"
            >
              <p className="lg:text-sm text-[12px] max-w-72 md:max-w-xl font-extralight text-[#C5C5C5] text-center">
                {Testimonials[currentTestimonial].text}
              </p>
              <p className="lg:text-sm text-[12px] text-[#FCA311] text-center">
                - {Testimonials[currentTestimonial].author}
              </p>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      {/* Blurred circle background */}
      <div
        className="absolute inset-0 mx-auto md:m-auto -top-40 md:top-0 w-[367px] h-[367px] md:w-[500px] md:h-[500px] rounded-full transition-colors duration-100 ease-in-out"
        style={{
          background: "linear-gradient(135deg, #FF5727 0%, #FFC360 100%)",
          opacity: 0.5,
          filter: "blur(80px)"
        }}
        aria-hidden="true"
      />

      <div
        className="absolute inset-0 m-auto md:hidden w-[367px] h-[367px] md:w-[500px] md:h-[500px] rounded-full transition-colors duration-100 ease-in-out"
        style={{
          background: "linear-gradient(135deg, #FF5727 0%, #FFC360 100%)",
          opacity: 0.3,
          filter: "blur(80px)",
        }}
        aria-hidden="true"
      />

      {/* Questions */}
      <div className="absolute m-[15%] flex flex-col items-center text-center flex-grow w-full">
        <div className="relative w-full flex flex-col items-center">
          <div
            className="relative mb-4 z-10 w-full text-center max-w-3xl"
          >
            <QuestionCompoent
              question={question}
              questionNo={questionNo}
              setQuestionNo={setQuestionNo}
              setQuestion={setQuestion}
              channelId={user.channelId}
            />
          </div>
        </div>
      </div>
    </motion.div>
  );
};
