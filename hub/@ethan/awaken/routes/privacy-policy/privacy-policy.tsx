"use client";

import React from "npm:react@canary";
import { Logo } from "../../lib/logo.tsx";
import { Text, ScrollArea } from "@reframe/ui/main.tsx";
import { motion } from "npm:framer-motion";

export const PrivacyPolicy = () => {
  const handleNavigateHome = () => {
    // Close the current tab if it was opened from the payment overlay
    if (globalThis.opener && !globalThis.opener.closed) {
      globalThis.close();
    } else {
      // Otherwise navigate to the home page
      globalThis.location.href = "/";
    }
  };

  return (
    <div
      className="
        relative min-h-screen text-white flex flex-col items-center
        before:absolute before:inset-0 before:-z-10
        before:bg-[radial-gradient(circle_at_center,_rgba(252,163,17,0.3)_0%,_rgba(0,0,0,0)_70%)]
        bg-black
      "
    >
      {/* Header with glow effect */}
      <div className="w-full relative">
        <motion.div
          className="absolute w-[500px] h-[300px] rounded-full blur-[100px] opacity-40 z-0"
          style={{
            top: "-150px",
            left: "50%",
            transform: "translateX(-50%)",
            background: "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
          }}
        />
      </div>

      {/* Top navigation bar */}
      <div className="w-full max-w-4xl flex items-center justify-between px-6 pt-8 pb-4 z-10 relative">
        <div 
          className="flex items-center gap-2 cursor-pointer group" 
          onClick={handleNavigateHome}
        >
          <Logo size={24} />
          <span className="text-base font-normal text-white group-hover:text-[#FCA311] transition-colors">
            awaken
          </span>
        </div>
      </div>

      {/* Content container */}
      <div className="w-full max-w-4xl px-6 pb-20 flex flex-col z-10 relative">
        {/* Title section with more vertical spacing */}
        <div className="text-center mb-12 mt-4">
          <Text className="text-3xl md:text-4xl font-semibold text-[#FCA311] mb-3">
            Privacy Policy
          </Text>
          <div className="h-0.5 w-16 bg-gradient-to-r from-[#FCA311] to-[#FF5727] mx-auto mb-3"></div>
          <Text className="text-sm text-white/60">
            Last updated: March 4, 2025
          </Text>
        </div>

        {/* Scroll area with content */}
        <ScrollArea className="w-full flex-1 overflow-auto pr-2">
          <div className="space-y-10 max-w-3xl mx-auto">
            <section>
              <p className="text-white/80 mb-4 leading-relaxed">
                Welcome to <strong>Awaken</strong>, a product of <strong>Pureplay Studios Ltd, The Growth Doctors Ltd</strong>, and <strong>Creating LLC</strong> ("Awaken," "we," "us," "our"). We operate the website located at <strong>awaken.is</strong> (the "Site") and a web-based application designed to provide AI-driven coaching, reflection, and personal growth features (collectively, the "Service"). We respect your privacy and are committed to protecting your personal data. This Privacy Policy explains how we collect, use, disclose, and protect the information you provide when you use our Site and Service.
              </p>
              <p className="text-white/80 mb-4 leading-relaxed">
                If anything is unclear, or if you have any questions, user in the United Kingdom, please contact us at:
              </p>
              
              <div className="bg-[#1E1E1E]/30 rounded-lg p-4 border border-[#FCA311]/10 mb-4">
                <p className="text-white/90 leading-relaxed">
                  <strong>Pureplay Studios Ltd</strong><br />
                  Suite LP7356<br />
                  20-22 Wenlock Road<br />
                  London N1 7GU<br />
                  Email: <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a>
                </p>
              </div>
              
              <div className="bg-[#1E1E1E]/30 rounded-lg p-4 border border-[#FCA311]/10 mb-4">
                <p className="text-white/90 leading-relaxed">
                  <strong>The Growth Doctors Ltd</strong><br />
                  Greystones<br />
                  Langford Road<br />
                  Lower Langford BS40 5HU<br />
                  Email: <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a>
                </p>
              </div>
              
              <p className="text-white/80 mb-4 leading-relaxed">
                For U.S. users, you may also contact our U.S. agent:
              </p>
              
              <div className="bg-[#1E1E1E]/30 rounded-lg p-4 border border-[#FCA311]/10">
                <p className="text-white/90 leading-relaxed">
                  <strong>Creating LLC</strong><br />
                  131 Continental Drive, Suite 305<br />
                  Newark, DE 19713<br />
                  Email: <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a>
                </p>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">1</span>
                Information We Collect
              </h2>
              <p className="text-white/80 mb-4 leading-relaxed">
                We may collect the following information when you use our Service:
              </p>
              
              <div className="space-y-4 mb-4">
                <div className="bg-[#1E1E1E]/30 rounded-lg p-4 border border-[#FCA311]/10">
                  <p className="text-[#FCA311] font-medium mb-1">1.1 Personal Information</p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li className="text-white/80 leading-relaxed">
                      <strong>Account Information:</strong> Name, email address, and other details necessary for account creation and maintenance.
                    </li>
                    <li className="text-white/80 leading-relaxed">
                      <strong>User-Generated Content:</strong> Text, audio recordings, or other content you enter into Awaken for AI coaching.
                    </li>
                  </ul>
                </div>
                
                <div className="bg-[#1E1E1E]/30 rounded-lg p-4 border border-[#FCA311]/10">
                  <p className="text-[#FCA311] font-medium mb-1">1.2 Automatic Data Collection</p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li className="text-white/80 leading-relaxed">
                      <strong>Device & Usage Data:</strong> Information such as IP address, browser type, operating system, referring URLs, and activity logs.
                    </li>
                    <li className="text-white/80 leading-relaxed">
                      <strong>Cookies & Tracking Technologies:</strong> We use cookies and similar tools for personalization, analytics, and security. You may manage cookie settings via your browser.
                    </li>
                  </ul>
                </div>
                
                <div className="bg-[#1E1E1E]/30 rounded-lg p-4 border border-[#FCA311]/10">
                  <p className="text-[#FCA311] font-medium mb-1">1.3 Information from Third Parties</p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li className="text-white/80 leading-relaxed">
                      <strong>Analytics Providers & Payment Processors:</strong> We may receive data from service providers assisting with operations.
                    </li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">2</span>
                How We Use Your Information
              </h2>
              <p className="text-white/80 mb-4 leading-relaxed">
                We use collected information for:
              </p>
              <ul className="list-none space-y-2 mb-3">
                {[
                  "<strong>Service Delivery & Improvement:</strong> Maintaining accounts, processing AI-driven content, and troubleshooting.",
                  "<strong>Communication:</strong> Sending service-related updates, responding to inquiries, and offering support.",
                  "<strong>Analytics & Development:</strong> Understanding usage patterns to enhance features.",
                  "<strong>Legal Compliance & Security:</strong> Meeting legal obligations, fraud prevention, and data security."
                ].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <span className="h-6 w-6 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 flex items-center justify-center text-xs text-[#FCA311] mr-3 mt-0.5 flex-shrink-0">✓</span>
                    <span className="text-white/80" dangerouslySetInnerHTML={{ __html: item }}></span>
                  </li>
                ))}
              </ul>
              <p className="text-white/80 mt-4 leading-relaxed">
                <strong>For U.S. users, Awaken complies with the California Consumer Privacy Act (CCPA) and other applicable state and federal laws.</strong>
              </p>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">3</span>
                Safeguarding Your Information
              </h2>
              <p className="text-white/80 mb-4 leading-relaxed">
                We are committed to safeguarding your personal data. We will never sell your personal data.
              </p>
              <ul className="list-none space-y-2 mb-3">
                {[
                  "<strong>Service Providers:</strong> We share non-personally-identifiable data with AI processing partners, including but not limited to OpenAI, Google, Microsoft Azure, Amazon Web Services (AWS), Anthropic, Cohere, and Mistral, as well as transcription tools. We always opt out of model training where this option is provided by our AI Processing partners. We cannot be held liable for how these AI providers handle data shared with them.",
                  "<strong>Analytics Tools:</strong> We share anonymized data for analytical purposes.",
                  "<strong>Legal Compliance:</strong> We may disclose information as required by law.",
                  "<strong>Business Transactions:</strong> In case of mergers or acquisitions, data may be transferred with notice."
                ].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <span className="h-6 w-6 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 flex items-center justify-center text-xs text-[#FCA311] mr-3 mt-0.5 flex-shrink-0">•</span>
                    <span className="text-white/80" dangerouslySetInnerHTML={{ __html: item }}></span>
                  </li>
                ))}
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">4</span>
                Data Storage & Security
              </h2>
              <div className="space-y-4">
                <div className="bg-[#1E1E1E]/30 rounded-lg p-4 border border-[#FCA311]/10">
                  <p className="text-white/90 leading-relaxed">
                    <span className="text-[#FCA311]">🔒 </span>
                    <strong>Encryption:</strong> Data is encrypted at rest and in transit.
                  </p>
                </div>
                
                <div className="bg-[#1E1E1E]/30 rounded-lg p-4 border border-[#FCA311]/10">
                  <p className="text-white/90 leading-relaxed">
                    <span className="text-[#FCA311]">🔒 </span>
                    <strong>Access Controls:</strong> We use Google Authentication as our sole sign-in method. All user access is managed through Google's authentication infrastructure, ensuring secure account protection. We do not store passwords or authentication credentials on our servers.
                  </p>
                </div>
                
                <div className="bg-[#1E1E1E]/30 rounded-lg p-4 border border-[#FCA311]/10">
                  <p className="text-white/90 leading-relaxed">
                    <span className="text-[#FCA311]">🔒 </span>
                    <strong>Data Breach Notification:</strong> In the event of a data breach, we will notify affected users as required by law.
                  </p>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">5</span>
                Data Retention
              </h2>
              <p className="text-white/80 mb-4 leading-relaxed">
                We retain personal data only as necessary. You may request deletion of your data by contacting <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a>. Some residual data may remain in backups.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">6</span>
                Your Rights
              </h2>
              <p className="text-white/80 mb-4 leading-relaxed">
                Depending on your location, you may have rights including:
              </p>
              <ul className="list-none space-y-2 mb-3">
                {[
                  "<strong>Access & Correction:</strong> Requesting access to or correction of personal data.",
                  "<strong>Deletion:</strong> Requesting data deletion where legally permissible.",
                  "<strong>Opt-Out of Data Sharing:</strong> U.S. users may opt out of personal data sales under the CCPA."
                ].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <span className="h-6 w-6 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 flex items-center justify-center text-xs text-[#FCA311] mr-3 mt-0.5 flex-shrink-0">•</span>
                    <span className="text-white/80" dangerouslySetInnerHTML={{ __html: item }}></span>
                  </li>
                ))}
              </ul>
              <p className="text-white/80 mt-4 leading-relaxed">
                To exercise your rights, contact us at <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a>.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">7</span>
                Children's Privacy
              </h2>
              <p className="text-white/80 mb-4 leading-relaxed">
                The Service is <strong>not intended for children under 13</strong>. If you believe we have collected data from a child, please contact us for removal at <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a>.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">8</span>
                International Data Transfers
              </h2>
              <p className="text-white/80 mb-4 leading-relaxed">
                Your data may be processed outside your country. We ensure compliance with international privacy standards.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">9</span>
                AI Usage Notice & Disclaimer
              </h2>
              <div className="bg-[#1E1E1E]/30 rounded-lg p-4 border border-[#FCA311]/10">
                <p className="text-white/90 leading-relaxed">
                  Awaken uses AI-driven features, and responses may not always be accurate. <strong>AI-generated content is not a substitute for professional, medical, legal, or mental health advice.</strong> Users should verify critical information independently.
                </p>
              </div>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">10</span>
                Changes to This Privacy Policy
              </h2>
              <p className="text-white/80 mb-4 leading-relaxed">
                We may update this policy periodically. Users will be notified of significant changes.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">11</span>
                Contact Us
              </h2>
              <p className="text-white/80 mb-4 leading-relaxed">
                For any privacy concerns, reach us at:
              </p>
              <ul className="list-none space-y-2 mb-3">
                {[
                  "<strong>Email:</strong> <a href='mailto:<EMAIL>' class='text-[#FCA311] hover:underline'><EMAIL></a>",
                  "<strong>U.K. Office:</strong> Pureplay Studios Ltd, 20-22 Wenlock Road, London N1 7GU",
                  "<strong>U.K. Office:</strong> The Growth Doctors Ltd, Greystones, Langford Road, Lower Langford BS40 5HU",
                  "<strong>U.S. Agent:</strong> Creating LLC, 131 Continental Drive, Suite 305, Newark, DE 19713, USA"
                ].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <span className="h-6 w-6 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 flex items-center justify-center text-xs text-[#FCA311] mr-3 mt-0.5 flex-shrink-0">•</span>
                    <span className="text-white/80" dangerouslySetInnerHTML={{ __html: item }}></span>
                  </li>
                ))}
              </ul>
              <p className="text-white/80 mt-4 leading-relaxed">
                This policy ensures compliance with <strong>CCPA, GDPR, FTC AI transparency rules, and international privacy regulations</strong>.
              </p>
            </section>
            
            {/* Footer with subtle separator */}
            <div className="pt-10 mt-10 border-t border-white/10 text-center">
              <p className="text-white/40 text-sm">
                © {new Date().getFullYear()} Awaken. All rights reserved.
              </p>
            </div>
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};