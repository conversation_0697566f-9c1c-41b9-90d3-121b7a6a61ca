"use client";

import React from "npm:react@canary";
import { Layout } from "../../../lib/layout.tsx";
import { Link } from "@reframe/ui/main.tsx";
import { BrainIcon, UsersIcon } from "../../../lib/icons.tsx";

export interface AdminDashboardProps {
  user: any;
  canAccessAnalysis: boolean;
  canAccessChat: boolean;
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({ 
  user, 
  canAccessAnalysis, 
  canAccessChat 
}) => {
  const availableCards = [];

  if (canAccessAnalysis) {
    availableCards.push(
      <Link
        key="analysis"
        to="/admin/analysis"
        className="group rounded-2xl border border-white/10 bg-white/5 hover:bg-white/10 transition-colors p-8 flex flex-col items-center justify-center text-center"
      >
        <BrainIcon className="w-14 h-14 text-orange-400 group-hover:scale-105 transition-transform" />
        <h2 className="mt-4 text-2xl font-semibold text-white">Chat Analysis</h2>
        <p className="mt-2 text-gray-300 text-sm max-w-xs">
          Use AI to analyze user conversations and gain insights.
        </p>
      </Link>
    );
  }

  if (canAccessChat) {
    availableCards.push(
      <Link
        key="chat"
        to="/admin/chat"
        className="group rounded-2xl border border-white/10 bg-white/5 hover:bg-white/10 transition-colors p-8 flex flex-col items-center justify-center text-center"
      >
        <UsersIcon className="w-14 h-14 text-orange-400 group-hover:scale-105 transition-transform" />
        <h2 className="mt-4 text-2xl font-semibold text-white">Coach Chat</h2>
        <p className="mt-2 text-gray-300 text-sm max-w-xs">
          Browse and respond to user messages across channels.
        </p>
      </Link>
    );
  }

  // Determine grid layout based on number of cards
  const gridClass = availableCards.length === 1 
    ? "grid grid-cols-1 justify-items-center w-full max-w-md mx-auto" 
    : "grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-3xl";

  return (
    <Layout user={user} userData={null} currentPath="admin-dashboard" keepDrawer={false}>
      <div className="flex-1 flex items-center justify-center p-6">
        <div className={gridClass}>
          {availableCards}
        </div>
      </div>
    </Layout>
  );
}; 