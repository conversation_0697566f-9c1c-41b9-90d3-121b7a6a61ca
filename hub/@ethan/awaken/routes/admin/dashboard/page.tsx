"use server";

import { AdminDashboard } from "./admin-dashboard.tsx";
import { SUPERUSER_EMAILS } from "../../../lib/server-constants.ts";
import { getAuthenticatedUser } from "../../../lib/auth-helper.ts";
import { getCoachByEmail } from "../actions/admin-actions.ts";
import { Redirect } from "@reframe/ui/main.tsx";

export const AdminDashboardPage = async () => {
  const user = await getAuthenticatedUser();

  if (!user) {
    return <Redirect to="/auth/sign-in" />;
  }

  // Check permissions
  const isSuperUser = SUPERUSER_EMAILS.includes(user.email);
  
  let isCoach = false;
  if (!isSuperUser) {
    // Check if user is a coach
    const coach = await getCoachByEmail(user.email);
    isCoach = !!coach;
  }

  // If user has neither permission, redirect to chat
  if (!isSuperUser && !isCoach) {
    return <Redirect to="/chat" />;
  }

  return <AdminDashboard 
    user={user} 
    canAccessAnalysis={isSuperUser} 
    canAccessChat={isSuperUser || isCoach} 
  />;
}; 