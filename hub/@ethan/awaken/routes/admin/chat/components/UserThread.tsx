"use client";

import { useEffect, useRef, useState } from "npm:react@canary";
import { Button, ScrollArea, Text } from "@reframe/ui/main.tsx";
import { CircleXIcon } from "@reframe/icons/circle-x.ts";
import { MessageItem } from "./MessageItem.tsx";
import { markThreadMessagesSeenByCoach, sendCoachMessage } from "../../actions/admin-actions.ts";
import { createPortal } from "npm:react-dom@canary";
import { animated, useTransition } from "npm:@react-spring/web@9.7.3";

interface ThreadProps {
  user: User;
  visible: boolean;
  onClose: () => void;
  messages: any[]; // coach_message only
  initialMessageId: string | null;
  coachName: string;
  channelId: number | string;
}

/**
 * An overlay that displays all `coach_message` items between a coach and a user.
 * Copied from the user-side `CoachThread` component and adapted for the admin view.
 */
export const UserThread: React.FC<ThreadProps> = ({
  user,
  visible,
  onClose,
  messages,
  initialMessageId,
  coachName,
  channelId,
}) => {
  // Do not attempt to render on the server
  if (typeof document === "undefined") return null;

  const [threadMsgs, setThreadMsgs] = useState(messages);

  /**
   * Local state for tracking which message should be highlighted. We start with
   * `initialMessageId` (if any) received from the parent component. After
   * 3 seconds the highlight is cleared so the UI returns to normal.
   */
  const [highlightId, setHighlightId] = useState<string | null>(null);

  // Ref to store the timeout so it can be cleared on unmount or visibility toggle
  const highlightTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Synchronise highlight state with visibility and selected anchor
  useEffect(() => {
    // Whenever visibility changes, reset highlight state accordingly
    if (visible) {
      setHighlightId(initialMessageId);
    } else {
      setHighlightId(null);
    }
  }, [visible, initialMessageId]);

  // Manage highlight timeout – clear any existing timer first, then start a new one
  useEffect(() => {
    if (highlightTimerRef.current) {
      clearTimeout(highlightTimerRef.current);
      highlightTimerRef.current = null;
    }

    if (highlightId) {
      highlightTimerRef.current = setTimeout(() => {
        setHighlightId(null);
        highlightTimerRef.current = null;
      }, 3000);
    }

    // Clean-up on unmount
    return () => {
      if (highlightTimerRef.current) {
        clearTimeout(highlightTimerRef.current);
        highlightTimerRef.current = null;
      }
    };
  }, [highlightId]);

  // Mark unseen thread messages as seen by the coach once the overlay is visible
  useEffect(() => {
    if (!visible) return;

    const hasUnseen = messages.some(
      (m) => m.sender === "user" && !m.seenByCoach
    );
    if (!hasUnseen) return;

    // Update on the server
    markThreadMessagesSeenByCoach(channelId, coachName)
      .catch((err) => console.error("Failed to mark thread messages seen", err));

    // Update locally so the UI reflects the change immediately
    setThreadMsgs((prev) =>
      prev.map((m) =>
        m.sender === "user" && !m.seenByCoach ? { ...m, seenByCoach: true } : m
      )
    );
  }, [visible, messages, channelId, coachName]);

  // Sync incoming message list while the overlay is open
  useEffect(() => {
    if (visible && messages.length > 0) {
      setThreadMsgs(messages);
    }
  }, [visible, messages]);

  const [input, setInput] = useState("");
  const scrollRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Scroll the highlighted message (if any) into view whenever it changes
  useEffect(() => {
    if (!scrollRef.current || !highlightId) return;
    const timer = setTimeout(() => {
      const el = scrollRef.current!.querySelector(
        `[data-thread-id="${highlightId}"]`
      ) as HTMLElement | null;
      if (el) {
        el.scrollIntoView({ block: "center" });
      }
    }, 100);
    return () => clearTimeout(timer);
  }, [highlightId, threadMsgs]);

  // Automatically scroll to the bottom when the overlay becomes visible and no
  // specific anchor was provided (e.g. opened via the header button).
  useEffect(() => {
    if (!visible || !scrollRef.current) return;
    if (initialMessageId) return; // anchor scroll will handle positioning

    // Allow the DOM to finish rendering then jump to bottom
    requestAnimationFrame(() => {
      if (scrollRef.current) {
        scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
      }
    });
  }, [visible, initialMessageId, threadMsgs]);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;
    textarea.style.height = "auto";
    textarea.style.height = `${Math.min(textarea.scrollHeight, 150)}px`;
  }, [input]);

  // Handle Enter vs Shift+Enter behaviour
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    if (e.key === "Enter") {
      if ((isMobile && e.shiftKey) || (!isMobile && !e.shiftKey)) {
        e.preventDefault();
        if (input.trim()) {
          handleSend();
        }
      }
    }
  };

  // Send a new coach message
  const handleSend = async () => {
    const content = input.trim();
    if (!content) return;
    try {
      const saved = await sendCoachMessage(channelId, { name: coachName, email: "" }, content);
      if (saved) {
        setThreadMsgs((prev) => [...prev, saved]);
        setInput("");
        requestAnimationFrame(() => {
          scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight });
        });
      }
    } catch (err) {
      console.error("sendCoachMessage error", err);
    }
  };

  const slideTransition = useTransition(visible, {
    from: { transform: "translateY(100%)" },
    enter: { transform: "translateY(0%)" },
    leave: { transform: "translateY(100%)" },
    config: {
      tension: 250,
      friction: 35,
      clamp: true,
    },
  });

  const overlay = slideTransition((style, item) =>
    item ? (
      <animated.div
        style={{ ...style, willChange: "transform" }}
        className="fixed inset-x-0 bottom-0 h-[75vh] bg-black/90 backdrop-blur-md border-t border-white/10 rounded-t-3xl flex flex-col z-50"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10 max-w-3xl mx-auto w-full">
          <Text className="text-white font-semibold">Thread with {user.userName}</Text>
          <Button variant="ghost" css="p-2 hover:bg-white/10 rounded-lg" onClick={onClose}>
            <CircleXIcon className="w-5 h-5 text-white" />
          </Button>
        </div>

        {/* Body */}
        <ScrollArea
          className="flex-1 overflow-y-auto p-4 space-y-4 w-full max-w-3xl mx-auto"
          ref={scrollRef}
        >
          {threadMsgs.map((m, idx) => {
            const isCoach = m.sender === "coach";
            const noop = () => {};
            return (
              <div
                key={m.id}
                data-thread-id={m.id}
                className={`flex flex-col w-full ${isCoach ? "items-end" : "items-start"} mb-3`}
              >
                <div className="w-full max-w-[90%]">
                  <MessageItem
                    userName={user.userName}
                    message={{
                      ...m,
                      Id: m.id,
                      Sender: m.sender,
                      Content: m.content,
                      Date: m.date,
                      Type: "coach_message",
                      userName: user.userName,
                      CoachName: m.coachName,
                    }}
                    index={idx}
                    allMessages={threadMsgs as any}
                    contentReady={true}
                    currentLetterIndex={0}
                    activeAudioId={0}
                    setActiveAudioId={noop}
                    toggleStar={noop}
                    showCoachPill={false}
                    highlight={m.id === highlightId}
                    showHumanCoachName={false}
                  />
                </div>
              </div>
            );
          })}
        </ScrollArea>

        {/* Input */}
        <div className="border-t border-white/10 p-4 flex gap-3 max-w-3xl mx-auto w-full">
          <textarea
            ref={textareaRef}
            className="flex-1 bg-white/10 text-white placeholder-gray-400 border border-white/20 rounded-lg px-4 py-2 focus:outline-none focus:border-orange-500 focus:ring-1 focus:ring-orange-500 resize-none overflow-y-auto"
            placeholder="Reply..."
            rows={1}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            style={{ minHeight: "48px", maxHeight: "150px" }}
          />
          <Button
            onClick={handleSend}
            disabled={input.trim() === ""}
            css={`${input.trim() === "" ? "bg-white/10 text-gray-400 cursor-not-allowed" : "bg-orange-600 hover:bg-orange-700 text-white"} px-4 py-2 rounded-lg`}
          >
            Send
          </Button>
        </div>
      </animated.div>
    ) : null
  );

  return createPortal(overlay, document.body);
};
