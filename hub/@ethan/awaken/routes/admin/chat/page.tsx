"use server";

import { AdminChat } from "./admin-chat.tsx";
import { SUPERUSER_EMAILS } from "../../../lib/server-constants.ts";
import { getCoachByEmail } from "../../admin/actions/admin-actions.ts";
import { Redirect } from "@reframe/ui/main.tsx";
import { getAuthenticatedUser } from "../../../lib/auth-helper.ts";

export const AdminChatPage = async () => {
  const user = await getAuthenticatedUser();
  
  if (!user) {
    return <Redirect to="/auth/sign-in" />;
  }
  
  // Check if user is a superuser or a coach
  const isSuperUser = SUPERUSER_EMAILS.includes(user.email);
  let isCoach = false;
  
  if (!isSuperUser) {
    // Check if user is a coach
    const coach = await getCoachByEmail(user.email);
    isCoach = !!coach;
    
    if (!isCoach) {
      return <Redirect to="/chat" />;
    }
  }
  
  // Pass user data to the client component
  return <AdminChat user={user} />;
}; 