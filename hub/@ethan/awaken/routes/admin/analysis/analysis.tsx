"use client";

import React, { useState, useEffect, useRef } from "npm:react@canary";
import { 
  Text, 
  Textarea, 
  Loader
} from "@reframe/ui/main.tsx";
import { getAllChannelConversations, analyzeConversations } from "../actions/admin-actions.ts";
import { MessageCircleIcon, BrainIcon, RefreshIcon } from "../../../lib/icons.tsx";
import { MoonIcon, SunIcon } from "../../../lib/icons.tsx";
import { Layout } from "../../../lib/layout.tsx";

export interface AdminChatAnalysisProps {
  user: any;
}

export const AdminChatAnalysis: React.FC<AdminChatAnalysisProps> = ({ user }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [conversations, setConversations] = useState<Record<string, any[]>>({});
  const [channels, setChannels] = useState<string[]>([]);
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);
  const [analysisMode, setAnalysisMode] = useState<'all' | 'single'>('all');
  const [analysisModel, setAnalysisModel] = useState<'gemini-2.5-pro-preview-06-05' | 'openai-openrouter' | 'openai-api-4.5' | 'openai-api-o1-medium' | 'openai-api-o1-high'>('gemini-2.5-pro-preview-06-05');
  const [query, setQuery] = useState("");
  const [followUpQuery, setFollowUpQuery] = useState("");
  const [conversationHistory, setConversationHistory] = useState<Array<{query: string; result: string}>>([]);
  const [fetchMode, setFetchMode] = useState<'byChannel' | 'recentOverall'>('byChannel');
  const [messagesLimit, setMessagesLimit] = useState<Record<string, number>>({
    byChannel: 10,
    recentOverall: 100
  });
  
  // New state for the input field value
  const [inputLimit, setInputLimit] = useState<string>('10');
  
  // Cache for conversation data to avoid unnecessary server requests
  const [cachedData, setCachedData] = useState<Record<string, {
    conversations: Record<string, any[]>;
    stats: {
      channelCount: number;
      totalMessages: number;
      fetchMode: 'byChannel' | 'recentOverall';
    };
  }>>({});
  
  const [tokenInfo, setTokenInfo] = useState<{
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
    responseTime: string;
  } | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Function to get the appropriate message limit for current mode
  const getCurrentLimit = () => {
    return messagesLimit[fetchMode];
  };
  
  // Cache key generator
  const getCacheKey = (mode: 'byChannel' | 'recentOverall', limit: number) => {
    return `${mode}-${limit}`;
  };
  
  // Initialize input limit when fetch mode changes
  useEffect(() => {
    setInputLimit(messagesLimit[fetchMode].toString());
  }, [fetchMode]);
  
  const fetchConversations = async (forceFetch = false) => {
    const currentLimit = getCurrentLimit();
    const cacheKey = getCacheKey(fetchMode, currentLimit);
    
    // If we have cached data and not forcing a refresh, use it
    if (!forceFetch && cachedData[cacheKey]) {
      console.log(`[ADMIN] Using cached data for ${fetchMode} mode with limit ${currentLimit}`);
      const data = cachedData[cacheKey];
      setConversations(data.conversations);
      setChannels(Object.keys(data.conversations).sort());
      return;
    }
    
    // Otherwise fetch from server
    setIsLoading(true);
    try {
      console.log(`[ADMIN] Fetching new data for ${fetchMode} mode with limit ${currentLimit}`);
      const data = await getAllChannelConversations(fetchMode, currentLimit);
      
      // Update cache with new data
      setCachedData(prev => ({
        ...prev,
        [cacheKey]: data
      }));
      
      setConversations(data.conversations);
      setChannels(Object.keys(data.conversations).sort());
    } catch (error) {
      console.error("Error fetching conversations:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Initial data fetch
  useEffect(() => {
    fetchConversations();
  }, []);
  
  // When fetch mode changes, try to use cached data or fetch new data
  useEffect(() => {
    const currentLimit = getCurrentLimit();
    const cacheKey = getCacheKey(fetchMode, currentLimit);
    
    if (cachedData[cacheKey]) {
      // Use cached data
      const data = cachedData[cacheKey];
      setConversations(data.conversations);
      setChannels(Object.keys(data.conversations).sort());
    } else {
      // Fetch new data
      fetchConversations();
    }
  }, [fetchMode]);
  
  // When messages limit changes for the current mode, update and potentially fetch
  const updatemessageLimit = (value: number) => {
    if (isNaN(value) || value <= 0) return;
    
    // Update the messages limit state
    setMessagesLimit(prev => ({
      ...prev,
      [fetchMode]: value
    }));
    
    // Check if we need to fetch new data
    const newCacheKey = getCacheKey(fetchMode, value);
    if (!cachedData[newCacheKey]) {
      // We'll need to fetch new data with this limit, but not immediately
      // Instead, wait a brief moment to avoid multiple fetches if user
      // is still typing or making further changes
      setTimeout(() => {
        fetchConversations(true);
      }, 100);
    }
  };
  
  const handleMessagesLimitChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Just update the input field value as the user types
    setInputLimit(e.target.value);
  };
  
  // Function to apply limit when user is done editing
  const applyLimit = () => {
    const value = parseInt(inputLimit);
    if (!isNaN(value) && value > 0) {
      updatemessageLimit(value);
    } else {
      // Reset to current value if invalid
      setInputLimit(messagesLimit[fetchMode].toString());
    }
  };
  
  useEffect(() => {
    // Scroll to bottom of results
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [conversationHistory]);
  
  const handleChannelSelect = (channelId: string) => {
    setSelectedChannel(channelId === selectedChannel ? null : channelId);
    // When a channel is selected, switch to single-user mode
    if (channelId !== selectedChannel) {
      setAnalysisMode('single');
    }
  };
  
  const handleAnalyze = async () => {
    if (!query.trim()) return;
    
    setIsAnalyzing(true);
    setTokenInfo(null);
    // Reset conversation history when starting a new query
    setConversationHistory([]);
    setFollowUpQuery(""); // Clear any follow-up input
    
    try {
      const dataForAnalysis = analysisMode === 'single' && selectedChannel
        ? { [selectedChannel]: conversations[selectedChannel] }
        : conversations;
      
      const result = await analyzeConversations(
        dataForAnalysis,
        query,
        {
          mode: analysisMode,
          model: analysisModel,
          promptMode: 'kokoro_meta'
        }
      );
      
      // Initialize conversation history with the first result
      setConversationHistory([{ query, result: result.analysis }]);
      setTokenInfo(result.tokenInfo);
    } catch (error) {
      console.error("Error analyzing conversations:", error);
      setConversationHistory([{ 
        query, 
        result: "Error analyzing conversations. Please try again." 
      }]);
    } finally {
      setIsAnalyzing(false);
    }
  };
  
  const handleFollowUp = async () => {
    if (!followUpQuery.trim()) return;
    
    setIsAnalyzing(true);
    setTokenInfo(null);
    
    try {
      // Create a better formatted prompt that includes the conversation history
      const historyText = conversationHistory.map(item => 
        `Question: ${item.query}\nResponse: ${item.result}`
      ).join('\n\n');
      
      const fullPrompt = `${followUpQuery.trim()}\n\nThis is a follow-up question to our previous conversation. For context, here's the history:\n\n${historyText}`;
      
      console.log("Follow-up prompt structure:", fullPrompt.substring(0, 100) + "...");
      
      const dataForAnalysis = analysisMode === 'single' && selectedChannel
        ? { [selectedChannel]: conversations[selectedChannel] }
        : conversations;
      
      const result = await analyzeConversations(
        dataForAnalysis,
        fullPrompt,
        {
          mode: analysisMode,
          model: analysisModel,
          promptMode: 'kokoro_meta'
        }
      );
      
      // Instead of replacing the analysis result, we keep track of all interactions
      const newHistoryItem = { query: followUpQuery, result: result.analysis };
      setConversationHistory(prev => [...prev, newHistoryItem]);
      setTokenInfo(result.tokenInfo);
      setFollowUpQuery(""); // Clear the follow-up input
    } catch (error) {
      console.error("Error processing follow-up question:", error);
      // Add error message to conversation
      setConversationHistory(prev => [...prev, { 
        query: followUpQuery, 
        result: "Error processing follow-up question. Please try again." 
      }]);
    } finally {
      setIsAnalyzing(false);
    }
  };
  
  const handleModeChange = (mode: 'all' | 'single') => {
    setAnalysisMode(mode);
    if (mode === 'all') {
      setSelectedChannel(null);
    }
  };
  
  const handleFetchModeChange = (mode: 'byChannel' | 'recentOverall') => {
    setFetchMode(mode);
  };
  
  const handleRefreshData = () => {
    // Clear the entire cache to force fresh fetches for both modes
    setCachedData({});
    
    // Fetch fresh data for the current mode
    fetchConversations(true);
  };
  
  const totalMessages = Object.values(conversations).reduce(
    (total: number, messages) => total + (messages as any[]).length, 
    0
  );
  
  // Custom renderer for markdown-like formatting with Awaken theme
  const renderFormattedText = (text: string) => {
    const lines = text.split('\n');
    const formattedElements: JSX.Element[] = [];
    
    let inList = false;
    let listItems: JSX.Element[] = [];
    let listType: 'ul' | 'ol' = 'ul';
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();
      
      if (trimmedLine === '') {
        if (inList) {
          if (listType === 'ul') {
            formattedElements.push(<ul key={`ul-${i}`} className="list-disc pl-6 mb-4 text-gray-200">{listItems}</ul>);
          } else {
            formattedElements.push(<ol key={`ol-${i}`} className="list-decimal pl-6 mb-4 text-gray-200">{listItems}</ol>);
          }
          inList = false;
          listItems = [];
        }
        formattedElements.push(<div key={`space-${i}`} className="h-4"></div>);
        continue;
      }
      
      if (trimmedLine.startsWith('##')) {
        const headingText = trimmedLine.replace(/^##\s*/, '');
        formattedElements.push(
          <h2 key={`h2-${i}`} className="text-xl font-bold mb-3 mt-5 text-orange-300">
            {headingText}
          </h2>
        );
        continue;
      }
      
      if (trimmedLine.startsWith('#')) {
        const headingText = trimmedLine.replace(/^#\s*/, '');
        formattedElements.push(
          <h1 key={`h1-${i}`} className="text-2xl font-bold mb-4 mt-6 text-orange-300">
            {headingText}
          </h1>
        );
        continue;
      }
      
      if (trimmedLine.startsWith('* ') || trimmedLine.startsWith('- ')) {
        const itemText = trimmedLine.replace(/^[*-]\s*/, '');
        const formattedItemContent = formatInlineStyles(itemText);
        
        if (!inList || listType !== 'ul') {
          if (inList) {
            if (listType === 'ul') {
              formattedElements.push(<ul key={`ul-${i}`} className="list-disc pl-6 mb-4 text-gray-200">{listItems}</ul>);
            } else {
              formattedElements.push(<ol key={`ol-${i}`} className="list-decimal pl-6 mb-4 text-gray-200">{listItems}</ol>);
            }
            listItems = [];
          }
          inList = true;
          listType = 'ul';
        }
        
        listItems.push(<li key={`li-${i}`} className="text-gray-200 mb-1">{formattedItemContent}</li>);
        continue;
      }
      
      if (/^\d+\.\s/.test(trimmedLine)) {
        const itemText = trimmedLine.replace(/^\d+\.\s*/, '');
        const formattedItemContent = formatInlineStyles(itemText);
        
        if (!inList || listType !== 'ol') {
          if (inList) {
            if (listType === 'ul') {
              formattedElements.push(<ul key={`ul-${i}`} className="list-disc pl-6 mb-4 text-gray-200">{listItems}</ul>);
            } else {
              formattedElements.push(<ol key={`ol-${i}`} className="list-decimal pl-6 mb-4 text-gray-200">{listItems}</ol>);
            }
            listItems = [];
          }
          inList = true;
          listType = 'ol';
        }
        
        listItems.push(<li key={`li-${i}`} className="text-gray-200 mb-1">{formattedItemContent}</li>);
        continue;
      }
      
      if (inList) {
        if (listType === 'ul') {
          formattedElements.push(<ul key={`ul-${i}`} className="list-disc pl-6 mb-4 text-gray-200">{listItems}</ul>);
        } else {
          formattedElements.push(<ol key={`ol-${i}`} className="list-decimal pl-6 mb-4 text-gray-200">{listItems}</ol>);
        }
        inList = false;
        listItems = [];
      }
      
      const formattedContent = formatInlineStyles(line);
      formattedElements.push(<p key={`p-${i}`} className="mb-4 text-gray-200">{formattedContent}</p>);
    }
    
    if (inList) {
      if (listType === 'ul') {
        formattedElements.push(
          <ul key="ul-final" className="list-disc pl-6 mb-4 text-gray-200">{listItems}</ul>
        );
      } else {
        formattedElements.push(
          <ol key="ol-final" className="list-decimal pl-6 mb-4 text-gray-200">{listItems}</ol>
        );
      }
    }
    
    return formattedElements;
  };
  
  const formatInlineStyles = (text: string) => {
    const boldRegex = /\*\*(.*?)\*\*|__(.*?)__/g;
    let parts = [];
    let lastIndex = 0;
    let match;
    
    while ((match = boldRegex.exec(text)) !== null) {
      const matchedText = match[1] || match[2];
      const beforeMatch = text.substring(lastIndex, match.index);
      
      if (beforeMatch) {
        parts.push(beforeMatch);
      }
      
      parts.push(<strong key={`bold-${match.index}`} className="text-orange-200 font-bold">{matchedText}</strong>);
      lastIndex = match.index + match[0].length;
    }
    
    if (lastIndex < text.length) {
      parts.push(text.substring(lastIndex));
    }
    
    const processedParts = parts.map((part, index) => {
      if (typeof part !== 'string') {
        return part;
      }
      
      const italicParts = [];
      const italicRegex = /\*(.*?)\*|_(.*?)_/g;
      let italicLastIndex = 0;
      let italicMatch;
      
      while ((italicMatch = italicRegex.exec(part)) !== null) {
        const matchedText = italicMatch[1] || italicMatch[2];
        const beforeMatch = part.substring(italicLastIndex, italicMatch.index);
        
        if (beforeMatch) {
          italicParts.push(beforeMatch);
        }
        
        italicParts.push(
          <em key={`italic-${italicMatch.index}`} className="text-gray-300 italic">{matchedText}</em>
        );
        
        italicLastIndex = italicMatch.index + italicMatch[0].length;
      }
      
      if (italicLastIndex < part.length) {
        italicParts.push(part.substring(italicLastIndex));
      }
      
      return italicParts.length > 1 ? italicParts : part;
    });
    
    return processedParts.flat();
  };
  
  const FullPageLoader = () => (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white/5 border border-white/10 rounded-lg p-8 flex flex-col items-center">
        <Loader size="lg" className="text-orange-400" />
        <p className="mt-4 text-white text-lg font-medium">Loading conversation data...</p>
      </div>
    </div>
  );
  
  return (
    <Layout user={user} userData={null} currentPath="admin-analysis" keepDrawer={false}>
      {isLoading && <FullPageLoader />}
      <div className="w-full max-w-5xl mx-auto p-4 overflow-y-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className={`text-4xl font-bold mb-2`}>Admin Chat Analysis</h1>
            <p className={`text-lg`}>
              Analyze conversation history to improve Awaken
            </p>
          </div>
        </div>
        
                 {/* Fetch Mode Selection */}
         <div className="border border-white/10 bg-white/5 rounded-lg p-6 mb-6 relative">
           <div className="flex justify-between items-center mb-4">
             <h3 className="font-medium text-white">
               Data Fetching Options
             </h3>
            
            <button
              onClick={handleRefreshData}
              disabled={isLoading}
              className={`px-4 py-2 rounded-md font-medium flex items-center justify-center gap-2 ${
                isLoading 
                  ? 'bg-white/10 text-gray-400 cursor-not-allowed'
                  : 'bg-orange-600 hover:bg-orange-700 text-white'
              }`}
            >
              {isLoading ? (
                <Loader size="sm" className="text-white" />
              ) : (
                <RefreshIcon className="w-4 h-4" />
              )}
              Refresh Data
            </button>
          </div>
          
                     <div className="flex flex-col space-y-4 mb-4 md:flex-row md:space-y-0 md:space-x-6 md:items-end">
             <div className="flex-1">
               <label className="block text-sm font-medium mb-2 text-gray-300">
                 Fetch Mode
               </label>
              <div className="flex space-x-3">
                <button
                  onClick={() => handleFetchModeChange('byChannel')}
                                     className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors border ${
                     fetchMode === 'byChannel' 
                       ? 'bg-orange-600 text-white border-orange-400'
                       : 'bg-white/5 text-gray-300 hover:bg-white/10 border-white/20'
                   }`}
                >
                  By Channel
                </button>
                <button
                  onClick={() => handleFetchModeChange('recentOverall')}
                                     className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors border ${
                     fetchMode === 'recentOverall' 
                       ? 'bg-orange-600 text-white border-orange-400'
                       : 'bg-white/5 text-gray-300 hover:bg-white/10 border-white/20'
                   }`}
                >
                  Most Recent Overall
                </button>
              </div>
                             <p className="mt-2 text-xs text-gray-400">
                 {fetchMode === 'byChannel' 
                   ? 'Fetch most recent messages for each channel'
                   : 'Fetch most recent messages across all channels'}
               </p>
            </div>
            
                         <div className="w-32">
               <label className="block text-sm font-medium mb-2 text-gray-300">
                 Message Limit
               </label>
              <input
                type="number"
                min="1"
                value={inputLimit}
                onChange={handleMessagesLimitChange}
                onBlur={applyLimit}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.currentTarget.blur(); // Remove focus
                    applyLimit();
                  }
                }}
                className="w-full py-2 px-3 rounded-md bg-white/5 border border-white/20 text-white placeholder-gray-500 focus:border-orange-500 focus:ring-1 focus:ring-orange-500"
              />
              <p className="mt-2 text-xs text-gray-400">
                {fetchMode === 'byChannel' ? 'Per channel' : 'Total'}
              </p>
            </div>
            
            <div className="w-44">
              <label className="block text-sm font-medium mb-2 text-gray-300">
                Model Selector
              </label>
              <select
                value={analysisModel}
                onChange={(e) => setAnalysisModel(e.target.value as 'gemini-2.5-pro-preview-06-05' | 'openai-openrouter' | 'openai-api-4.5' | 'openai-api-o1-medium' | 'openai-api-o1-high')}
                className="w-full py-2 px-3 rounded-md bg-white/5 border border-white/20 text-white focus:border-orange-500 focus:ring-1 focus:ring-orange-500"
              >
                <option value="gemini-2.5-pro-preview-06-05">Gemini 2.5 Pro [OR]</option>
                <option value="openai-openrouter">GPT 4.5 Preview [OR]</option>
                <option value="openai-api-4.5">GPT 4.5 (API)</option>
                <option value="openai-api-o1-medium">o1 Medium (API)</option>
                <option value="openai-api-o1-high">o1 High (API)</option>
              </select>
              <p className="mt-2 text-xs text-gray-400">
                {analysisModel.includes('api') ? 'OpenAI API' : 'AI model via OpenRouter'}
              </p>
            </div>
          </div>
          
          <div className="p-3 rounded-md bg-orange-500/20 border border-orange-500/30 mt-2">
            <p className="text-sm text-orange-200">
              {isLoading 
                ? 'Loading conversation data...' 
                : `Loaded ${totalMessages} messages from ${Object.keys(conversations).length} channels using ${fetchMode === 'byChannel' ? 'per-channel' : 'recent overall'} mode (limit: ${messagesLimit[fetchMode]})`}
            </p>
          </div>
        </div>
        
        <div className="border border-white/10 bg-white/5 rounded-lg p-6 mb-8">
          <div className="mb-6">
            <div className="flex space-x-4 mb-6">
              <button
                onClick={() => handleModeChange('all')}
                className={`px-5 py-3 rounded-md text-sm font-medium transition-colors border ${
                  analysisMode === 'all' 
                    ? 'bg-orange-600 text-white border-orange-400'
                    : 'bg-white/5 text-gray-300 hover:bg-white/10 border-white/20'
                }`}
              >
                All Users ({Object.keys(conversations).length} users, {totalMessages} messages)
              </button>
              <button
                onClick={() => handleModeChange('single')}
                className={`px-5 py-3 rounded-md text-sm font-medium transition-colors border ${
                  analysisMode === 'single' 
                    ? 'bg-orange-600 text-white border-orange-400'
                    : 'bg-white/5 text-gray-300 hover:bg-white/10 border-white/20'
                }`}
              >
                Single User{selectedChannel ? ` (${selectedChannel})` : ''}
              </button>
            </div>
            
            {analysisMode === 'single' && (
              <>
                <h3 className="font-medium mb-3 text-white">
                  Select User Channel
                </h3>
                
                {channels.length === 0 ? (
                  <div className="flex justify-center items-center p-8 bg-white/5 rounded-md border border-white/20">
                    <p className="text-gray-400">No channels found</p>
                  </div>
                ) : (
                  <div className="max-h-60 overflow-y-auto border border-white/20 rounded-md bg-white/5 relative">
                    {channels.map((channelId) => (
                      <div 
                        key={channelId}
                        className={`flex items-center gap-3 p-4 cursor-pointer border-b border-white/10 hover:bg-white/5 transition-colors ${
                          selectedChannel === channelId 
                            ? 'bg-orange-500/20 border-l-4 border-orange-400'
                            : 'border-l-4 border-transparent'
                        }`}
                        onClick={() => handleChannelSelect(channelId)}
                      >
                        <MessageCircleIcon className={`${selectedChannel === channelId ? 'text-orange-400' : 'text-gray-400'} w-5 h-5`} />
                        <span className={`${selectedChannel === channelId ? 'text-orange-200' : 'text-gray-200'}`}>
                          Channel: <span className="font-medium">{channelId}</span> 
                          <span className={`text-gray-400 ml-2`}>
                            ({conversations[channelId]?.length || 0} messages)
                          </span>
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
          
          <div className="mt-6">
            <h3 className="font-medium mb-3 text-white">
              Ask Question or Request Analysis
            </h3>
            <Textarea
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="E.g., What are common user concerns? What topics generate the most engagement?"
              rows={4}
              css="w-full mb-4 p-4 bg-white/5 border border-white/20 rounded-md !text-white placeholder-gray-500 focus:border-orange-500 focus:ring-1 focus:ring-orange-500"
            />
            
            <button
              onClick={handleAnalyze}
              disabled={isAnalyzing || isLoading || !query.trim() || (analysisMode === 'single' && !selectedChannel)}
              className={`w-full px-5 py-3 rounded-md font-medium transition-colors ${
                isAnalyzing || isLoading || !query.trim() || (analysisMode === 'single' && !selectedChannel)
                  ? 'bg-white/10 text-gray-400 cursor-not-allowed'
                  : 'bg-orange-600 hover:bg-orange-700 text-white'
              }`}
            >
              {isAnalyzing ? (
                <div className="flex items-center justify-center gap-2">
                  <Loader size="sm" className="text-white" />
                  <span>Analyzing...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center gap-2">
                  <BrainIcon className="w-5 h-5" />
                  <span>Analyze Conversations</span>
                </div>
              )}
            </button>
          </div>
        </div>
        
        {conversationHistory.length > 0 && (
          <div className="border border-white/10 bg-white/5 rounded-lg p-6">
            <div className="flex flex-col gap-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-xl text-white">
                  Analysis Results
                </h3>
                
                {/* Token Usage Info */}
                {tokenInfo && (
                  <div className="bg-white/10 text-orange-200 px-4 py-2 rounded-md border border-white/20 flex items-center gap-4 text-sm">
                    <div className="flex flex-col items-center">
                      <span className="font-bold">{tokenInfo.inputTokens}</span>
                      <span className="text-xs">Input</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <span className="font-bold">{tokenInfo.outputTokens}</span>
                      <span className="text-xs">Output</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <span className="font-bold">{tokenInfo.totalTokens}</span>
                      <span className="text-xs">Total</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <span className="font-bold">{tokenInfo.responseTime}s</span>
                      <span className="text-xs">Time</span>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Display the conversation thread */}
              <div className="flex flex-col gap-6">
                {conversationHistory.map((item, index) => (
                  <div key={index} className="flex flex-col gap-4">
                    {/* User question */}
                                        <div className="flex gap-3 items-start">
                      <div className="rounded-full p-2 bg-white/10 flex-shrink-0">
                        <span className="text-sm font-medium text-white">You</span>
                      </div>
                      <div className="bg-white/5 p-3 rounded-lg border border-white/20 flex-grow">
                        <p className="text-white">{item.query}</p>
                      </div>
                    </div>
                    
                    {/* AI response */}
                    <div className="flex gap-3 items-start">
                      <div className="rounded-full p-2 bg-orange-600 flex-shrink-0">
                        <BrainIcon className="w-4 h-4 text-orange-200" />
                      </div>
                      <div className="bg-white/5 p-5 rounded-md leading-relaxed border border-white/20 flex-grow">
                        {renderFormattedText(item.result)}
                      </div>
                    </div>
                    
                    {/* Add a separator between conversation turns, except for the last one */}
                    {index < conversationHistory.length - 1 && (
                      <div className="border-b border-white/20 my-2"></div>
                    )}
                  </div>
                ))}
              </div>
              
              {/* Follow-up Question Input */}
                              <div className="mt-4">
                  <div className="flex items-center gap-2 mb-2">
                    <BrainIcon className="w-5 h-5 text-orange-400" />
                    <h4 className="font-medium text-white">
                      Ask a follow-up question
                    </h4>
                  </div>
                <div className="flex gap-2">
                                      <Textarea
                      value={followUpQuery}
                      onChange={(e) => setFollowUpQuery(e.target.value)}
                      placeholder="Ask a follow-up question to continue the conversation..."
                      rows={2}
                      css="w-full p-3 bg-white/5 border border-white/20 rounded-md !text-white placeholder-gray-500 focus:border-orange-500 focus:ring-1 focus:ring-orange-500"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && e.ctrlKey) {
                        handleFollowUp();
                      }
                    }}
                  />
                                      <button
                      onClick={handleFollowUp}
                      disabled={isAnalyzing || !followUpQuery.trim()}
                      className={`px-4 whitespace-nowrap flex-shrink-0 ${
                        isAnalyzing || !followUpQuery.trim()
                          ? 'bg-white/10 text-gray-400 cursor-not-allowed'
                          : 'bg-orange-600 hover:bg-orange-700 text-white'
                      }`}
                  >
                    {isAnalyzing ? (
                      <div className="flex items-center justify-center gap-2">
                        <Loader size="sm" className="text-white" />
                        <span>Processing...</span>
                      </div>
                    ) : (
                      <span>Send</span>
                    )}
                  </button>
                </div>
                                  <p className="mt-1 text-xs text-gray-400">
                    Press Ctrl+Enter to send
                  </p>
              </div>
            </div>
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
    </Layout>
  );
}; 