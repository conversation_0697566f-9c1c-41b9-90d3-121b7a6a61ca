"use client";

import React from "npm:react@canary";
import { Logo } from "../../lib/logo.tsx";
import { Text, ScrollArea } from "@reframe/ui/main.tsx";
import { motion } from "npm:framer-motion";

export const Terms = () => {
  const handleNavigateHome = () => {
    // Close the current tab if it was opened from the payment overlay
    if (globalThis.opener && !globalThis.opener.closed) {
      globalThis.close();
    } else {
      // Otherwise navigate to the home page
      globalThis.location.href = "/";
    }
  };

  
  return (
    <div
      className="
        relative min-h-screen text-white flex flex-col items-center
        before:absolute before:inset-0 before:-z-10
        before:bg-[radial-gradient(circle_at_center,_rgba(252,163,17,0.3)_0%,_rgba(0,0,0,0)_70%)]
        bg-black
      "
    >
      {/* Header with glow effect */}
      <div className="w-full relative">
        <motion.div
          className="absolute w-[500px] h-[300px] rounded-full blur-[100px] opacity-40 z-0"
          style={{
            top: "-150px",
            left: "50%",
            transform: "translateX(-50%)",
            background: "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
          }}
        />
      </div>

      {/* Top navigation bar */}
      <div className="w-full max-w-4xl flex items-center justify-between px-6 pt-8 pb-4 z-10 relative">
        <div 
          className="flex items-center gap-2 cursor-pointer group" 
          onClick={handleNavigateHome}
        >
          <Logo size={24} />
          <span className="text-base font-normal text-white group-hover:text-[#FCA311] transition-colors">
            awaken
          </span>
        </div>
      </div>

      {/* Content container */}
      <div className="w-full max-w-4xl px-6 pb-20 flex flex-col z-10 relative">
        {/* Title section with more vertical spacing */}
        <div className="text-center mb-12 mt-4">
          <Text className="text-3xl md:text-4xl font-semibold text-[#FCA311] mb-3">
            Terms of Service
          </Text>
          <div className="h-0.5 w-16 bg-gradient-to-r from-[#FCA311] to-[#FF5727] mx-auto mb-3"></div>
          <Text className="text-sm text-white/60">
            Last updated: March 4, 2025
          </Text>
        </div>

        {/* Scroll area with content */}
        <ScrollArea className="w-full flex-1 overflow-auto pr-2">
          <div className="space-y-10 max-w-3xl mx-auto">
            {/* Introduction section with company info */}
            <section>
              <p className="text-white/80 mb-4 leading-relaxed">
                Welcome to <strong>Awaken</strong>, a product of <strong>Pureplay Studios Ltd, The Growth Doctors Ltd, and Creating LLC</strong> ("Awaken," "we," "us," "our"). Awaken provides AI-driven coaching, journaling, reflection, and related services (collectively, the "Services") through our website at <strong>awaken.is</strong> (the "Site"). These Terms of Service ("Terms") govern your access to and use of the Site and Services. By creating an account or otherwise using the Services, you agree to be bound by these Terms. If you do not agree, you may not access or use the Services.
              </p>
              <p className="text-white/80 mb-4 leading-relaxed">
                If you have any questions regarding these Terms or our Services, please contact us at:
              </p>
              <div className="pl-6 text-white/80 mb-3 leading-relaxed">
                <p className="mb-2"><strong>Pureplay Studios Ltd</strong><br />
                Suite LP7356<br />
                20-22 Wenlock Road<br />
                London N1 7GU<br />
                Email: <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a></p>
                
                <p className="mb-2"><strong>The Growth Doctors Ltd</strong><br />
                Greystones<br />
                Langford Road<br />
                Lower Langford BS40 5HU<br />
                Email: <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a></p>
                
                <p className="mb-2">For U.S. users, you may also contact:<br />
                <strong>Creating LLC</strong><br />
                131 Continental Drive, Suite 305, Newark, DE 19713, USA<br />
                Email: <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a></p>
              </div>
            </section>

            {/* Section 1: Acceptance of Terms */}
            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">1</span>
                Acceptance of Terms
              </h2>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">1.1 Binding Agreement</h3>
              <p className="text-white/80 mb-4 leading-relaxed ml-11">
                By accessing or using the Services, you acknowledge that you have read, understood, and agree to be bound by these Terms and our <strong>Privacy Policy</strong> (collectively, the "Agreement"). If you do not agree, you may not use the Services.
              </p>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">1.2 Eligibility</h3>
              <p className="text-white/80 mb-3 leading-relaxed ml-11">
                You must be at least <strong>16 years old</strong> (or the equivalent age of majority in your jurisdiction) to use the Services. However, for users in the <strong>United States</strong>, you must be at least <strong>13 years old</strong> in compliance with the <strong>Children's Online Privacy Protection Act (COPPA)</strong>. By using the Services, you represent and warrant that you meet this requirement.
              </p>
            </section>

            {/* Section 2: Changes to These Terms */}
            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">2</span>
                Changes to These Terms
              </h2>
              <p className="text-white/80 mb-3 leading-relaxed">
                We may modify these Terms at any time at our sole discretion. If we do, we will provide notice by posting the updated Terms on our Site or by other reasonable means. Your continued use of the Services following the posting of changes constitutes acceptance of the updated Terms. If you do not agree with any changes, you must stop using the Services.
              </p>
            </section>

            {/* Section 3: Privacy */}
            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">3</span>
                Privacy
              </h2>
              <p className="text-white/80 mb-3 leading-relaxed">
                Your use of the Services is subject to our <strong>Privacy Policy</strong>, which explains how we collect, use, and protect your personal data. For users in the <strong>United States</strong>, Awaken complies with the <strong>California Consumer Privacy Act (CCPA)</strong> and other applicable privacy laws.
              </p>
            </section>

            {/* Section 4: Use of the Services */}
            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">4</span>
                Use of the Services
              </h2>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">4.1 Account Creation</h3>
              <p className="text-white/80 mb-4 leading-relaxed ml-11">
                To access certain features of the Services, you may need to create an account. You agree to provide accurate and complete information and to keep it updated. You are responsible for maintaining the confidentiality of your account credentials and all activity under your account.
              </p>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">4.2 Prohibited Conduct</h3>
              <p className="text-white/80 mb-3 leading-relaxed ml-11">
                You agree not to use the Services in any way that:
              </p>
              <ul className="list-disc pl-16 text-white/80 mb-4 leading-relaxed space-y-1">
                <li>Violates any applicable law or regulation, including privacy, intellectual property, or data protection laws.</li>
                <li>Infringes upon the rights of others, including their intellectual property or privacy rights.</li>
                <li>Is fraudulent, harassing, hateful, pornographic, defamatory, or incites violence or the exploitation of minors.</li>
                <li>Attempts to gain unauthorized access to systems, accounts, or other networks.</li>
                <li>Disrupts, degrades, or interferes with the normal operation of the Services (including placing an unreasonable load on our infrastructure or distributing malware).</li>
              </ul>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">4.3 No Medical or Therapeutic Services</h3>
              <p className="text-white/80 mb-3 leading-relaxed ml-11">
                Awaken is <strong>not a mental health service</strong> and does not provide therapy, counseling, medical, or psychiatric services. The Services are intended for general self-improvement and personal reflection purposes only. If you require mental health support, please seek help from a qualified healthcare professional or emergency services.
              </p>
            </section>

            {/* Section 5: Subscription & Payment Terms */}
            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">5</span>
                Subscription & Payment Terms
              </h2>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">5.1 Subscription Plans</h3>
              <p className="text-white/80 mb-4 leading-relaxed ml-11">
                Certain parts of the Services may be offered on a subscription basis ("Paid Services"). Details about subscription plans, pricing, and billing cycles are available on our Site.
              </p>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">5.2 Billing & Auto-Renewal</h3>
              <ul className="list-disc pl-16 text-white/80 mb-4 leading-relaxed space-y-1">
                <li><strong>Billing Cycle:</strong> Subscription fees are billed in advance on a recurring basis (e.g., monthly or annually), depending on your plan.</li>
                <li><strong>Auto-Renewal:</strong> Subscriptions automatically renew unless you cancel or we terminate. You authorize us to charge your payment method for the renewal.</li>
                <li><strong>U.S. Compliance:</strong> Awaken complies with <strong>California's automatic renewal laws</strong> and other applicable regulations.</li>
              </ul>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">5.3 Cancellations & Refunds</h3>
              <ul className="list-disc pl-16 text-white/80 mb-3 leading-relaxed space-y-1">
                <li>You may cancel your subscription at any time through your account settings or by contacting us. Access to Paid Services continues until the end of your billing cycle.</li>
                <li><strong>Refund Policy for U.S. Users:</strong> Refunds are provided as required by applicable state laws, including prorated refunds where mandated.</li>
              </ul>
            </section>

            {/* Section 6: Governing Law & Dispute Resolution */}
            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">6</span>
                Governing Law & Dispute Resolution
              </h2>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">6.1 Governing Law</h3>
              <ul className="list-disc pl-16 text-white/80 mb-4 leading-relaxed space-y-1">
                <li><strong>For U.S. Users:</strong> These Terms shall be governed by the laws of the <strong>State of California</strong>, unless otherwise required by your state of residence.</li>
                <li><strong>For Non-U.S. Users:</strong> These Terms are governed by the laws of <strong>England and Wales</strong>.</li>
              </ul>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">6.2 Arbitration & Class Action Waiver (U.S. Users)</h3>
              <ul className="list-disc pl-16 text-white/80 mb-3 leading-relaxed space-y-1">
                <li><strong>Binding Arbitration:</strong> Any dispute shall be resolved through binding arbitration under the <strong>Federal Arbitration Act (FAA)</strong> and administered by the <strong>American Arbitration Association (AAA)</strong>.</li>
                <li><strong>Class Action Waiver:</strong> By using the Services, you waive your right to participate in class-action lawsuits against Awaken.</li>
                <li><strong>Opt-Out Option:</strong> You may opt out of arbitration by notifying us within <strong>30 days</strong> of first accepting these Terms.</li>
              </ul>
            </section>

            {/* Section 7: AI Usage Notice & Disclaimer */}
            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">7</span>
                AI Usage Notice & Disclaimer
              </h2>
              <p className="text-white/80 mb-4 leading-relaxed">
                Awaken uses third-party AI services, including language models, transcription, and text-to-speech providers ("AI Providers"). For <strong>U.S. users</strong>, Awaken complies with <strong>Federal Trade Commission (FTC) AI transparency guidelines</strong>.
              </p>
              <p className="text-white/80 mb-3 leading-relaxed">
                AI-generated content may be:
              </p>
              <ul className="list-disc pl-8 text-white/80 mb-4 leading-relaxed space-y-1">
                <li>Incorrect, incomplete, or outdated.</li>
                <li>Transmitted in plaintext for processing by AI Providers.</li>
                <li>Processed without being retained or used for training, per AI Provider policies.</li>
              </ul>
              <p className="text-white/80 mb-3 leading-relaxed">
                You should not rely on AI-generated content for professional, medical, legal, or financial advice without independent verification.
              </p>
            </section>

            {/* Section 8: Accessibility (ADA Compliance) */}
            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">8</span>
                Accessibility (ADA Compliance)
              </h2>
              <p className="text-white/80 mb-3 leading-relaxed">
                We are committed to digital accessibility and comply with applicable accessibility standards, including the <strong>Americans with Disabilities Act (ADA)</strong>. If you require accommodations, please contact <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a>.
              </p>
            </section>

            {/* Section 9: Contact Information */}
            <section>
              <h2 className="text-xl font-medium text-[#FCA311] mb-4 flex items-center">
                <span className="inline-block w-8 h-8 rounded-full bg-gradient-to-r from-[#FCA311]/20 to-[#FF5727]/20 mr-3 flex-shrink-0 flex items-center justify-center text-[#FCA311]">9</span>
                Contact Information
              </h2>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">For U.S. users:</h3>
              <p className="text-white/80 mb-4 leading-relaxed ml-11">
                <strong>Creating LLC</strong><br />
                131 Continental Drive, Suite 305, Newark, DE 19713, USA<br />
                Email: <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a>
              </p>

              <h3 className="text-lg font-medium text-[#FCA311]/90 mb-3 ml-11">For non-U.S. users:</h3>
              <p className="text-white/80 mb-4 leading-relaxed ml-11">
                <strong>Pureplay Studios Ltd</strong><br />
                Suite LP7356, 20-22 Wenlock Road, London N1 7GU, UK<br />
                Email: <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a>
              </p>
              <p className="text-white/80 mb-3 leading-relaxed ml-11">
                <strong>The Growth Doctors Ltd</strong><br />
                Greystones, Langford Road, Lower Langford BS40 5HU<br />
                Email: <a href="mailto:<EMAIL>" className="text-[#FCA311] hover:underline"><EMAIL></a>
              </p>
            </section>
            
            {/* Footer with subtle separator */}
            <div className="pt-10 mt-10 border-t border-white/10 text-center">
              <p className="text-white/40 text-sm">
                © {new Date().getFullYear()} Awaken. All rights reserved.
              </p>
            </div>
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};