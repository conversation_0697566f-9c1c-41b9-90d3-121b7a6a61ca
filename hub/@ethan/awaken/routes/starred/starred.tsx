"use client";

import React, { useState, useRef, useEffect } from "npm:react@canary";
import { motion } from "npm:framer-motion";
import { <PERSON>rollA<PERSON>, Button, Text } from "@reframe/ui/main.tsx";
import { Logo } from "../../lib/logo.tsx";
import { StarIcon, PlayIcon, PauseIcon } from "../../lib/icons.tsx";
import { Layout } from "../../lib/layout.tsx";
import { getUserData } from "../../lib/db.ts";

interface StarredMessage {
  Id: string;
  Sender?: string;
  Date: string;
  Content: string;
  Audio?: string;
  CoachName?: string;
}

interface IntegrateProps {
  user: {
    channelId?: string | number;
    image?: string;
  };
  starred?: Array<StarredMessage>;
}

// Helper to format date as "24 Jan, 3.22 PM"
function formatInsightDate(dateString: string) {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("en-GB", { month: "short" });
  let hour = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hour >= 12 ? "PM" : "AM";
  hour = hour % 12 || 12;
  const minWithZero = minutes < 10 ? `0${minutes}` : minutes;
  return `${day} ${month}, ${hour}.${minWithZero} ${ampm}`;
}

// Decide if text should be orange (assistant) or white (user)
function getContentTextClass(sender?: string) {
  if (sender === "assistant") {
    return "text-[#FCA311]";
  }
  return "text-white";
}

// Convert sender to "You" or actual coach name
function getSenderLabel(sender?: string, coachName?: string) {
  if (sender === "assistant") {
    return coachName || "Kokoro";
  }
  return "You";
}

export const Starred: React.FC<IntegrateProps> = ({ user, starred = [] }) => {
  // Get user data for the Layout component
  const [userData, setUserData] = useState<Record<string, unknown> | null>(null);
  
  // Fetch user data when component mounts
  useEffect(() => {
    const fetchUserData = async () => {
      if (user?.channelId) {
        try {
          const data = await getUserData(user.channelId.toString());
          if (data) {
            setUserData(data);
          }
        } catch (error) {
          console.error('Failed to fetch user data:', error);
        }
      }
    };
    
    fetchUserData();
  }, [user?.channelId]);

  // Add state for audio playback
  const [activeAudioId, setActiveAudioId] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const messageAudioRef = useRef<HTMLAudioElement>(null);
  
  // Add state to track if there's more content to scroll
  const [hasMoreContent, setHasMoreContent] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Function to check if there's more content to scroll
  const checkScrollable = () => {
    const scrollElement = scrollAreaRef.current;
    if (scrollElement) {
      const { scrollTop, scrollHeight, clientHeight } = scrollElement;
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px threshold
      setHasMoreContent(!isNearBottom && scrollHeight > clientHeight);
    }
  };

  // Check scrollable on mount and when starred messages change
  useEffect(() => {
    checkScrollable();
  }, [starred]);

  // Helper function to toggle play/pause
  const togglePlay = (id: string, audioUrl?: string) => {
    if (!audioUrl) return; // Don't do anything if no audio URL

    if (activeAudioId === id) {
      const audio = messageAudioRef.current;
      if (audio) {
        if (audio.paused) {
          audio.play().catch(e => console.error("Error playing audio:", e));
        } else {
          audio.pause();
          // Keep activeAudioId set to allow resuming play
        }
      }
    } else {
      // If switching to a new audio
      setActiveAudioId(id);
      setProgress(0); // Reset progress for new audio
      // useEffect will handle setting src and playing
    }
  };

  // Effect to handle changing audio source and playing
  useEffect(() => {
    const audio = messageAudioRef.current;
    // Find the message matching the active ID
    const activeMessage = starred.find(m => m.Id === activeAudioId);

    if (audio && activeMessage && activeMessage.Audio) {
      // Ensure dataset reflects the current message ID
      audio.dataset.messageId = activeMessage.Id;

      if (audio.src !== activeMessage.Audio) {
        audio.src = activeMessage.Audio;
        audio.load(); // Ensure the new source is loaded
      }
      audio.play().catch(e => {
          console.error("Error playing audio:", e);
          // If play fails, reset state
          setActiveAudioId(null);
          setProgress(0);
      });
    } else if (audio && !activeMessage) {
        // If active message is not found (e.g., list updated), pause and reset
        audio.pause();
        setActiveAudioId(null);
        setProgress(0);
    }
  }, [activeAudioId, starred]);


  // Effect to update progress
  useEffect(() => {
    const audio = messageAudioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      if (audio.duration > 0) {
        setProgress((audio.currentTime / audio.duration) * 100);
      }
    };

    // Check if the audio element corresponds to the currently active message ID
    const isCurrentAudioActive = audio.dataset.messageId === activeAudioId;

    const handleAudioStateChange = () => {
      // Update activeAudioId if playback stops naturally or is paused externally
      // Only reset if the audio element matches the active ID
      if (audio.paused && isCurrentAudioActive) {
        // We don't reset activeAudioId on pause, only on end or error
      }
    };

    audio.addEventListener("timeupdate", updateProgress);
    audio.addEventListener("play", handleAudioStateChange);
    audio.addEventListener("pause", handleAudioStateChange);

    return () => {
      audio.removeEventListener("timeupdate", updateProgress);
      audio.removeEventListener("play", handleAudioStateChange);
      audio.removeEventListener("pause", handleAudioStateChange);
    };
  }, [activeAudioId]); // Depend on activeAudioId to re-attach listeners if needed


  // Effect to handle audio ending
  useEffect(() => {
    const audio = messageAudioRef.current;
    if (!audio) return;

    const handleEnded = () => {
      // Reset state only if the ended audio matches the currently active one
      if (audio.dataset.messageId === activeAudioId) {
        setProgress(0);
        setActiveAudioId(null); // Reset when audio finishes
      }
    };

    audio.addEventListener("ended", handleEnded);
    return () => audio.removeEventListener("ended", handleEnded);
  }, [activeAudioId]); // Add activeAudioId dependency


  // 12 placeholder insights
  const placeholderInsights: StarredMessage[] = Array.from(
    { length: 12 },
    (_, i) => ({
      Id: `placeholder-${i}`,
      Sender: "user",
      Date: new Date().toISOString(),
      Content: `This is a placeholder description for the insight #${i + 1}. It provides a quick overview or summary of the concept or reflection.`,
    })
  );

  // Combine starred messages with placeholders
  const _insights = [...starred]; // Prefixed with underscore to indicate it's intentionally unused
  const insights = [...starred];

  return (
    <Layout user={user} userData={userData} currentPath="integrate">
      <div className="relative flex flex-col items-center pt-4 pb-8 bg-transparent">
        <div className="w-full max-w-xl p-4 sm:p-6 flex flex-col items-center h-full">
          <motion.div 
            className="flex flex-col items-center w-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex w-full justify-center mb-6">
              <div className="flex items-center gap-2">
                <Logo size={32} />
                <span className="text-xl font-semibold text-[#FCA311] pl-1">awaken</span>
              </div>
            </div>
            <Text className="text-2xl font-semibold text-[#FCA311] mb-4">
              Starred Messages
            </Text>

            <div className="relative w-full max-w-xl">
              <ScrollArea 
                className="w-full h-[calc(100vh-200px)] overflow-auto" 
                ref={scrollAreaRef}
                onScroll={checkScrollable}
              >
                <div className="space-y-4">
                  {starred.length === 0 ? (
                    <motion.div
                      className="text-center p-8 text-gray-400"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="mb-4">
                        <StarIcon className="w-12 h-12 mx-auto text-[#FCA311]" />
                      </div>
                      <Text className="text">
                        No starred messages yet. <br />
                        Star important messages in your chat session 
                        to see them appear here.
                      </Text>
                    </motion.div>
                  ) : (
                    starred.map((insight, index) => {
                      const senderLabel = getSenderLabel(insight.Sender, insight.CoachName);
                      const formattedDate = formatInsightDate(insight.Date);
                      const contentClass = getContentTextClass(insight.Sender);

                      return (
                        <motion.div
                          key={insight.Id + index}
                          className="bg-[#1E1E1E] bg-opacity-80 rounded-lg p-4 shadow-md"
                          initial={{ opacity: 0, y: 30 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                        >
                          {/* Sender and Date on one line */}
                          <div className="text-xs font-normal mb-1">
                            <span className="text-[#FCA311] mr-2">{senderLabel}</span>
                            <span className="text-gray-400">{formattedDate}</span>
                          </div>

                          {/* Conditionally render Play/Pause button and Progress bar if Audio exists */}
                          {insight.Audio && (
                            <div className="mt-2">
                              <div className="flex items-center mb-2">
                                <Button
                                  variant="outline"
                                  css={`w-8 h-8 p-1 rounded-full border ${activeAudioId === insight.Id ? 'border-[#FCA311]' : 'border-[#505050]'} bg-[#272727] hover:bg-[#333333] flex items-center justify-center mr-2`}
                                  onClick={() => togglePlay(insight.Id, insight.Audio)}
                                  aria-label={activeAudioId === insight.Id && messageAudioRef.current && !messageAudioRef.current.paused ? "Pause audio" : "Play audio"}
                                >
                                  {activeAudioId === insight.Id && messageAudioRef.current && !messageAudioRef.current.paused ? (
                                    <PauseIcon className="w-4 h-4 text-[#FCA311]" />
                                  ) : (
                                    <PlayIcon className="w-4 h-4 text-[#FCA311]" />
                                  )}
                                </Button>
                                <span className="text-xs text-gray-400">Play voice note</span>
                              </div>
                              {/* Progress Bar */}
                              {activeAudioId === insight.Id && (
                                <div className="h-1 w-full bg-[#505050] rounded-full overflow-hidden mb-2">
                                  <div
                                    className="h-full bg-[#FCA311] transition-width duration-100 ease-linear"
                                    style={{ width: `${progress}%` }}
                                  />
                                </div>
                              )}
                            </div>
                          )}

                          {/* Message on next line */}
                          <div className={`text-sm mt-1 ${contentClass}`}>
                            {insight.Content}
                          </div>
                        </motion.div>
                      );
                    })
                  )}
                </div>
              </ScrollArea>

              {/* Fade effect at bottom when there's more content */}
              {hasMoreContent && (
                <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-black/80 to-transparent pointer-events-none rounded-b-lg" />
              )}
            </div>

            {/* Hidden Audio Element */}
            <audio ref={messageAudioRef} className="hidden" data-message-id={activeAudioId || ''} />
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};