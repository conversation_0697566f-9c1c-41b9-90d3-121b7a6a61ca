import { resolvePath } from "../../@reframe/zero/utils/path.ts";

const commit = JSON.parse(
  Deno.readTextFileSync(
    "./.cache/commit/--@ethan--awaken--main.tsx.json",
  ),
) as Record<string, {
  imports: string[];
}>;

const visited = new Set<string>();

const dfs = (node: string, chain: string[]) => {
  visited.add(node);
  if (!commit[node]) {
    console.warn("missing", node);
    return;
  }
  const deps = commit[node].imports.map((r) => resolvePath(r, node));
  for (const dep of deps) {
    if (chain.includes(dep)) {
      console.log([...chain, dep].join("\n -> "));

      throw new Error("circular");
    }
    if (!visited.has(dep)) {
      dfs(dep, [...chain, dep]);
    }
  }
};

for (const node of Object.keys(commit)) {
  if (!visited.has(node)) {
    dfs(node, []);
  }
}