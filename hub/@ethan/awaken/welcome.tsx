"use client";

import { motion, AnimatePresence } from "npm:framer-motion";
import { PlayIcon } from "@reframe/icons/play.ts";
import { ArrowRightIcon } from "@reframe/icons/arrow-right.ts";
import { PauseIcon } from "@reframe/icons/pause-icon.ts";
import { ParticleField } from "./lib/particles.tsx";
import { useEffect, useRef, useState } from "npm:react@canary";
import React from "npm:react@canary";
import { Steps } from "./routes/onboarding/components/steps.tsx";

import { Volume2Icon } from "@reframe/icons/volume2.ts";

import { VolumeXIcon } from "@reframe/icons/volumeoff.ts";

import { RotateCwIcon } from "@reframe/icons/rotate-cw.ts";
import { Chat } from "./routes/chat/chat.tsx";
import { Logo } from "./lib/logo.tsx";

/* Font loaded globally in head, local preload no longer needed */

const sentences = [
  {
    text: "What would it be like to feel freer, more joyful and in flow with life?",
    timestamp: 0,
  },
  { text: "What if you could dissolve old limitations...", timestamp: 6 },
  { text: "And what would that unlock?", timestamp: 7 },
  {
    text: "Imagine a space where struggles empower you",
    timestamp: 10,
  },
  {
    text: "and you have a guide in every moment.",
    timestamp: 13,
  },
  { text: "This is that space.", timestamp: 16 },
  { text: "I am your guide.", timestamp: 19 },
  { text: "Shall we begin?", timestamp: 21 },
];

const PowerfulMessage = ({ setQuestion, setShowPowerMessage }) => {
  const [currentSentence, setCurrentSentence] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  const [isMuted, setIsMuted] = useState(false); // New state to manage mute/unmute
  const audioRef = useRef(null); // Reference for the audio element

  useEffect(() => {
    if (currentSentence < sentences.length - 1) {
      // Calculate the delay based on the difference between the current and next sentence timestamps
      const delay =
        (sentences[currentSentence + 1].timestamp -
          sentences[currentSentence].timestamp) *
        1000;

      const timer = setTimeout(() => {
        setCurrentSentence((prev) => prev + 1);
      }, delay);

      return () => clearTimeout(timer);
    } else {
      setIsComplete(true); // Mark as complete when the last sentence is reached
    }
  }, [currentSentence]);

  const progress = currentSentence / (sentences.length - 1);

  const handleRotateClick = () => {
    if (audioRef.current) {
      console.log("replaying");
      audioRef.current.pause(); // Pause the audio
      audioRef.current.currentTime = 0; // Reset audio to start
    }

    setCurrentSentence(0); // Reset the sentence

    // Delay playing the audio to ensure DOM updates are completed
    setTimeout(() => {
      if (audioRef.current) {
        audioRef.current.play(); // Play the audio after small delay
      }
    }, 0); // Small delay to let React fully update
  };

  return (
    <motion.div
      key={"powerfulMessage"}
      className="relative flex flex-col items-center justify-center min-h-svh max-h-svh text-orange-300 p-6 component-b overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 1 }}
    >
      {/* Background audio */}
      <audio
        ref={audioRef}
        src="https://ik.imagekit.io/dns5janxf/awaken%20-%20intro%20audio%201.mp3?updatedAt=1728996829426"
        muted={isMuted} // Mute the audio by default
        autoPlay
      />

      {/* Volume Icon */}
      <div className="absolute top-6 right-6 z-10 cursor-pointer">
        {isMuted ? (
          <VolumeXIcon
            className="w-8 h-8"
            color="#FFC07E"
            onClick={() => {
              audioRef.current.muted = false;
              setIsMuted(false);
            }}
          />
        ) : (
          <Volume2Icon
            className="w-8 h-8"
            color="#FFC07E"
            onClick={() => {
              audioRef.current.muted = true;
              setIsMuted(true);
            }}
          />
        )}
      </div>

      {/* Upper circle */}
      <motion.div
        className="absolute w-[500px] h-[500px] rounded-full blur-[100px]"
        style={{
          top: "-30%", // Adjusted for mobile
          background: "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
        }}
        initial={{ opacity: 0.1 }}
        animate={{ opacity: 0.4 }}
        transition={{ duration: 0 }}
      />

      {/* Message content */}
      {/* Text Animation with Staggered Sentences */}
      <motion.div
        key={"powerfulMessageContent"}
        className="max-w-3xl w-full space-y-6 text-center z-10 text-[#FCA311]"
        initial={{ y: 0 }}
        animate={{ y: isComplete ? -30 : 0 }} // Move text up when complete
        transition={{ duration: 2, ease: "easeInOut" }} // Extended duration for smoother movement
      >
        {sentences.map((sentence, index) => (
          <motion.p
            key={sentence.text}
            initial={{ opacity: 0, y: 20 }} // Start text from a slightly lower position
            animate={{ opacity: index <= currentSentence ? 1 : 0.1, y: 0 }} // Smoothly bring it to normal position
            transition={{
              duration: 1.5,
              ease: "easeInOut",
              delay: index * 0.2, // Stagger effect for each sentence
            }}
            className="text-sm md:text-xl font-light"
          >
            {sentence.text}
          </motion.p>
        ))}
      </motion.div>

      {/* Bottom "house shape" */}
      {/* <motion.div
        className="absolute bottom-0 w-full h-[500px] md:h-[715px] pointer-events-none"
        initial={{ opacity: 0.1 }}
        animate={{ opacity: 0.2 + 0.2 * progress }}
        transition={{ duration: 0 }}
        style={{
          top: "75%",
          width: "100%", // Make it full width on mobile
          maxWidth: "1030px",
          filter: "blur(100px)",
        }}
      >
        <div
          className="absolute"
          style={{
            width: "100%",
            maxWidth: "1030px",
            height: "100%",
            background:
              "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
            clipPath: "polygon(0% 20%, 50% 0%, 100% 20%, 100% 100%, 0% 100%)",
            left: "50%",
            transform: "translateX(-50%)",
          }}
        />
      </motion.div> */}

      {/* Continue Button */}
      <div className="mt-8" style={{ minHeight: "88px" }}>
        {isComplete ? (
          <>
            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 1.5,
                ease: "easeInOut",
                delay: 0.5, // Delay the button appearance for smoother transition after text
              }}
              className="mt-8 px-6 py-2 bg-gradient-to-r from-[#00000033] to-[#5C3B0633] border border-0.5 border-[#FCA311] text-white rounded-full flex items-center space-x-2 transition-colors z-10 hover:bg-[#5C3B06] hover:border-[#FCA311]"
              onClick={() => {
                // setQuestion(1);
                // setShowPowerMessage(false);
                globalThis.location.href = "/auth/sign-in";
              }}
            >
              <span>Continue</span>
              <ArrowRightIcon className="w-4 h-4" />
            </motion.button>
            <AnimatePresence>
              <motion.div
                key="refresh"
                initial={{ opacity: 1 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }} // Smooth fade out on exit
                transition={{ duration: 2, ease: "easeInOut", delay: 0.5 }} // Smooth transition
                className="absolute flex flex-col text-center justify-center gap-2 bottom-6 left-1/2 transform -translate-x-1/2 items-center z-10"
              >
                <motion.div
                  className="flex flex-row gap-2 rounded-full border-[#FFC07E] text-orange-300 hover:text-orange-400 transition-colors p-3 cursor-pointer"
                  onClick={() => {
                    handleRotateClick();
                  }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <RotateCwIcon className="w-4 h-4" color="#9B9B9B" />{" "}
                  <motion.span className="text-sm text-[#9B9B9B]">
                    Replay
                  </motion.span>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          </>
        ) : (
          <motion.div
            key="skipOnboarding"
            initial={{ opacity: 1 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }} // Smooth fade out on exit
            transition={{ duration: 2, ease: "easeInOut", delay: 0.5 }} // Smooth transition
            className="absolute flex flex-col text-center justify-center gap-2 bottom-6 left-1/2 transform -translate-x-1/2 items-center z-10"
          >
            <motion.div
              className="rounded-full border-[#FFC07E] text-orange-300 hover:text-orange-400 transition-colors p-3 cursor-pointer"
              onClick={() => {
                // setQuestion(1);
                setShowPowerMessage(false);
                globalThis.location.href = "/auth/sign-in";
              }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <motion.span className="text-sm text-[#9B9B9B]">
                Skip to onboarding
              </motion.span>
            </motion.div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export const Welcome = () => {
  const [showPowerMessage, setShowPowerMessage] = useState(false);
  const [opacity, setOpacity] = useState(0.1);

  const [question, setQuestion] = useState(0);

  const [increasing, setIncreasing] = useState(true);

  useEffect(() => {
    const interval = setInterval(() => {
      setOpacity((prevOpacity) => {
        if (increasing) {
          if (prevOpacity >= 0.7) {
            setIncreasing(false);
            return 0.7;
          }
          return prevOpacity + 0.005;
        } else {
          if (prevOpacity <= 0.1) {
            setIncreasing(true);
            return 0.1;
          }
          return prevOpacity - 0.005;
        }
      });
    }, 100);

    return () => clearInterval(interval);
  }, [increasing]);

  const handlePlayClick = () => {
    setShowPowerMessage(true);
  };

  /* Font preloading handled globally; previous effect removed to prevent layout shift */

  return (
    <div className="relative min-h-svh max-h-svh bg-black overflow-hidden justify-center items-center touch-none" style={{ fontFamily: '"Rethink Sans", sans-serif' }}>
      <ParticleField /> {/* Ensure this stays in the background */}
      
      {/* Login link at the bottom */}
      {!showPowerMessage && (
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20 text-sm">
          <span className="text-white">Already registered? </span>
          <motion.span 
            className="text-orange-300 hover:text-orange-400 transition-colors cursor-pointer"
            onClick={() => globalThis.location.href = "/auth/sign-in"}
            whileHover={{ scale: 1.05 }}
          >
            Login
          </motion.span>
        </div>
      )}
      
      {!showPowerMessage && question === 0 ? (
        <AnimatePresence mode="wait">
          <motion.div
            key="onboard"
            className="relative flex flex-col items-center justify-center min-h-svh text-white"
            initial={{ opacity: 1 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 1 }}
          >
            <motion.div
              className="absolute inset-0 m-auto w-[467px] h-[467px] rounded-full"
              initial={{ opacity: 0.1 }}
              style={{
                background: "linear-gradient(135deg, #FF5727 0%, #FFC360 100%)",
                filter: "blur(80px)",
              }}
              animate={{ opacity: opacity }} // Framer Motion handling opacity
              transition={{ duration: 1 }}
            />

            <div className="relative z-10 flex flex-col items-center text-center px-6">
              <div className="mb-16 flex items-center space-x-2">
                <Logo size={25} />
                <span className="text-xl font-normal">awaken</span>
              </div>
              <h1 className="mb-4 text-2xl font-semibold md:text-6xl">
                <span></span>
                <span className="bg-gradient-to-r from-[#FF5727] to-[#FFC360] bg-clip-text text-transparent">
                  Awaken freedom.<br />Transform fear.
                </span>
              </h1>
              <p className="mb-12 text-xl">
                Awaken AI walks with you 24/7, guiding you to love, presence, and inner power in the raw moments of daily life.
              </p>
              <button
                className="rounded-full flex items-center justify-center border border-0.5 border-[#FFC07E] p-4 transition-transform hover:scale-110"
                aria-label="Play"
                onClick={handlePlayClick}
              >
                <PlayIcon
                  className="h-8 w-8 fill-[#FFBA5B] ml-0.5"
                  color="#FFBA5B"
                />
              </button>
            </div>
          </motion.div>
        </AnimatePresence>
      ) : showPowerMessage ? (
        <PowerfulMessage
          setQuestion={setQuestion}
          setShowPowerMessage={setShowPowerMessage}
        />
      ) : null}
    </div>
  );
};
