"use server";

/**
 * Simple email sending utilities for friend-like, minimally styled emails
 */

import Reframe from "@";

/**
 * Converts plain text to simple HTML, preserving line breaks and basic formatting
 * Supports **bold**, *italic*, and [link](url) markdown-style formatting
 */
function textToSimpleHtml(text: string): string {
  return text
    .split('\n\n')
    .filter(para => para.trim() !== '')
    .map(para => {
      const trimmedPara = para.trim();

      // Convert '---' to <hr>
      if (trimmedPara === '---') {
        return '<hr style="border: none; border-top: 1px solid #ddd; margin: 2em 0;">';
      }

      // Convert [text](url) to <a href="url">text</a>
      para = para.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" style="color: #0066cc; text-decoration: none;">$1</a>');
      // Convert **text** to <strong>text</strong>
      para = para.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
      // Convert *text* to <em>text</em>
      para = para.replace(/\*([^*]+)\*/g, '<em>$1</em>');

      return `<p>${para.replace(/\n/g, '<br>')}</p>`;
    })
    .join('\n');
}

/**
 * Sends a simple, friend-like email without heavy styling
 * Perfect for organic emails that should feel personal and authentic
 * 
 * @param email - Recipient email address
 * @param subject - Email subject line
 * @param message - Plain text message (supports markdown: **bold**, *italic*, and [links](url))
 * @param fromName - Optional sender name (defaults to "Kokoro")
 * @returns Postmark API response
 */
export const sendSimpleEmail = async (
  email: string, 
  subject: string, 
  message: string, 
  fromName: string = "Kokoro"
) => {
  try {
    console.log("[SIMPLE_EMAIL] Sending email to:", email);
    console.log("[SIMPLE_EMAIL] Subject:", subject);
    
    // Convert message to simple HTML
    const htmlContent = textToSimpleHtml(message);
    
    // Create minimal HTML email
    const emailHtml = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; color: #333; line-height: 1.8; font-size: 16px; max-width: 600px; margin: 0 auto; padding: 20px;">
  ${htmlContent}
  <p style="margin-top: 30px;">
    <a href="https://awaken.is/chat" style="color: #0066cc; text-decoration: underline;">Reply</a>
  </p>
  <p style="margin-top: 50px; font-size: 12px; color: #666;">
    © 2025 Awaken. All rights reserved.<br>
    <a href="https://awaken.is/chat" style="color: #666;">Manage email settings in the menu</a>
  </p>
</body>
</html>`;

    const postmarkBody = {
      From: `${fromName} <<EMAIL>>`,
      To: email,
      Subject: subject,
      HtmlBody: emailHtml,
      TextBody: message, // Plain text fallback
      MessageStream: 'outbound'
    };

    console.log("[SIMPLE_EMAIL] Sending email via Postmark");

    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': Reframe.env.POSTMARK_API_TOKEN
      },
      body: JSON.stringify(postmarkBody)
    });

    const result = await response.json();
    console.log("[SIMPLE_EMAIL] Email sent successfully:", result);
    return result;
  } catch (error) {
    console.error("[SIMPLE_EMAIL] Error sending email:", error);
    throw error;
  }
};