"use client";

import React, { useEffect } from "npm:react@canary";
import { useSpring, animated, config } from "npm:@react-spring/web";
import { CheckIcon } from "@reframe/icons/check.ts";

interface SuccessToastProps {
  message: string;
  onClose: () => void;
  duration?: number;
}

export const SuccessToast: React.FC<SuccessToastProps> = ({ 
  message, 
  onClose, 
  duration = 5000 
}) => {
  useEffect(() => {
    const timer = setTimeout(onClose, duration);
    return () => clearTimeout(timer);
  }, [onClose, duration]);

  const springProps = useSpring({
    from: { opacity: 0, transform: 'translate(-50%, -20px)' },
    to: { opacity: 1, transform: 'translate(-50%, 0px)' },
    config: config.gentle,
    onRest: () => {
      // Optional: Start exit animation after duration
      setTimeout(onClose, duration);
    },
  });

  return (
    <animated.div
      style={{
        ...springProps,
        position: 'fixed',
        top: '1rem',
        left: '50%',
        zIndex: 50,
      }}
    >
      <div className="bg-[#1a1a1a] border border-[#FCA31133] rounded-lg shadow-lg px-4 py-3 flex items-center gap-3">
        <div className="bg-[#FCA311] rounded-full p-1">
          <CheckIcon className="w-4 h-4 text-black" />
        </div>
        <span 
          className="text-white"
          dangerouslySetInnerHTML={{ __html: message }}
        />
      </div>
    </animated.div>
  );
}; 