"use server";

import Reframe from "@";

export const geminiRequest = async (
  requests: any, 
  model: string = "gemini-2.5-flash",
  structuredOutput: boolean = false
) => {
  const API_KEY = Reframe.env.GEMINI_API_KEY;
  const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${API_KEY}`;

  // console.log("requests", requests);

  // Helper function to call Gemini with a full JSON payload.
  async function callGemini(fullPayload: any): Promise<any> {
    // Add structured output config if requested
    if (structuredOutput) {
      fullPayload.generationConfig = {
        ...fullPayload.generationConfig,
        responseMimeType: "application/json"
      };
    }
    
    // console.log("fullPayload", fullPayload);
    const response = await fetch(geminiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(fullPayload)
    });
    if (!response.ok) {
      // show full error
      throw new Error(`Gemini API error: ${response.status}`);
    }
    return response.json();
  }

  try {
    let geminiData;
    // If the request body contains a "requests" array, process in parallel.
    geminiData = await Promise.all(
      requests.map((payload: any) => callGemini(payload))
    );
    // console.log("Full Gemini response:", geminiData);

    // Normalize geminiData to an array.
    const dataArray = geminiData;

    // Extract the text output from each response.
    const extractedTexts = dataArray.map((result: any) => {
      const textContent = result?.candidates?.[0]?.content?.parts?.[0]?.text || "";
      
      // If structured output is requested, try to parse as JSON
      if (structuredOutput && textContent) {
        try {
          return JSON.parse(textContent);
        } catch (error) {
          console.warn("Failed to parse structured output as JSON, returning raw text:", error);
          return textContent;
        }
      }
      
      return textContent;
    });

    // console.log("extractedTexts", extractedTexts);

    // Build an object with separate keys for each text output.
    const textOutputs: Record<string, any> = {};
    extractedTexts.forEach((text, index) => {
      textOutputs[`text${index + 1}`] = text;
    });

    // console.log("textOutputs", textOutputs);
    // Return the text outputs as separate objects in the JSON.
    return Response.json({
      success: "true",
      ...textOutputs
    });
  } catch (error) {
    console.error("Error in Gemini route:", error);
    return Response.json(
      { success: "false", error: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * Call OpenRouter API with specified model
 */
export async function openRouterRequest(modelId: string, systemInstruction: string, userPrompt: string) {
  try {
    console.log(`[OPENROUTER] Calling OpenRouter API with model: ${modelId}`);
    
    // OpenRouter API configuration
    const API_KEY = Reframe.env.OPENROUTER_API_KEY;
    const openRouterUrl = "https://openrouter.ai/api/v1/chat/completions";
    
    // Simple messages array
    const messages = [
      {
        role: "system",
        content: systemInstruction
      },
      {
        role: "user",
        content: userPrompt
      }
    ];
    
    // Prepare the request payload for OpenRouter
    const payload = {
      model: modelId,
      messages: messages,
      temperature: 1.0,
      max_tokens: 5000,
      reasoning: {
        effort: "low"
      }
    };
    
    // Make the API request to OpenRouter
    console.log("[OPENROUTER] Payload:", JSON.stringify(payload, null, 2));
    const response = await fetch(openRouterUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${API_KEY}`
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenRouter API error: ${response.status} ${errorText}`);
    }
    
    const apiResponse = await response.json();
    
    // Extract the response text
    let responseText = "";
    if (apiResponse.choices && apiResponse.choices.length > 0) {
      responseText = apiResponse.choices[0].message.content;
    }
    
    if (!responseText) {
      throw new Error(`No response was generated by ${modelId}`);
    }
    
    return responseText;
  } catch (error) {
    console.error(`[OPENROUTER] Error:`, error);
    throw error;
  }
}

// 1A – new raw helper ─ handles full Anthropic payload
export interface ClaudePayload {
  system: string;
  messages: any[];
  tools?: any[];
  tool_choice?: any;
  thinking?: any;
  temperature?: number;
  max_tokens?: number;
  model?: string;
}

export async function claudeRequestRaw(p: ClaudePayload) {
  const {
    system,
    messages,
    tools,
    tool_choice,
    thinking,
    temperature = 1.0,
    max_tokens = 3500,
    model = "claude-3-7-sonnet-20250219"
  } = p;

  const body = {
    model,
    temperature,
    max_tokens,
    system,
    messages,
    ...(tools && { tools }),
    ...(tool_choice && { tool_choice }),
    ...(thinking && { thinking }),
  };

  const res = await fetch("https://api.anthropic.com/v1/messages", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "x-api-key": Reframe.env.CLAUDE_API_KEY,
      "anthropic-version": "2023-06-01",
      "anthropic-beta": "prompt-caching-2024-07-31",
    },
    body: JSON.stringify(body),
  });
  if (!res.ok) throw new Error(`Claude API ${res.status}: ${await res.text()}`);
  return res.json(); // raw JSON (stop_reason, tool_use blocks, etc.)
}

// 1B – keep legacy wrapper exactly as old callers expect
export async function claudeRequest(systemPrompt: string, messageArray: any[]) {
  const json = await claudeRequestRaw({
    system: systemPrompt,
    messages: messageArray,
    temperature: 0.4, // Keep original temperature for backward compatibility
  });
  return json.content[0].text; // unchanged behaviour
}

export async function sendLog(message: string, subject: string = "[LOG FROM AWAKEN]") {
  const postmarkBody = {
    From: '<EMAIL>',
    To: "<EMAIL>",
    Subject: `${subject}`,
    TextBody: message,
    MessageStream: 'outbound'
  };

  const response = await fetch('https://api.postmarkapp.com/email', {
    method: 'POST',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'X-Postmark-Server-Token': Reframe.env.POSTMARK_API_TOKEN
    },
    body: JSON.stringify(postmarkBody)
  });

  const result = await response.json();
  console.log("[EMAIL] Email sent successfully:", result);
  console.log("[EMAIL] Final subject sent:", postmarkBody.Subject);
}