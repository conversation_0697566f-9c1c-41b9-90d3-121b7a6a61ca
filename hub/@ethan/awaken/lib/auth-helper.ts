"use server"

import { getAuth } from "@reframe/auth/auth.ts";
import { getUserInfo } from "./get-user-info.ts";
import { SUPERUSER_EMAILS } from "./server-constants.ts";

export const getAuthenticatedUser = async () => {
  const auth = await getAuth();
  
  // console.log("auth", auth);

  if (!auth.authenticated) {
    throw new Error("unauthorized");
  }

  const user = await getUserInfo({
    email: auth.session.email,
  });

  // console.log("THIS CALLED");
  // console.log("user", user);
  return user;
};

// keep it async, is used from client as server action
export const checkIsSuperUser = async (email: string): Promise<boolean> => {
  try {
    // Normalize the input email
    const normalizedEmail = email.toLowerCase().trim();
    
    // Normalize the SUPERUSER_EMAILS list
    const lowerCaseSuperUserEmails = SUPERUSER_EMAILS.map(e => e.toLowerCase().trim());
    
    // Log the values being compared
    // console.log(`[USER] Normalized email for check: '${normalizedEmail}'`);
    // console.log(`[USER] Lowercase Superuser Emails:`, lowerCaseSuperUserEmails);

    // Perform the check
    const isSuper = lowerCaseSuperUserEmails.includes(normalizedEmail);
    
    console.log(`[USER] Checking superuser status for ${email} (normalized: ${normalizedEmail}): ${isSuper}`);
    return isSuper;
  } catch (error) {
    console.error(`[USER] Error checking superuser status for ${email}:`, error);
    // Default to false in case of error for security
    return false; 
  }
};

export const verifyUserPermission = async (channelId?: string) => {
  const user = await getAuthenticatedUser();
  if(await checkIsSuperUser(user.email)) return true;
  if(user?.channelId !== Number(channelId)) {
    console.log("ACTION AUTHENTICATION CHECK FAILED for channelId:", channelId)
    return false;
  }
  return true;
}