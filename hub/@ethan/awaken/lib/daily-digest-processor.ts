"use server";
import Reframe from "@";

import { 
  getUsersForDigest, 
  getUnreadMessagesForDigest, 
  hasDigestBeenSent, 
  recordDigestSent,
  generateDigestSubject,
  generateDigestEmailBody,
  cleanupOldDigests,
  type UserDigest 
} from "./daily-digest.ts";
import { sendSimpleEmail } from "./email-sender.ts";

/**
 * Check if it's 3pm in the user's timezone
 * 
 * @param timezone - User's timezone (e.g., "America/New_York")
 * @returns true if it's currently 3pm in that timezone
 */
export const isThreePmInTimezone = (timezone: string): boolean => {
  try {
    const now = new Date();
    const userTime = new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      hour: 'numeric',
      hour12: false
    }).format(now);
    
    const currentHour = parseInt(userTime);
    return currentHour === 15; // 3pm in 24-hour format
  } catch (error) {
    console.error(`[DIGEST] Error checking time for timezone ${timezone}:`, error);
    return false;
  }
};

/**
 * Get the local date string for a user's timezone
 * 
 * @param timezone - User's timezone
 * @returns Local date string (e.g., "Monday, January 15, 2025")
 */
export const getLocalDateString = (timezone: string): string => {
  try {
    const now = new Date();
    return new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(now);
  } catch (error) {
    console.error(`[DIGEST] Error getting local date for timezone ${timezone}:`, error);
    return new Date().toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
};

/**
 * Get the 24-hour window for checking messages (from 3pm yesterday to 3pm today in user's timezone)
 * 
 * @param timezone - User's timezone
 * @returns Object with windowStart and windowEnd as ISO strings
 */
export const getDigestTimeWindow = (timezone: string): { windowStart: string; windowEnd: string } => {
  try {
    const now = new Date();
    
    // Get current time in user's timezone
    const userNow = new Date(now.toLocaleString("en-US", { timeZone: timezone }));
    
    // Set to 3pm today in user's timezone
    const threePmToday = new Date(userNow);
    threePmToday.setHours(15, 0, 0, 0);
    
    // Set to 3pm yesterday in user's timezone
    const threePmYesterday = new Date(threePmToday);
    threePmYesterday.setDate(threePmYesterday.getDate() - 1);
    
    // Convert back to UTC for database queries
    const windowStart = new Date(threePmYesterday.getTime() - (threePmYesterday.getTimezoneOffset() * 60000)).toISOString();
    const windowEnd = new Date(threePmToday.getTime() - (threePmToday.getTimezoneOffset() * 60000)).toISOString();
    
    return { windowStart, windowEnd };
  } catch (error) {
    console.error(`[DIGEST] Error calculating time window for timezone ${timezone}:`, error);
    
    // Fallback to UTC-based calculation
    const now = new Date();
    const windowEnd = now.toISOString();
    const windowStart = new Date(now.getTime() - (24 * 60 * 60 * 1000)).toISOString();
    
    return { windowStart, windowEnd };
  }
};

/**
 * Process daily digest for a single user
 * 
 * @param user - User data including channelId, email, timezone, etc.
 * @returns Object with success status and details
 */
export const processUserDigest = async (user: {
  channelId: number;
  email: string;
  name: string;
  timezone: string;
  emailDaily: number;
}): Promise<{
  success: boolean;
  skipped?: string;
  emailSent?: boolean;
  messageCount?: number;
  coachCount?: number;
  error?: string;
}> => {
  try {
    const { channelId, email, timezone } = user;

    // if(channelId !== 865549891) {
    //   return {
    //     success: false,
    //     skipped: "Not testing user"
    //   }
    // }

    console.log(`[DIGEST] Processing user ${channelId} (${email}) in timezone ${timezone}`);
    
    console.log(`[DIGEST] Checking if it's 3pm in timezone ${timezone}`);
    console.log(`[DIGEST] isThreePmInTimezone(timezone): ${isThreePmInTimezone(timezone)}`);
    
    // Check if it's 3pm in user's timezone
    if (!isThreePmInTimezone(timezone)) {
      return { 
        success: true, 
        skipped: `Not 3pm in timezone ${timezone}` 
      };
    }
    
    // Get today's date in YYYY-MM-DD format for the user's timezone
    const today = new Date().toLocaleDateString('en-CA', { timeZone: timezone }); // en-CA gives YYYY-MM-DD format
    
    // Check if digest was already sent today
    if (await hasDigestBeenSent(channelId.toString(), today)) {
      return { 
        success: true, 
        skipped: `Digest already sent for ${today}` 
      };
    }
    
    // Get the 24-hour window for checking messages
    const { windowStart, windowEnd } = getDigestTimeWindow(timezone);
    
    console.log(`[DIGEST] Checking messages for user ${channelId} between ${windowStart} and ${windowEnd}`);
    
    // Get unread messages for this user
    const userDigest = await getUnreadMessagesForDigest(channelId, windowStart, windowEnd);
    
    if (!userDigest || userDigest.totalMessageCount === 0) {
      return { 
        success: true, 
        skipped: "No unread messages in the last 24 hours" 
      };
    }

    console.log(`[DIGEST] Found ${userDigest.totalMessageCount} unread messages from ${userDigest.coachCount} coaches for user ${channelId}`);
    
    // Generate email content
    const subject = generateDigestSubject(userDigest);
    const localDate = getLocalDateString(timezone);
    const emailBody = generateDigestEmailBody(userDigest, localDate);
    
    console.log(`[DIGEST] Sending digest email to ${email} with subject: ${subject}`);
    
    // Send the email
    await sendSimpleEmail(email, subject, emailBody, "Awaken");
    
    // Record that the digest was sent
    await recordDigestSent(
      channelId.toString(), 
      today, 
      userDigest.totalMessageCount, 
      userDigest.coachCount
    );
    
    console.log(`[DIGEST] ✅ Successfully sent digest to user ${channelId} (${email})`);
    
    return {
      success: true,
      emailSent: true,
      messageCount: userDigest.totalMessageCount,
      coachCount: userDigest.coachCount
    };
    
  } catch (error) {
    console.error(`[DIGEST] ❌ Error processing digest for user ${user.channelId}:`, error);
    return {
      success: false,
      error: error.message || "Unknown error"
    };
  }
};

/**
 * Main function to process daily digests for all eligible users
 * This function should be called by the cron job every 30 minutes
 */
export const processDailyDigests = async (): Promise<void> => {
  try {
    console.log("[DIGEST] Starting daily digest processing...");
    
    // Clean up old digest records first
    await cleanupOldDigests();
    
    // Get all users who have daily email enabled
    const users = await getUsersForDigest();
    
    if (users.length === 0) {
      console.log("[DIGEST] No users found with daily email enabled");
      return;
    }
    
    console.log(`[DIGEST] Processing ${users.length} users for daily digest`);
    
    // Process each user
    const results = await Promise.allSettled(
      users.map(user => processUserDigest(user))
    );
    
    // Summarize results
    let successCount = 0;
    let emailsSent = 0;
    let skippedCount = 0;
    let errorCount = 0;
    let totalMessages = 0;
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const { success, emailSent, skipped, messageCount, error } = result.value;
        
        if (success) {
          successCount++;
          if (emailSent) {
            emailsSent++;
            totalMessages += messageCount || 0;
          } else if (skipped) {
            skippedCount++;
          }
        } else {
          errorCount++;
          console.error(`[DIGEST] Error for user ${users[index].channelId}: ${error}`);
        }
      } else {
        errorCount++;
        console.error(`[DIGEST] Promise rejected for user ${users[index].channelId}:`, result.reason);
      }
    });
    
    console.log(`[DIGEST] Daily digest processing complete:`);
    console.log(`[DIGEST] - ${successCount}/${users.length} users processed successfully`);
    console.log(`[DIGEST] - ${emailsSent} digest emails sent`);
    console.log(`[DIGEST] - ${totalMessages} total messages in sent digests`);
    console.log(`[DIGEST] - ${skippedCount} users skipped (wrong time or no messages)`);
    console.log(`[DIGEST] - ${errorCount} errors`);
    
  } catch (error) {
    console.error("[DIGEST] Error in daily digest processing:", error);
  }
};
