"use server";

/**
 * Helper functions for managing daily awakening patterns
 */

interface Message {
  Date: string;
  Sender: string;
  Type?: string;
  message_type?: string;
  MessageType?: string;
  Content: string;
}

/**
 * Determines if a user should receive a daily awakening based on the 1st, 3rd, and 7th day pattern
 * 
 * New Schedule:
 * - Day 1 after last interaction: Send 1st awakening
 * - Day 3 after last interaction: Send 2nd awakening  
 * - Day 7 after last interaction: Send 3rd awakening
 * - After day 7: Stop sending awakenings until user interacts again
 * 
 * @param messagesData - Array of user messages
 * @param channelId - User's channel ID for logging
 * @returns Object with shouldSendAwakening boolean and reason string
 */
export const shouldSendAwakeningOnSchedule = (messagesData: Message[], channelId: string | number): { shouldSendAwakening: boolean; reason: string; daysSinceLastInteraction?: number } => {
  console.log(`[AWAKENING_HELPER] Checking awakening schedule for user ${channelId}`);
  
  if (!Array.isArray(messagesData) || messagesData.length === 0) {
    console.log(`[AWAKENING_HELPER] User ${channelId} - No messages found, allowing first awakening`);
    return { shouldSendAwakening: true, reason: "No messages found - first awakening", daysSinceLastInteraction: 0 };
  }

  // Reverse the array to get newest messages first
  const reversedMessages = [...messagesData].reverse();
  
  // Determine which property to check for message type
  const messageTypeProperty = reversedMessages[0].Type !== undefined ? 'Type' : 
                             reversedMessages[0].message_type !== undefined ? 'message_type' : 
                             reversedMessages[0].MessageType !== undefined ? 'MessageType' : null;

  if (!messageTypeProperty) {
    console.log(`[AWAKENING_HELPER] User ${channelId} - Cannot determine message type property. Message keys:`, 
      Object.keys(reversedMessages[0]));
    return { shouldSendAwakening: true, reason: "Cannot determine message type - allowing awakening" };
  }

  // Find the last user interaction (non-daily-awakening message)
  let lastUserInteractionIndex = -1;
  let lastUserInteractionDate: Date | null = null;
  
  for (let i = 0; i < reversedMessages.length; i++) {
    const message = reversedMessages[i];
    const messageType = message[messageTypeProperty];
    
    // If it's not a daily awakening, it's a user interaction
    if (messageType !== 'daily_awakening') {
      lastUserInteractionIndex = i;
      lastUserInteractionDate = new Date(message.Date);
      break;
    }
  }

  // If no user interaction found, don't send awakening (this shouldn't happen in practice)
  if (lastUserInteractionIndex === -1 || !lastUserInteractionDate) {
    console.log(`[AWAKENING_HELPER] User ${channelId} - No user interactions found`);
    return { shouldSendAwakening: false, reason: "No user interactions found" };
  }

  // Count daily awakenings since the last user interaction
  const awakeningsSinceLastInteraction = lastUserInteractionIndex;
  
  // Calculate days since last interaction
  const now = new Date();
  const daysSinceLastInteraction = Math.floor((now.getTime() - lastUserInteractionDate.getTime()) / (1000 * 60 * 60 * 24));
  
  console.log(`[AWAKENING_HELPER] User ${channelId} - Days since last interaction: ${daysSinceLastInteraction}`);
  console.log(`[AWAKENING_HELPER] User ${channelId} - Awakenings since last interaction: ${awakeningsSinceLastInteraction}`);

  // Define the awakening schedule: send on days 1, 3, and 7
  const awakeningSchedule = [1, 3, 7];
  
  // Check if we should send an awakening based on the schedule
  if (awakeningSchedule.includes(daysSinceLastInteraction)) {
    // Get the expected number of awakenings for this day
    const expectedAwakenings = awakeningSchedule.filter(day => day <= daysSinceLastInteraction).length;
    
    // Check if we've already sent the required number of awakenings
    if (awakeningsSinceLastInteraction < expectedAwakenings) {
      console.log(`[AWAKENING_HELPER] User ${channelId} - Should send awakening for day ${daysSinceLastInteraction} (${awakeningsSinceLastInteraction}/${expectedAwakenings} sent)`);
      return { 
        shouldSendAwakening: true, 
        reason: `Scheduled awakening for day ${daysSinceLastInteraction}`,
        daysSinceLastInteraction 
      };
    } else {
      console.log(`[AWAKENING_HELPER] User ${channelId} - Already sent awakening for day ${daysSinceLastInteraction} (${awakeningsSinceLastInteraction}/${expectedAwakenings} sent)`);
      return { 
        shouldSendAwakening: false, 
        reason: `Already sent awakening for day ${daysSinceLastInteraction}`,
        daysSinceLastInteraction 
      };
    }
  }

  // If it's past day 7, stop sending awakenings
  if (daysSinceLastInteraction > 7) {
    console.log(`[AWAKENING_HELPER] User ${channelId} - Past day 7 (${daysSinceLastInteraction} days), stopping awakenings`);
    return { 
      shouldSendAwakening: false, 
      reason: `Past day 7 (${daysSinceLastInteraction} days) - stopping awakenings`,
      daysSinceLastInteraction 
    };
  }

  // For days not in the schedule (2, 4, 5, 6), don't send
  console.log(`[AWAKENING_HELPER] User ${channelId} - Day ${daysSinceLastInteraction} not in schedule, skipping`);
  return { 
    shouldSendAwakening: false, 
    reason: `Day ${daysSinceLastInteraction} not in awakening schedule`,
    daysSinceLastInteraction 
  };
};

/**
 * Test function to verify the awakening schedule logic
 * This can be called manually to test different scenarios
 */
export const testAwakeningSchedule = () => {
  console.log("Testing awakening schedule logic...");
  
  // Test scenarios
  const testCases = [
    {
      name: "No messages",
      messages: [],
      expected: true
    },
    {
      name: "Day 1 - should send first awakening",
      messages: [
        { Date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), Sender: "user", Type: "message", Content: "Hello" }
      ],
      expected: true
    },
    {
      name: "Day 1 - already sent awakening",
      messages: [
        { Date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), Sender: "user", Type: "message", Content: "Hello" },
        { Date: new Date().toISOString(), Sender: "assistant", Type: "daily_awakening", Content: "Awakening" }
      ],
      expected: false
    },
    {
      name: "Day 2 - should not send",
      messages: [
        { Date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), Sender: "user", Type: "message", Content: "Hello" },
        { Date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), Sender: "assistant", Type: "daily_awakening", Content: "Awakening" }
      ],
      expected: false
    },
    {
      name: "Day 3 - should send second awakening",
      messages: [
        { Date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), Sender: "user", Type: "message", Content: "Hello" },
        { Date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), Sender: "assistant", Type: "daily_awakening", Content: "Awakening 1" }
      ],
      expected: true
    },
    {
      name: "Day 7 - should send third awakening",
      messages: [
        { Date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), Sender: "user", Type: "message", Content: "Hello" },
        { Date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(), Sender: "assistant", Type: "daily_awakening", Content: "Awakening 1" },
        { Date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), Sender: "assistant", Type: "daily_awakening", Content: "Awakening 2" }
      ],
      expected: true
    },
    {
      name: "Day 8 - should not send (past schedule)",
      messages: [
        { Date: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000).toISOString(), Sender: "user", Type: "message", Content: "Hello" },
        { Date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), Sender: "assistant", Type: "daily_awakening", Content: "Awakening 1" },
        { Date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), Sender: "assistant", Type: "daily_awakening", Content: "Awakening 2" },
        { Date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), Sender: "assistant", Type: "daily_awakening", Content: "Awakening 3" }
      ],
      expected: false
    }
  ];
  
  testCases.forEach((testCase, index) => {
    const result = shouldSendAwakeningOnSchedule(testCase.messages, `test-${index}`);
    const passed = result.shouldSendAwakening === testCase.expected;
    console.log(`Test ${index + 1} (${testCase.name}): ${passed ? 'PASS' : 'FAIL'}`);
    if (!passed) {
      console.log(`  Expected: ${testCase.expected}, Got: ${result.shouldSendAwakening}`);
      console.log(`  Reason: ${result.reason}`);
    }
  });
  
  console.log("Awakening schedule testing complete.");
}; 