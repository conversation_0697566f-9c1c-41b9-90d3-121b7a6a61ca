export type PlanType = 'basic' | 'premium' | 'free';
export type BillingInterval = 'monthly' | 'yearly';

export interface PlanDetails {
  name: string;
  displayName: string;
  essencePerMonth: number;
  limits: {
    messagesPerMonth: number;
    callMinsPerMonth: number;
    messagesPerDay: number;
    callMinsPerDay: number;
    callCountPerDay: number;
    maxCallDuration: number;
  };
}

export const PLANS: Record<PlanType, PlanDetails> = {
  basic: {
    name: 'basic_plan',
    displayName: 'Awaken Beyond',
    essencePerMonth: 110,
    limits: {
      messagesPerMonth: 1000,
      callMinsPerMonth: 200,
      messagesPerDay: 200,
      callMinsPerDay: 1000,
      callCountPerDay: 30,
      maxCallDuration: 35,
    }
  },
  premium: {
    name: 'premium_plan',
    displayName: 'Awaken Boundless',
    essencePerMonth: 250,
    limits: {
      messagesPerMonth: 1250,
      callMinsPerMonth: 500,
      messagesPerDay: 600,
      callMinsPerDay: 1000,
      callCountPerDay: 30,
      maxCallDuration: 45
    }
  },
  free: {
    name: 'free_plan',
    displayName: 'Free',
    essencePerMonth: 10,
    limits: {
      messagesPerMonth: 200,
      callMinsPerMonth: 50,
      messagesPerDay: 50,
      callMinsPerDay: 1000,
      callCountPerDay: 10,
      maxCallDuration: 20
    }
  }
} as const;

export function isValidPlanType(type: string): type is PlanType {
  return ['basic', 'premium', 'free'].includes(type);
} 