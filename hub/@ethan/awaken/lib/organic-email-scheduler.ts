import { v4 as uuid } from "npm:uuid";
import { db } from "./db.ts";
import { EmailJobDAO } from "./organic-email-db.ts";

// Type definitions
export type Stage = 1 | 2 | 3;
export type EmailType = "acknowledgement" | "quick_reflection" | "resource";

// Helper function for random number in range
function rand(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

// Pure function to pick email type based on stage
export function pickType(stage: Stage): EmailType {
  const r = Math.random();
  if (stage === 1)
    return r < 0.6
      ? "acknowledgement"
      : r < 0.8
      ? "resource"
      : "quick_reflection";
  if (stage === 2)
    return r < 0.6
      ? "quick_reflection"
      : r < 0.9
      ? "resource"
      : "acknowledgement";
  return r < 0.7
    ? "resource"
    : r < 0.9
    ? "quick_reflection"
    : "acknowledgement";
}

// Calculate send time with timezone awareness
export async function scheduleAt(
  stage: Stage, 
  userId: string,
  now = new Date()
): Promise<Date> {
  // Get user timezone from database
  const user = await db
    .selectFrom("user")
    .select(["timezone"])
    .where("channelId", "=", userId)
    .executeTakeFirst();
    
  const timezone = user?.timezone || "America/New_York"; // fallback
  
  // Calculate base delay - TESTING MODE: much shorter delays
  let targetTime: Date;
  
  if (stage === 1) targetTime = new Date(+now + 30000); // 30 seconds
  else if (stage === 2) targetTime = new Date(+now + 60000); // 1 minute
  else targetTime = new Date(+now + 120000); // 2 minutes
  
  // Adjust to 6am-10pm window in user's timezone
  const userTime = new Date(targetTime.toLocaleString("en-US", { timeZone: timezone }));
  const hour = userTime.getHours();
  
  if (hour < 6) {
    // Move to 6am same day
    userTime.setHours(6, 0, 0, 0);
  } else if (hour >= 22) {
    // Move to 6am next day
    userTime.setDate(userTime.getDate() + 1);
    userTime.setHours(6, 0, 0, 0);
  }
  
  // Convert back to UTC
  return new Date(userTime.toLocaleString("en-US", { timeZone: "UTC" }));
}

// Handle user turn - schedule organic email
export async function handleUserTurn(
  userId: string,
  coachId: string,
  now = new Date()
) {
  try {
    console.log(`[ORGANIC_EMAIL] Starting handleUserTurn for user ${userId}, coach ${coachId}`);
    
    // Check if user has opted out of emails
    console.log(`[ORGANIC_EMAIL] Checking email preference for user ${userId}`);
    const user = await db
      .selectFrom("user")
      .select(["emailDaily"])
      .where("channelId", "=", userId)
      .executeTakeFirst();
    
    console.log(`[ORGANIC_EMAIL] User email preference:`, user?.emailDaily);
    
    if (user?.emailDaily === 0) {
      console.log(`[ORGANIC_EMAIL] User ${userId} has opted out of daily emails`);
      return;
    }
    
    console.log(`[ORGANIC_EMAIL] Canceling any pending emails for user ${userId}`);
    await EmailJobDAO.cancelPending(userId);
    
    const first: Stage = 1;
    const emailType = pickType(first);
    const sendAtTime = await scheduleAt(first, userId, now);
    
    console.log(`[ORGANIC_EMAIL] Scheduling stage ${first} email type "${emailType}" for ${sendAtTime}`);
    
    await EmailJobDAO.insert({
      id: uuid(),
      userId: userId,
      coachId: coachId,
      stage: first,
      type: emailType,
      sendAt: sendAtTime.toISOString(),
      createdAt: now.toISOString(),
    });
    
    console.log(`[ORGANIC_EMAIL] Successfully scheduled organic email for user ${userId}`);
  } catch (error) {
    console.error(`[ORGANIC_EMAIL] Error in handleUserTurn for user ${userId}:`, error);
    throw error;
  }
}