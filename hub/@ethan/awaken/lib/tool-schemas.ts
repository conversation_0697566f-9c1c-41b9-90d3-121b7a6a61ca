// lib/tool-schemas.ts (Revised to match VAPI spec for inline definition)

// 1. Define the core function details (name, description, parameters)
//    Align descriptions and defaults with your dashboard version for consistency.
const FETCH_KNOWLEDGE_FUNCTION_DEF = {
  name: "fetch_knowledge", // Matches dashboard
  // strict: true, // Optional: Set explicitly to match dashboard if needed, otherwise defaults to false
  description: "Fetch up to N thematically-relevant knowledge items from the coach's knowledge-base", // Use dashboard description
  parameters: {
    type: "object",
    properties: {
      query: {
        type: "string",
        description: "The query that your subconscious will use to intuit what to pull from your knowledgebase, that will serve in this moment" // Use dashboard description
      },
      "client_state": {
        type: "string",
        description: "Short description of the client's current emotional / situational context." // Use dashboard description
      },
      needs: {
        type: "array",
        items: { type: "string" },
        description: "Desired content types e.g. story, principle, quote" // Use dashboard description
      },
      "max_items": {
        type: "number",
        // Default is handled by your action, but good to note VAPI dashboard default might differ
        description: "Maximum number of items to return (defaults to 1 in VAPI dashboard, 3 in your action - be consistent)"
      }
    },
    required: ["query"] // Matches dashboard
  }
} as const;

// 2. Define the full VAPI Tool Configuration Object
export const FETCH_KNOWLEDGE_TOOL_VAPI_CONFIG = {
  type: "function", // Required: Must be "function"
  async: false, // << REQUIRED & CRITICAL: Set to false for synchronous execution
  server: {
    url: "https://steadily-light-lobster.ngrok-free.app/api/tool-handler",
    headers: {
      "Authorization": "Bearer a9f8b2d7e0c3a1b4d9e8f7a6b5c4d3e2f1a0b9c8d7e6f5a4b3c2d1e0f9a8b7c6"
    }
  },
  function: FETCH_KNOWLEDGE_FUNCTION_DEF, // Embed the function definition
} as const;

// 3. Update the lookup object
export const TOOL_VAPI_CONFIGS: Record<string, any> = {
  "fetch_knowledge": FETCH_KNOWLEDGE_TOOL_VAPI_CONFIG,
  // Add other tools here following the same structure
};

// 4. Anthropic tool schemas
export const TOOL_SCHEMAS = {
  vapi: { fetch_knowledge: FETCH_KNOWLEDGE_TOOL_VAPI_CONFIG },

  anthropic: {
    fetch_knowledge: {
      name: "fetch_knowledge",
      description: "Return up to N relevant items from a coach KB",
      input_schema: {
        type: "object",
        properties: {
          query: { type: "string" },
          client_state: { type: "string" },
          needs: { type: "array", items: { type: "string" } },
          max_items: { type: "number" },
          kb_identifier: { type: "string" },
        },
        required: ["query", "kb_identifier"],
      },
    },
  },
} as const;
