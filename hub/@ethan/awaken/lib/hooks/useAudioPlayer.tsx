"use client";

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "npm:react@canary";

// Unified hook that powers all audio interactions across the chat experience.
// It merges the visual-feedback focused implementation used in `chat.tsx` with
// the simple global–audio coordination previously used inside message bubbles.
//
// IMPORTANT: – Keep API backward-compatible with existing usages.
// – Expose `activeAudioId`, `setActiveAudioId`, `globalAudioRef`, `stopAudio`
//   for message-level audio players.
// – Expose visualisation helpers (`audioScale`, etc.) for the central circle.

interface UseAudioPlayerOptions {
  /** Optional URL to preload.  */
  initialAudioUrl?: string | null;
  /** If true the audio will start automatically once loaded. */
  autoPlay?: boolean;
  /** Callback fired whenever play / pause state toggles. */
  onPlayStateChange?: (playing: boolean) => void;
  /** Callback fired once the currently playing track finishes. */
  onComplete?: () => void;
}

export const useAudioPlayer = (
  {
    initialAudioUrl = null,
    autoPlay = false,
    onPlayStateChange,
    onComplete,
  }: UseAudioPlayerOptions = {},
) => {
  /* ------------------------------------------------------------------ */
  /* Public reactive state                                               */
  /* ------------------------------------------------------------------ */
  const [isPlaying, setIsPlaying] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [audioScale, setAudioScale] = useState(1);
  const [audioUrl, setAudioUrl] = useState<string | null>(initialAudioUrl);

  /* These 3 are used by the message-level AudioPlayer so that only one
     message can be playing at any given time.                          */
  const [activeAudioId, setActiveAudioId] = useState<number | null>(null);
  // The single <audio> element that plays message-level audio.
  const globalAudioRef = useRef<HTMLAudioElement | null>(null);

  /* ------------------------------------------------------------------ */
  /* Refs used for the Web-Audio-based visualisation (central circle)    */
  /* ------------------------------------------------------------------ */
  const rootAudioRef = useRef<HTMLAudioElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const sourceRef = useRef<AudioBufferSourceNode | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameIdRef = useRef<number | null>(null);
  const isActiveRef = useRef(false);

  /* ------------------------------------------------------------------ */
  /* Helpers                                                             */
  /* ------------------------------------------------------------------ */
  const initializeAudioContext = async () => {
    if (!audioContextRef.current) {
      const AC = globalThis.AudioContext;
      audioContextRef.current = new AC();
    }
    if (audioContextRef.current.state === "suspended") {
      try {
        await audioContextRef.current.resume();
      } catch (e) {
        console.error("[AudioPlayer] Failed to resume AudioContext", e);
      }
    }
  };

  const startVisualisation = (analyser: AnalyserNode) => {
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const render = () => {
      if (!isActiveRef.current) {
        console.log("[AudioPlayer] Visualization stopped - isActiveRef is false");
        return;
      }
      animationFrameIdRef.current = requestAnimationFrame(render);
      analyser.getByteFrequencyData(dataArray);
      const avg = dataArray.reduce((a, v) => a + v, 0) / bufferLength;
      const amplification = 2.2;
      const newScale = 0.8 + (avg / 255) * amplification;
      setAudioScale(newScale);
    };

    render();
  };

  /* ------------------------------------------------------------------ */
  /* Core play / stop implementation (Web-Audio buffer based)            */
  /* ------------------------------------------------------------------ */
  const playBuffer = async (src: string) => {
    try {
      await initializeAudioContext();

      // Clean previous
      if (sourceRef.current) {
        sourceRef.current.stop();
        sourceRef.current.disconnect();
        sourceRef.current = null;
      }

      const resp = await fetch(src);
      if (!resp.ok) throw new Error("Failed to fetch audio");
      const buf = await resp.arrayBuffer();

      if (!audioContextRef.current) return;
      const audioBuffer = await audioContextRef.current.decodeAudioData(buf);

      const bufferSource = audioContextRef.current.createBufferSource();
      bufferSource.buffer = audioBuffer;
      sourceRef.current = bufferSource;

      const analyser = audioContextRef.current.createAnalyser();
      analyser.fftSize = 256;
      analyser.smoothingTimeConstant = 0.5;
      analyserRef.current = analyser;  // Store analyser in ref to prevent garbage collection
      bufferSource.connect(analyser);
      analyser.connect(audioContextRef.current.destination);

      // Set state BEFORE starting audio to avoid race conditions
      isActiveRef.current = true;
      setIsPlaying(true);
      onPlayStateChange?.(true);
      
      bufferSource.start(0);
      startVisualisation(analyser);

      bufferSource.onended = () => {
        isActiveRef.current = false;
        setIsPlaying(false);
        setIsComplete(true);
        onPlayStateChange?.(false);
        onComplete?.();
        stopAudio();
      };
    } catch (err) {
      console.error("[AudioPlayer] playBuffer error", err);
      setIsPlaying(false);
      onPlayStateChange?.(false);
    }
  };

  /* ------------------------------------------------------------------ */
  /* Public API                                                          */
  /* ------------------------------------------------------------------ */

  const stopAudio = useCallback(() => {
    /* --- HTMLAudioElement variant (message-level) --- */
    if (globalAudioRef.current) {
      try {
        globalAudioRef.current.pause();
        globalAudioRef.current.currentTime = 0;
        globalAudioRef.current = null;
      } catch (err) {
        console.error("[AudioPlayer] Error stopping global audio", err);
      }
    }

    /* --- Web-Audio buffer variant (central circle) --- */
    rootAudioRef.current?.pause();
    if (sourceRef.current) {
      try {
        sourceRef.current.stop(0);
        sourceRef.current.disconnect();
      } catch {}
      sourceRef.current = null;
    }
    if (analyserRef.current) {
      analyserRef.current.disconnect();
      analyserRef.current = null;
    }
    if (animationFrameIdRef.current) {
      cancelAnimationFrame(animationFrameIdRef.current);
      animationFrameIdRef.current = null;
    }

    setIsPlaying(false);
    onPlayStateChange?.(false);
  }, [onPlayStateChange]);

  /** Toggle play / pause for the currently loaded buffer-based audio (used by circle). */
  const togglePlay = () => {
    if (isPlaying) {
      stopAudio();
    } else if (audioUrl) {
      playBuffer(audioUrl).catch(console.error);
    }
  };

  /** Load URL and immediately play (buffer-based). Suitable for circle playback. */
  const play = (src: string) => {
    setAudioUrl(src);
    playBuffer(src).catch(console.error);
  };

  /* ------------------------------------------------------------------ */
  /* Effects                                                             */
  /* ------------------------------------------------------------------ */
  // Reset completion whenever URL changes
  useEffect(() => {
    setIsComplete(false);
  }, [audioUrl]);

  // Clean up on unmount ONLY - not when stopAudio changes
  useEffect(() => {
    console.log("[AudioPlayer] Component mounted");
    return () => {
      console.log("[AudioPlayer] Component unmounting - calling stopAudio");
      // Create a local reference to avoid dependency issues
      if (sourceRef.current) {
        try {
          sourceRef.current.stop(0);
          sourceRef.current.disconnect();
        } catch {}
      }
      if (analyserRef.current) {
        analyserRef.current.disconnect();
      }
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      if (globalAudioRef.current) {
        try {
          globalAudioRef.current.pause();
          globalAudioRef.current.currentTime = 0;
        } catch {}
      }
    };
  }, []); // Empty dependency array ensures this only runs on mount/unmount

  // Auto-play if requested
  useEffect(() => {
    if (autoPlay && initialAudioUrl) {
      play(initialAudioUrl);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /* ------------------------------------------------------------------ */
  /* Return value                                                        */
  /* ------------------------------------------------------------------ */
  return {
    /* ---- message-level helpers ---- */
    activeAudioId,
    setActiveAudioId,
    globalAudioRef,

    /* ---- circle helpers ---- */
    isPlaying,
    isComplete,
    audioScale,
    togglePlay,
    play,
    stopAudio,
    rootAudioRef,

    /* misc */
    audio: audioUrl,
    setAudio: setAudioUrl,
    initializeAudioContext,
  } as const;
}; 