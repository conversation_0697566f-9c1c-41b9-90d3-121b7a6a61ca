"use client";

import { useState, useRef, useEffect } from "npm:react@canary";
import { encode } from "npm:@msgpack/msgpack";

interface UseVoiceRecorderProps {
  channelId: string;
  onRecordingComplete?: (audioBlob: Blob) => void;
}

export const useVoiceRecorder = ({ channelId, onRecordingComplete }: UseVoiceRecorderProps) => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null);
  const [analyser, setAnalyser] = useState<AnalyserNode | null>(null);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackProgress, setPlaybackProgress] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const timerRef = useRef<number | null>(null);
  const audioChunks = useRef<Blob[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const audioUrlRef = useRef<string | null>(null);
  const isCancelingRef = useRef(false);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  // Start recording function
  const startRecording = async () => {
    if (isRecording) return;
    
    // Clear success state when starting a new recording
    setShowSuccess(false);
    
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      proceedWithRecording(stream); 
    } catch (error) {
      console.error("Error getting mic permission or stream:", error);
    }
  };

  // Proceed with recording after permission is granted
  const proceedWithRecording = (stream: MediaStream) => {
    startRecordingWithStream(stream);
  };

  // Start recording with media stream
  const startRecordingWithStream = (stream: MediaStream) => {
    // Reset state for new recording
    setAudioBlob(null);
    if (audioUrlRef.current) URL.revokeObjectURL(audioUrlRef.current);
    audioUrlRef.current = null;
    audioChunks.current = [];
    setPlaybackProgress(0);
    setIsPlaying(false);
    isCancelingRef.current = false;

    // Setup audio context and analyser
    const newAudioContext = new (globalThis.AudioContext || (window as any).webkitAudioContext)();
    const source = newAudioContext.createMediaStreamSource(stream);
    const newAnalyser = newAudioContext.createAnalyser();
    newAnalyser.fftSize = 512; // Adjusted FFT size
    newAnalyser.smoothingTimeConstant = 0.5;
    source.connect(newAnalyser);
    setAudioContext(newAudioContext);
    setAnalyser(newAnalyser);

    // Setup MediaRecorder
    const options = { mimeType: "audio/webm;codecs=opus" }; 
    let recorder: MediaRecorder;
    try {
        recorder = new MediaRecorder(stream, options);
    } catch (e) {
        console.warn('audio/webm;codecs=opus not supported, falling back to default');
        try {
            recorder = new MediaRecorder(stream); // Fallback to default
        } catch (fallbackError) {
            console.error("MediaRecorder creation failed entirely:", fallbackError);
            cleanupStream(stream); // Clean up the stream if recorder fails
            return; // Stop execution if recorder can't be created
        }
    }
    
    mediaRecorderRef.current = recorder;
    
    recorder.ondataavailable = (e) => {
      if (e.data.size > 0) audioChunks.current.push(e.data);
    };

    recorder.onstop = () => {
      if (isCancelingRef.current) {
        isCancelingRef.current = false;
        cleanupStream(stream); // Cleanup stream only on explicit cancel
        return;
      }
      if (audioChunks.current.length === 0) {
          console.warn("Recording stopped with no audio data.");
          cleanupStream(stream); // Still clean up
          setIsRecording(false); // Ensure recording state is reset
          return;
      }
      const mimeType = mediaRecorderRef.current?.mimeType || 'audio/webm;codecs=opus'; // Get actual mimeType
      const newAudioBlob = new Blob(audioChunks.current, { type: mimeType });
      
      if (newAudioBlob.size === 0) {
         console.warn("Created an empty audio blob.");
         cleanupStream(stream); // Clean up
         setIsRecording(false); // Reset state
         return;
      }

      setAudioBlob(newAudioBlob);
      const audioUrl = URL.createObjectURL(newAudioBlob);
      audioUrlRef.current = audioUrl;
      if (audioRef.current) {
        // Set the audio source
        audioRef.current.src = audioUrl;
        
        // We know the duration from recording time, no need to wait for metadata
        // Set playback UI immediately
        setPlaybackProgress(0);
      }
      cleanupStream(stream); // Clean up stream after processing blob
    };

    recorder.onerror = (event) => {
        console.error("MediaRecorder error:", event);
        cleanupStream(stream);
        setIsRecording(false);
    };

    recorder.start(100); // Record in 100ms chunks
    setIsRecording(true);
    setRecordingTime(0);
    if (timerRef.current) clearInterval(timerRef.current);
    timerRef.current = globalThis.setInterval(() => setRecordingTime(prev => prev + 1), 1000);
  };

  // Stop recording
  const stopRecording = () => {
    clearInterval(timerRef.current as unknown as number);
    timerRef.current = null;
    
    // Store the final recording duration for playback
    setAudioDuration(recordingTime);
    
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== "inactive") {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      // Extra safety to ensure we also clear any ongoing audio context that might still be active
      if (audioContext) {
        try {
          if (audioContext.state !== "closed") {
            audioContext.suspend();
          }
        } catch (e) {
          console.error("Error suspending audio context:", e);
        }
      }
    }
  };

  // Toggle playback
  const togglePlayback = () => {
    if (!audioRef.current || !audioBlob) return;
    
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      // If audio has ended, reset to beginning
      if (playbackProgress >= 100) {
        audioRef.current.currentTime = 0;
        setPlaybackProgress(0);
      }
      
      audioRef.current.play().catch(error => {
        console.error("Audio play error:", error);
        // Reset UI if playback fails
        setIsPlaying(false);
      });
    }
  };

  // Cancel recording
  const cancelRecording = () => {
    isCancelingRef.current = true;
    stopRecording();
    setAudioBlob(null);
    
    if (audioUrlRef.current) {
      URL.revokeObjectURL(audioUrlRef.current);
      audioUrlRef.current = null;
    }
  };

  // Submit recording
  const submitRecording = async () => {
    if (audioBlob) {
      setIsPlaying(false);
      if (audioRef.current) audioRef.current.pause();
      
      // Set loading state
      setIsLoading(true);
      
      try {
        // Convert audio blob to array buffer and encode with msgpack
        // const encodedAudio = encode(new Uint8Array(arrayBuffer));
        
        // Call server action with JSON-safe array
        // const transcript = await processAudioTranscriptionAction(
        //   Array.from(encodedAudio), 
        //   channelId
        // );
        
        // console.log("Transcription:", transcript);
  
        // Check if data is an empty object
        // if (Object.keys(transcript).length === 0) {
        //   setIsLoading(false);
        //   return;
        // }
  
        // Show success message and keep it visible
        setIsLoading(false);
        resetRecordingState();
        setShowSuccess(true);
        
        // Process the recording immediately but keep success message visible
        if (onRecordingComplete) {
          onRecordingComplete(audioBlob);
        }
      } catch (error) {
        console.error("Error processing audio:", error);
        setIsLoading(false);
        resetRecordingState();
      }
    }
  };

  // Clean up stream
  const cleanupStream = (stream: MediaStream | undefined) => {
    stream?.getTracks().forEach(track => {
      if (track.readyState === 'live') { 
        track.stop();
      }
    });
    if (audioContext && audioContext.state !== 'closed') {
      audioContext.close().catch(e => console.warn("Error closing AudioContext:", e));
      setAudioContext(null);
    }
  };

  // Reset recording state
  const resetRecordingState = () => {
    // Clean up previous recording
    if (audioUrlRef.current) {
      URL.revokeObjectURL(audioUrlRef.current);
      audioUrlRef.current = null;
    }
    
    if (audioRef.current) {
      audioRef.current.src = '';
      audioRef.current.load(); // Reload to clear any previous audio
    }

    // Reset state for a new recording
    setAudioBlob(null);
    setIsRecording(false);
    setRecordingTime(0);
    setAudioDuration(0);
    setPlaybackProgress(0);
    setIsPlaying(false);
    audioChunks.current = [];
    
    // Also clean up AudioContext if it exists
    if (audioContext && audioContext.state !== "closed") {
      try {
        audioContext.close();
        setAudioContext(null);
        setAnalyser(null);
      } catch (e) {
        console.error("Error closing audio context:", e);
      }
    }
  };

  // Update playback progress
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      if (audioDuration > 0) {
        // Ensure audioDuration is not zero to avoid division by zero
        const progress = Math.min(100, (audio.currentTime / audioDuration) * 100);
        setPlaybackProgress(progress);
      } else {
        // Handle case where duration might be 0 initially
        setPlaybackProgress(0);
      }
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setPlaybackProgress(0); // Reset progress when ended
      // Optionally reset currentTime if desired, e.g., audio.currentTime = 0;
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    audio.addEventListener("timeupdate", updateProgress);
    audio.addEventListener("ended", handleEnded);
    audio.addEventListener("play", handlePlay); // Add play listener
    audio.addEventListener("pause", handlePause); // Add pause listener
    
    // Initial check in case audio is already playing/paused when effect runs
    setIsPlaying(!audio.paused);

    return () => {
      audio.removeEventListener("timeupdate", updateProgress);
      audio.removeEventListener("ended", handleEnded);
      audio.removeEventListener("play", handlePlay); // Clean up play listener
      audio.removeEventListener("pause", handlePause); // Clean up pause listener
    };
  }, [audioBlob, audioDuration]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (isRecording) {
        stopRecording();
      }
      if (mediaRecorderRef.current?.stream) {
        cleanupStream(mediaRecorderRef.current.stream);
      }
      if (timerRef.current) clearInterval(timerRef.current);
      if (audioUrlRef.current) URL.revokeObjectURL(audioUrlRef.current);
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close().catch(e => console.warn("Error closing context on unmount:", e));
      }
    };
  }, []);

  return {
    audioRef,
    isRecording,
    recordingTime,
    audioBlob,
    analyser,
    isPlaying,
    playbackProgress,
    audioDuration,
    isLoading,
    showSuccess,
    formatTime,
    startRecording,
    stopRecording,
    togglePlayback,
    cancelRecording,
    submitRecording,
    resetRecordingState
  };
};
