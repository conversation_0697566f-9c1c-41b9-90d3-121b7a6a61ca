"use client";

import React from "npm:react@canary";
import { Button, Text } from "@reframe/ui/main.tsx";
import { motion } from "npm:framer-motion";
import { CircleXIcon } from "@reframe/icons/circle-x.ts";
import { EssenceIcon } from "./icons.tsx";
import { createTopUpSession } from "../actions/stripe-actions.ts";

export const TOP_UP_OPTIONS = [
  { price: 200, essence: 1100, popular: true, id: "tu_xyh9uiondi4" },
  { price: 50, essence: 230, popular: false, id: "tu_xnui32ho58u" },
  { price: 25, essence: 100, popular: false, id: "tu_xyh9uiondi4" },
  { price: 10, essence: 38, popular: false, id: "tu_xnui32ho58u" },
  { price: 5, essence: 18, popular: false, id: "tu_xsainm1928u" },
];
interface TopUpOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  channelId: string;
}

export const TopUpOverlay: React.FC<TopUpOverlayProps> = ({ isOpen, onClose, channelId }) => {
  const [loading, setLoading] = React.useState<number | null>(null);
  const [error, setError] = React.useState<string | null>(null);

  const handleTopUp = async (price: number, essence: number, topUpId: string) => {
    try {
      setLoading(price);
      setError(null);

      const checkoutUrl = await createTopUpSession(channelId, price, essence, topUpId);
      if (checkoutUrl) {
        globalThis.location.href = checkoutUrl;
      } else {
        throw new Error("Failed to create checkout session");
      }
    } catch (err) {
      console.error("Top-up error:", err);
      setError(err instanceof Error ? err.message : "Failed to process top-up");
    } finally {
      setLoading(null);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/80 backdrop-blur-sm z-40"
        onClick={onClose}
      />
      <motion.div
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.95, opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
      >
        <div className="bg-[#1a1a1a] rounded-xl w-full max-w-md p-6 relative">
          <Button
            variant="ghost"
            onClick={onClose}
            css="absolute right-4 top-4 text-gray-400 hover:text-white"
          >
            <CircleXIcon className="w-6 h-6" />
          </Button>

          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="text-[#ff6b35]">
                <EssenceIcon size={48} color="#ff6b35" />
              </div>
            </div>
            <div className="space-y-2">
              <Text css="text-2xl font-bold text-white block">Top Up Essence</Text>
              <Text css="text-gray-400 block">
                Power your transformative journey
              </Text>
            </div>
          </div>

          <div className="space-y-4">
            {TOP_UP_OPTIONS.map(({ essence, price, popular, id }) => (
              <motion.div
                key={essence}
                whileHover={{ scale: 1.02 }}
                className={`
                  relative p-4 rounded-lg border cursor-pointer
                  ${popular 
                    ? 'border-[#FCA311] bg-gradient-to-b from-[#FCA31115] to-transparent' 
                    : 'border-gray-700 hover:border-[#FCA311]/50'}
                `}
                onClick={() => handleTopUp(price, essence, id)}
              >
                {popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-[#FCA311] text-black text-xs font-bold px-3 py-1 rounded-full">
                      MOST POPULAR
                    </span>
                  </div>
                )}

                <div className="flex justify-between items-start">
                  <div className="space-y-1 flex flex-col">
                    <Text css="text-xl font-bold text-white block">{essence} Essence</Text>
                    <Text css="text-sm text-gray-400 block"></Text>
                  </div>
                  <div className="text-right flex flex-col">
                    <Text css="text-2xl font-bold text-[#FCA311] block">${price}</Text>
                    <Text css="text-sm text-gray-400 block">${(price/essence * 100).toFixed(2)}/100 essence</Text>
                  </div>
                </div>

                {loading === price && (
                  <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-2 border-[#FCA311] border-t-transparent" />
                  </div>
                )}
              </motion.div>
            ))}
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-500 text-sm text-center">
              {error}
            </div>
          )}
        </div>
      </motion.div>
    </>
  );
}; 