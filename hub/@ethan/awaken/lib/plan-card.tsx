import type React from "react"
import { Button } from "@reframe/ui/main"

interface PlanCardProps {
  title: string
  features: string[]
  price: number
  originalPrice: number
  onSubscribe: () => void
  loading: boolean
  isPremium?: boolean
}

export const PlanCard: React.FC<PlanCardProps> = ({
  title,
  features,
  price,
  originalPrice,
  onSubscribe,
  loading,
  isPremium = false,
}) => {
  return (
    <div
      className={`p-6 rounded-xl border ${
        isPremium ? "border-[#FFC107] bg-[#1C1C1C]" : "border-[#FFC1074D] bg-[#0B0B0B]"
      }`}
    >
      <h3 className={`text-xl font-semibold mb-4 ${isPremium ? "text-[#FFC107]" : "text-white"}`}>{title}</h3>
      <ul className="space-y-2 mb-6">
        {features.map((feature, index) => (
          <li key={index} className="flex items-center text-gray-300 text-sm">
            <span className="mr-2 text-[#FFC107]">•</span>
            {feature}
          </li>
        ))}
      </ul>
      <div className="text-gray-400 mb-4">
        <div className="text-xs mb-1">Per month</div>
        <div className="flex items-center gap-2">
          <span className="text-2xl text-white font-semibold">${price}</span>
          <span className="text-base line-through">${originalPrice}</span>
        </div>
      </div>
      <Button
        variant={isPremium ? "default" : "outline"}
        className={`w-full ${
          isPremium
            ? "bg-[#FFC107] text-black hover:bg-[#FFC107]/90"
            : "border-white text-white hover:bg-[#FFC107] hover:border-[#FFC107] hover:text-black"
        } text-sm py-2`}
        onClick={onSubscribe}
        disabled={loading}
      >
        Upgrade
      </Button>
    </div>
  )
}

