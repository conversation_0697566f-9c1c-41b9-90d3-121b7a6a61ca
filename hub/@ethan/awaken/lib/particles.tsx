"use client";

import { useEffect, useRef, useState } from "npm:react@canary";
import React from "npm:react@canary";

// Particle interface
interface Particle {
  y: number; // Only the y-position is dynamic
  speed: number; // Speed is dynamic too, but it's set once
}

export const ParticleField = React.memo(() => {
  const numParticles = globalThis.innerWidth < 768 ? 25 : 100;
  const particlesRef = useRef<HTMLDivElement[]>([]);
  const particleProps = useRef(
    Array.from({ length: numParticles }, () => ({
      y: Math.random() * 120 + 20,
      speed: (Math.random() * 0.1 + 0.05) * 0.4,
      x: Math.random() * 100,
      size: Math.random() * 2 + 1,
      color: randomColor(),
    }))
  );

  // Function to initialize the animation loop
  useEffect(() => {
    const animate = () => {
      particleProps.current.forEach((particle, index) => {
        particle.y -= particle.speed;
        if (particle.y < -10) {
          particle.y = 100; // Reset to bottom when particle moves out of view
        }

        // Directly update DOM elements
        const particleElement = particlesRef.current[index];
        if (particleElement) {
          particleElement.style.top = `${particle.y}%`;
        }
      });

      requestAnimationFrame(animate); // Continue the animation loop
    };

    requestAnimationFrame(animate);

    return () => {
      // Cancel animation if needed
    };
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particleProps.current.map((particle, index) => (
        <div
          key={index}
          ref={(el) => (particlesRef.current[index] = el!)} // Store references to DOM elements
          className="absolute rounded-full"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            opacity: 0.8,
            filter: "blur(1px)",
            willChange: "transform", // Hint for better performance
          }}
        />
      ))}
    </div>
  );
});

// Function to generate a random color
function randomColor() {
  const colors = [
    "rgba(255, 87, 39, 0.8)",
    "rgba(255, 140, 140, 0.8)",
    "rgba(255, 165, 0, 0.8)",
  ];
  return colors[Math.floor(Math.random() * colors.length)];
}

export const UpdatedInfinityIcon = ({
  size,
  className,
  style,
  fill,
}: {
  size: number;
  className: string;
  style?: React.CSSProperties;
  fill?: string;
}) => {
  return (
    <div className={className} style={style}>
      <svg
        width={size}
        height={size}
        viewBox="0 0 61 29"
        xmlns="http://www.w3.org/2000/svg"
        strokeWidth="1"
      >
        <path
          d="M60.0478 14.4988C60.0481 17.3267 59.2098 20.0912 57.6389 22.4426C56.0679 24.794 53.8349 26.6268 51.2223 27.709C48.6097 28.7912 45.7348 29.0744 42.9613 28.5226C40.1877 27.9708 37.6401 26.6089 35.6406 24.6091L35.5239 24.4828L21.2604 8.37734C20.0545 7.19354 18.5258 6.39227 16.8662 6.07409C15.2066 5.75592 13.4899 5.93501 11.9317 6.58889C10.3735 7.24277 9.04316 8.34231 8.10758 9.74953C7.172 11.1567 6.6729 12.8089 6.6729 14.4988C6.6729 16.1886 7.172 17.8408 8.10758 19.2481C9.04316 20.6553 10.3735 21.7548 11.9317 22.4087C13.4899 23.0626 15.2066 23.2417 16.8662 22.9235C18.5258 22.6053 20.0545 21.804 21.2604 20.6202L21.9943 19.791C22.497 19.2223 23.2051 18.8765 23.9627 18.8298C24.7204 18.7831 25.4656 19.0393 26.0343 19.542C26.6031 20.0447 26.9489 20.7528 26.9955 21.5105C27.0422 22.2681 26.7861 23.0133 26.2833 23.5821L25.4851 24.4828L25.3683 24.6091C23.3688 26.6083 20.8213 27.9697 18.0481 28.5211C15.2748 29.0726 12.4003 28.7893 9.78803 27.7071C7.17576 26.625 4.94303 24.7925 3.37218 22.4415C1.80133 20.0904 0.962891 17.3263 0.962891 14.4988C0.962891 11.6712 1.80133 8.90718 3.37218 6.55612C4.94303 4.20507 7.17576 2.3726 9.78803 1.29043C12.4003 0.208263 15.2748 -0.0750042 18.0481 0.476448C20.8213 1.0279 23.3688 2.3893 25.3683 4.38852L25.4851 4.5148L39.7486 20.6202C40.9545 21.804 42.4832 22.6053 44.1428 22.9235C45.8024 23.2417 47.519 23.0626 49.0772 22.4087C50.6354 21.7548 51.9658 20.6553 52.9014 19.2481C53.837 17.8408 54.3361 16.1886 54.3361 14.4988C54.3361 12.8089 53.837 11.1567 52.9014 9.74953C51.9658 8.34231 50.6354 7.24277 49.0772 6.58889C47.519 5.93501 45.8024 5.75592 44.1428 6.07409C42.4832 6.39227 40.9545 7.19354 39.7486 8.37734L39.0147 9.20656C38.7658 9.48819 38.4638 9.71803 38.1261 9.88295C37.7883 10.0479 37.4214 10.1447 37.0462 10.1678C36.6711 10.1909 36.295 10.1399 35.9396 10.0177C35.5842 9.89551 35.2563 9.70449 34.9746 9.45557C34.693 9.20664 34.4632 8.90469 34.2982 8.56694C34.1333 8.22919 34.0365 7.86226 34.0134 7.48711C33.9903 7.11195 34.0413 6.73592 34.1635 6.38047C34.2857 6.02503 34.4767 5.69713 34.7256 5.41551L35.5239 4.5148L35.6406 4.38852C37.6401 2.38872 40.1877 1.0268 42.9613 0.475006C45.7348 -0.0767838 48.6097 0.206344 51.2223 1.28858C53.8349 2.37082 56.0679 4.20355 57.6389 6.55498C59.2098 8.9064 60.0481 11.6709 60.0478 14.4988Z"
          fill={fill}
        />
        <defs>
          <linearGradient
            id="paint0_linear_3808_2147"
            x1="30.5053"
            y1="28.7974"
            x2="30.5053"
            y2="0.200196"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#FF5727" />
            <stop offset="1" stop-color="#FFC360" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
};
