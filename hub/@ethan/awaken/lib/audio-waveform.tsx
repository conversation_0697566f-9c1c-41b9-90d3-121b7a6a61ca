"use client";

import { useEffect, useRef, useState, useMemo } from "npm:react@canary";
import React from "npm:react@canary";

// Updated to bars with improved desktop appearance
export const AudioWaveform = ({
  analyser,
  isRecording,
}: {
  analyser: AnalyserNode | null;
  isRecording: boolean;
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const previousDataRef = useRef<number[]>([]);
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    const checkDesktop = () => {
      setIsDesktop(globalThis.innerWidth >= 768);
    };
    
    checkDesktop();
    globalThis.addEventListener('resize', checkDesktop);
    
    return () => globalThis.removeEventListener('resize', checkDesktop);
  }, []);

  // Adjust canvas dimensions based on container width
  useEffect(() => {
    const resizeCanvas = () => {
      if (canvasRef.current && containerRef.current) {
        const containerWidth = containerRef.current.clientWidth;
        // Set canvas width based on container width for better scaling
        canvasRef.current.width = containerWidth;
        // Redraw canvas if needed
        if (analyser && isRecording) {
          drawWaveform();
        }
      }
    };

    resizeCanvas();
    globalThis.addEventListener('resize', resizeCanvas);
    
    return () => globalThis.removeEventListener('resize', resizeCanvas);
  }, [isDesktop, analyser, isRecording]);

  // Draw waveform function to avoid code duplication
  const drawWaveform = () => {
    const canvas = canvasRef.current;
    if (!canvas || !analyser) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;
    // More bars on desktop for smoother appearance
    const bufferLength = isDesktop ? 64 : 32; 
    const dataArray = new Uint8Array(bufferLength);

    // Adjust smoothing based on device
    const smoothFactor = isDesktop ? 0.5 : 0.7;
    // Smaller spacing on desktop for finer bars
    const barSpacing = isDesktop ? 2 : 1; 
    const barWidth = (width - (bufferLength * barSpacing)) / bufferLength;
    const effectiveBarWidth = Math.max(barWidth, isDesktop ? 1 : 2); // Make sure bars have minimum width
    const minHeight = height * 0.2;

    ctx.clearRect(0, 0, width, height);
    
    if (isRecording) {
      analyser.getByteFrequencyData(dataArray);

      ctx.fillStyle = "#FCA311";

      for (let i = 0; i < bufferLength; i++) {
        const rawHeight = (dataArray[i] / 255.0) * height;
        const barHeight = Math.max(rawHeight, minHeight);
        
        // Smooth the transition
        const smoothHeight = previousDataRef.current[i]
          ? previousDataRef.current[i] +
            (barHeight - previousDataRef.current[i]) * smoothFactor
          : barHeight;

        // Calculate vertical position to center the bar
        const y = (height - smoothHeight) / 2;
        
        // Draw rounded rectangle for each bar
        const x = i * (effectiveBarWidth + barSpacing);
        
        // Use smaller border radius on desktop for cleaner look
        const borderRadius = isDesktop ? 1 : 2;
        ctx.beginPath();
        ctx.roundRect(x, y, effectiveBarWidth, smoothHeight, borderRadius);
        ctx.fill();

        previousDataRef.current[i] = smoothHeight;
      }

      animationRef.current = requestAnimationFrame(drawWaveform);
    } else {
      // Draw minimum height bars when not recording
      ctx.fillStyle = "#FCA311";
      for (let i = 0; i < bufferLength; i++) {
        const x = i * (effectiveBarWidth + barSpacing);
        const y = (height - minHeight) / 2;
        const borderRadius = isDesktop ? 1 : 2;
        
        ctx.beginPath();
        ctx.roundRect(x, y, effectiveBarWidth, minHeight, borderRadius);
        ctx.fill();
      }
      previousDataRef.current = [];
    }
  };

  useEffect(() => {
    drawWaveform();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [analyser, isRecording, isDesktop]);

  return (
    <div 
      ref={containerRef} 
      className="w-full md:max-w-[80%] mx-auto"
    >
      <canvas 
        ref={canvasRef} 
        width={300} 
        height={20} 
        className="w-full" 
      />
    </div>
  );
};
