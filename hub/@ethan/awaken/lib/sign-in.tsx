"use client";

import { But<PERSON>, Image, Text, X, Y } from "@reframe/ui/main.tsx";
import { Auth } from "@reframe/auth/auth.ts";

import { motion, AnimatePresence } from "npm:framer-motion";
import { PlayIcon } from "@reframe/icons/play.ts";
import { ArrowRightIcon } from "@reframe/icons/arrow-right.ts";
import { PauseIcon } from "@reframe/icons/pause-icon.ts";
import { ParticleField } from "./particles.tsx";
import { useEffect, useRef, useState } from "npm:react@canary";
import React from "npm:react@canary";
import { Logo } from "./logo.tsx";

export const SignInComponent = () => {
  return (
    <div className="w-full relative min-h-svh max-h-svh bg-black overflow-hidden root-component touch-none">
      <ParticleField />
      <motion.div
        key={"powerfulMessage"}
        className="relative flex flex-col min-h-svh text-orange-300 p-6 component-b items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 1 }}
      >
        {/* Upper circle */}
        <motion.div
          className="absolute w-[506px] h-[506px] rounded-full blur-[100px] md:blur-[150px]"
          style={{
            top: "-30%", // Adjusted for mobile
            background:
              "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
          }}
          initial={{ opacity: 0.1 }}
          animate={{ opacity: 0.6 }}
          transition={{ duration: 0 }}
        />

        {/* Bottom "house shape" */}
        <motion.div
          className="absolute bottom-0 w-full h-[500px] md:h-[715px] pointer-events-none"
          initial={{ opacity: 0.1 }}
          animate={{ opacity: 0.6 }}
          transition={{ duration: 0 }}
          style={{
            top: "75%",
            width: "100%", // Make it full width on mobile
            maxWidth: "1030px",
            filter: "blur(100px)",
          }}
        >
          <div
            className="absolute"
            style={{
              width: "100%",
              maxWidth: "1030px",
              height: "100%",
              background:
                "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
              clipPath: "polygon(0% 20%, 50% 0%, 100% 20%, 100% 100%, 0% 100%)",
              left: "50%",
              transform: "translateX(-50%)",
            }}
          />
        </motion.div>

        <div className="flex flex-col items-center gap-4 w-full">
          <div className="flex items-center space-x-2">
            <Logo size={30} />
            <h1 className="text-[24px] md:text-[36px] font-normal text-white">
              awaken
            </h1>
          </div>
          {/* <div>
            <span className="text-white text-lg md:text-2xl">
              A mirror to your{" "}
            </span>
            <span className="bg-gradient-to-r from-[#FF5727] to-[#FFC360] bg-clip-text text-transparent text-lg md:text-2xl">
              limitless self
            </span>
          </div> */}
          <div className="flex w-full justify-center mt-2">
            <Button
              variant="outline"
              css="w-[80%] md:w-auto px-6 py-2 bg-gradient-to-r from-[#00000033] to-[#5C3B0633] border-[#FCA3114D] hover:border-[#FCA311] text-white rounded-full flex items-center space-x-2 transition-colors z-10 hover:bg-[#5C3B06] hover:border-[#FCA311]"
              onClick={() => Auth.signIn("google")}
            >
              <Image
                className="w-5 h-5 md:w-6 md:h-6"
                src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg"
                alt="Google Icon"
              />
              <span className="text-sm md:text-base">Continue with Google</span>
            </Button>
          </div>
        </div>

        {/* Centered Text Content */}
        {/* <div className="relative z-10 flex flex-col items-center text-center space-y-2 mt-[20%] md:mt-[15%] px-4 md:px-0">
          <div className="flex items-center space-x-2">
            <div className="h-4 w-4 md:h-6 md:w-6 rounded-full bg-gradient-to-r from-[#FF5727] to-[#FFC360]" />
            <h1 className="text-[24px] md:text-[36px] font-normal text-white">
              awaken
            </h1>
          </div>
          <div>
            <span className="text-white text-lg md:text-2xl">
              A mirror to your{" "}
            </span>
            <span className="bg-gradient-to-r from-[#FF5727] to-[#FFC360] bg-clip-text text-transparent text-lg md:text-2xl">
              limitless self
            </span>
          </div>
        </div> */}

        {/* Button Section */}
        {/* <div className="absolute bottom-1/4 w-full flex flex-col items-center space-y-2 px-4">
          <Button
            variant="outline"
            css="w-full md:w-auto px-6 py-2 bg-gradient-to-r from-[#00000033] to-[#5C3B0633] border-[#FCA3114D] hover:border-[#FCA311] text-white rounded-full flex items-center space-x-2 transition-colors z-10 hover:bg-[#5C3B06] hover:border-[#FCA311]"
            onClick={() => Auth.signIn("google")}
          >
            <Image
              className="w-5 h-5 md:w-6 md:h-6"
              src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg"
              alt="Google Icon"
            />
            <span className="text-sm md:text-base">Continue with Google</span>
          </Button>
        </div> */}
      </motion.div>
    </div>
  );
};
