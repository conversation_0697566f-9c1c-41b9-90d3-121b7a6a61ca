"use client";

import React, { useEffect, useRef, useState } from "npm:react@canary";
import { RefreshOverlay } from "./refresh-overlay.tsx";

const VERSION_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes

export const VersionCheck = () => {
  const [showRefresh, setShowRefresh] = useState(false);
  const checkVersionRef = useRef<NodeJS.Timeout>();
  const lastVersionRef = useRef<string>();

  const checkVersion = async () => {
    try {
      console.log('[VERSION] Checking for new version...');
      const res = await fetch('/api/version');
      
      if (!res.ok) {
        console.warn('[VERSION] Version check failed:', res.status);
        return;
      }
      
      const { version, timestamp } = await res.json();
      console.log('[VERSION] Server version:', version, 'Timestamp:', new Date(timestamp).toLocaleString());
      
      if (!lastVersionRef.current) {
        console.log('[VERSION] First version check, storing:', version);
        lastVersionRef.current = version;
        return;
      }

      if (lastVersionRef.current !== version) {
        console.log('[VERSION] Version mismatch detected:', {
          current: lastVersionRef.current,
          server: version
        });
        setShowRefresh(true);
      } else {
        console.log('[VERSION] Version up to date:', version);
      }
    } catch (error) {
      console.error('[VERSION] Error checking version:', error);
    }
  };

  useEffect(() => {
    console.log('[VERSION] Setting up version check system');

    const handleVisibilityChange = () => {
      const isVisible = document.visibilityState === 'visible';
      console.log('[VERSION] Visibility changed:', {
        state: document.visibilityState,
        isVisible
      });
      
      if (isVisible) {
        checkVersion();
      }
    };

    const handleWindowFocus = () => {
      console.log('[VERSION] Window focused');
      checkVersion();
    };

    const handleWindowBlur = () => {
      console.log('[VERSION] Window blurred');
    };

    // Initial check
    checkVersion();

    // Set up periodic checks
    checkVersionRef.current = setInterval(() => {
      console.log('[VERSION] Running periodic version check');
      checkVersion();
    }, VERSION_CHECK_INTERVAL);

    // Set up visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange);
    globalThis.addEventListener('focus', handleWindowFocus);
    globalThis.addEventListener('blur', handleWindowBlur);
    
    console.log('[VERSION] Added all event listeners');

    return () => {
      console.log('[VERSION] Cleaning up version check system');
      if (checkVersionRef.current) {
        clearInterval(checkVersionRef.current);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      globalThis.removeEventListener('focus', handleWindowFocus);
      globalThis.removeEventListener('blur', handleWindowBlur);
    };
  }, []);

  return showRefresh ? <RefreshOverlay /> : null;
}; 