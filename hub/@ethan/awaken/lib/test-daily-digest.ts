import { 
  getUsersForDigest, 
  getUnreadMessagesForDigest, 
  generateDigestSubject,
  generateDigestEmailBody,
  type UserDigest 
} from "./daily-digest.ts";
import { 
  isThreePmInTimezone, 
  getLocalDateString, 
  getDigestTimeWindow 
} from "./daily-digest-processor.ts";

/**
 * Test function to verify daily digest functionality
 * This can be called manually to test the system
 */
export const testDailyDigest = async (): Promise<void> => {
  console.log("[TEST] Starting daily digest test...");
  
  try {
    // Test 1: Check timezone functions
    console.log("\n[TEST] Testing timezone functions:");
    const testTimezones = ["America/New_York", "Europe/London", "Asia/Tokyo", "America/Los_Angeles"];
    
    for (const timezone of testTimezones) {
      const isThreePm = isThreePmInTimezone(timezone);
      const localDate = getLocalDateString(timezone);
      const timeWindow = getDigestTimeWindow(timezone);
      
      console.log(`[TEST] ${timezone}:`);
      console.log(`  - Is 3pm: ${isThreePm}`);
      console.log(`  - Local date: ${localDate}`);
      console.log(`  - Time window: ${timeWindow.windowStart} to ${timeWindow.windowEnd}`);
    }
    
    // Test 2: Get users for digest
    console.log("\n[TEST] Testing user retrieval:");
    const users = await getUsersForDigest();
    console.log(`[TEST] Found ${users.length} users with daily email enabled`);
    
    if (users.length > 0) {
      const testUser = users[0];
      console.log(`[TEST] Testing with user: ${testUser.channelId} (${testUser.email})`);
      
      // Test 3: Get unread messages for the test user
      console.log("\n[TEST] Testing message retrieval:");
      const { windowStart, windowEnd } = getDigestTimeWindow(testUser.timezone);
      const userDigest = await getUnreadMessagesForDigest(testUser.channelId, windowStart, windowEnd);
      
      if (userDigest) {
        console.log(`[TEST] Found digest data for user ${testUser.channelId}:`);
        console.log(`  - Total messages: ${userDigest.totalMessageCount}`);
        console.log(`  - Coaches: ${userDigest.coachCount}`);
        console.log(`  - Coach names: ${userDigest.coaches.map(c => c.coachName).join(', ')}`);
        
        // Test 4: Generate email content
        console.log("\n[TEST] Testing email generation:");
        const subject = generateDigestSubject(userDigest);
        const localDate = getLocalDateString(testUser.timezone);
        const emailBody = generateDigestEmailBody(userDigest, localDate);
        
        console.log(`[TEST] Generated email:`);
        console.log(`  Subject: ${subject}`);
        console.log(`  Body preview (first 200 chars): ${emailBody.substring(0, 200)}...`);
        
        // Show full email body for review
        console.log("\n[TEST] Full email body:");
        console.log("=".repeat(60));
        console.log(emailBody);
        console.log("=".repeat(60));
        
      } else {
        console.log(`[TEST] No unread messages found for user ${testUser.channelId} in the time window`);
      }
    }
    
    console.log("\n[TEST] Daily digest test completed successfully!");
    
  } catch (error) {
    console.error("[TEST] Error during daily digest test:", error);
    throw error;
  }
};

/**
 * Test function to create a mock digest for demonstration
 */
export const createMockDigest = (): UserDigest => {
  return {
    userId: "123456789",
    email: "<EMAIL>",
    firstName: "John",
    timezone: "America/New_York",
    totalMessageCount: 5,
    coachCount: 2,
    coaches: [
      {
        coachName: "Kokoro",
        messageCount: 3,
        messages: [
          {
            id: "msg1",
            content: "How are you feeling today? I noticed you seemed a bit stressed in our last conversation.",
            date: new Date().toISOString(),
            coachName: "Kokoro",
            messageType: "message"
          },
          {
            id: "msg2", 
            content: "I found this interesting article about mindfulness that might help you.",
            date: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
            coachName: "Kokoro",
            messageType: "message"
          },
          {
            id: "msg3",
            content: "Remember to take breaks throughout your day. Your wellbeing is important.",
            date: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
            coachName: "Kokoro",
            messageType: "message"
          }
        ]
      },
      {
        coachName: "Marcus",
        messageCount: 2,
        messages: [
          {
            id: "msg4",
            content: "Discipline is the bridge between goals and accomplishment. How are you progressing on your objectives?",
            date: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
            coachName: "Marcus",
            messageType: "message"
          },
          {
            id: "msg5",
            content: "The obstacle is the way. What challenges are you facing today that could become opportunities?",
            date: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
            coachName: "Marcus", 
            messageType: "message"
          }
        ]
      }
    ]
  };
};

/**
 * Test function to generate and display a mock email
 */
export const testMockEmail = (): void => {
  console.log("[TEST] Generating mock daily digest email...");
  
  const mockDigest = createMockDigest();
  const subject = generateDigestSubject(mockDigest);
  const localDate = getLocalDateString(mockDigest.timezone);
  const emailBody = generateDigestEmailBody(mockDigest, localDate);
  
  console.log("\n[TEST] Mock Daily Digest Email:");
  console.log("=".repeat(60));
  console.log(`Subject: ${subject}`);
  console.log("");
  console.log(emailBody);
  console.log("=".repeat(60));
};
