"use client";

import { useState } from "npm:react@canary";
import React from "npm:react@canary";

import { Volume2Icon } from "@reframe/icons/volume2.ts";
import { MessageCircleIcon } from "@reframe/icons/message-circle.ts";
import { InfinityIcon } from "@reframe/icons/infinity.ts";
import { UpdatedInfinityIcon } from "./particles.tsx";

type ToggleState = "sound" | "chat" | "infinite";

interface IconToggleProps {
  onChange?: (state: ToggleState) => void;
  defaultState?: ToggleState;
}

export function IconToggle({
  onChange,
  defaultState = "sound",
}: IconToggleProps) {
  const [activeState, setActiveState] = useState<ToggleState>(defaultState);

  const handleClick = (state: ToggleState) => {
    setActiveState(state);
    onChange?.(state);
  };

  return (
    <div className="p-4 flex items-center justify-center">
      <div
        className="relative bg-[#95959526] rounded-full py-2 flex items-center justify-between gap-1"
        style={{ width: "auto", height: "auto" }}
      >

        {/* Hard-code highlight circle for two states */}
        <div
          className={`absolute h-full w-1/2 rounded-full 
            transition-all duration-300 ease-in-out
            bg-gradient-to-t from-[#FF57274D] to-[#FFC3604D]
            ${activeState === "sound" ? "left-0" : "left-1/2"}
          `}
        />

        {/* Icons */}
        <button
          onClick={() => handleClick("sound")}
          className={`relative z-10 h-auto w-12 rounded-full flex items-center justify-center transition-colors ${
            activeState === "sound" ? "text-white" : "text-white opacity-40"
          }`}
          aria-pressed={activeState === "sound"}
        >
          <Volume2Icon className="w-4 h-4 text-white" />
          <span className="sr-only">Sound Mode</span>
        </button>

        <button
          onClick={() => handleClick("chat")}
          className={`relative z-10 h-auto w-12 rounded-full flex items-center justify-center transition-colors ${
            activeState === "chat" ? "text-white" : "text-white opacity-40"
          }`}
          aria-pressed={activeState === "chat"}
        >
          <MessageCircleIcon className="w-4 h-4 text-white" />
          <span className="sr-only">Chat Mode</span>
        </button>



      </div>
    </div>
  );
}

