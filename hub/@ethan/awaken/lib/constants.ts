"use client";


export const webhookAudioSaveUrl =
  "https://hook.eu1.make.com/va6y4jl3ewe767dokkg86rijc8lfx1qi";

export type Messages = {
  Date?: string;
  Sender: "user" | "assistant";
  Content: string;
  Audio?: string;
  New?: boolean;
};

export const webhookInfinteUrl =
"https://hook.eu1.make.com/bkai9iamww32i7oxddjn1slhqyu5ocwf";

// const Questions = {
//   1: {
//     title: "What do you want to create in your life?",
//     answers: [
//       "Build my business",
//       "Supercharge my career",
//       "Develop an amazing relationship",
//       "Beautiful family",
//       "More money/wealth/abundance",
//       "Feel more free",
//       "Feel more peace",
//     ],
//   },
//   2: {
//     title: "Write your name",
//     answers: [],
//   },
//   3: {
//     title: "What you do for living?",
//     answers: [
//       "Student",
//       "Professional",
//       "Therapist/Coach",
//       "Homemaker",
//       "Business Owner",
//       "Retired",
//       "Unemployed",
//       "Other",
//     ],
//   },
//   4: {
//     title: "What's your age range?",
//     answers: ["18-24", "25-34", "35-44", "45-54", "55+"],
//   },
//   5: {
//     title: "Gender",
//     answers: ["Male", "Female", "Other"],
//   },
//   6: {
//     title: "Relationship status",
//     answers: ["Single", "Married", "Prefer not to say"],
//   },
//   7: {
//     title: "What you'll choose:",
//     answers: ["Spritituality", "Faith"],
//   },
//   8: {
//     title: "What's one thing you would love to change in your life?",
//     answers: [],
//   },
// };

export const Questions = {
  1: {
    title: "What would you love to use Awaken for?",
    answers: [
      "Navigate challenges",
      "Deepen relationships",
      "Clarify direction / purpose",
      "Increase leadership / impact",
      "Live with joy and freedom",
      "Be bolder / more courageous",
      "Feel ease and flow",
      "Be truer to myself",
      "Cultivate presence / awareness",
    ],
    multiple: true,
  },
  2: {
    title: "What are your spiritual/wellbeing practices?",
    answers: [
      "Meditation / Prayer",
      "Journaling / Self-inquiry",
      "Coaching / Therapy",
      "Exercise / Gym",
      "Nature / Walks",
      "Body & Breathwork",
      "Creative arts",
      "Reading / Learning",
      "Other",
    ],
    multiple: true,
  },
  3: {
    title: "What aspect of your life would you love us to shift or transform in the next 30 days?",
    answers: [], // For microphone input with text option
    multiple: false,
  },
};

export const Testimonials = [
  {
    text: "I've really appreciated how Kokoro has been making me live life more freely. I want to share this great tool with everyone",
    author: "Jivtesh Singh",
  },
  {
    text: "Awaken gave me the strength to keep going. Thank you from the bottom of my heart. It is already making a real difference",
    author: "Vale",
  },
  {
    text: "Awaken is extremely powerful, it always identifies the right thread to pull on, and always leaves me with one or two lines that I share with my friends and therapist",
    author: "Aditya",
  },
];

export const SUPERUSER_EMAILS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'] as string[];