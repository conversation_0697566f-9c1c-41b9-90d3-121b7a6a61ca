import { db } from "./db.ts";

export const essenceCost = (baseAmount: number, mult = 1) => baseAmount * mult;

export const fetchMultiplier = async (coachName: string) => {
  const result = await db
    .selectFrom("coaches")
    .select("cost_multiplier")
    .where("name", "=", coachName)
    .executeTakeFirst();

  // The database client returns snake_case columns as camelCase properties.
  // We default to 1.0 if the coach or their multiplier isn't found or is null.
  const multiplier = Number(result?.costMultiplier ?? 1.0);

  // Final check to prevent returning NaN from an unexpected value.
  return isNaN(multiplier) ? 1.0 : multiplier;
};
