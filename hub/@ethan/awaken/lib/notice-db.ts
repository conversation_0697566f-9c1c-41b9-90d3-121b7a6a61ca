"use server";

import { v4 as uuidv4 } from "npm:uuid";
import { db } from "./db.ts";
import { sql } from "npm:kysely";
import { SUPERUSER_EMAILS } from "./server-constants.ts";

// TypeScript interface for the Notice
export interface Notice {
  id: string;
  message: string;
  createdAt: string;
  createdBy: string;
}

// Get the latest notice for a user, considering their last seen notice
export const getLatestNotice = async (channelId: string) => {
  console.log('[DB] Fetching latest notice and last seen status for channel:', channelId);
  
  try {
    // First get the user's lastNoticeSeenAt timestamp
    const user = await db
      .selectFrom('user')
      .select(['lastNoticeSeenAt'])
      .where('channelId', '=', Number(channelId))
      .executeTakeFirst();
    
    const lastSeenAt = user?.lastNoticeSeenAt;
    
    // Get the latest notice from database
    const latestNotice = await db
      .selectFrom('notice')
      .select(['id', 'message', 'createdAt', 'createdBy'])
      .orderBy('createdAt', 'desc')
      .limit(1)
      .executeTakeFirst();
    
    if (!latestNotice) {
      console.log('[DB] No notices found');
      return null;
    }
    
    // Only return the notice if it's newer than the last seen notice
    if (!lastSeenAt || new Date(latestNotice.createdAt) > new Date(lastSeenAt)) {
      console.log('[DB] Found new unseen notice');
      // Format similar to the previous Airtable response
      return {
        id: latestNotice.id,
        Message: latestNotice.message,
        Date: latestNotice.createdAt,
        CreatedBy: latestNotice.createdBy
      };
    }
    
    console.log('[DB] No new notices since last seen');
    return null;
  } catch (error) {
    console.error('[DB] Error fetching latest notice:', error);
    throw error;
  }
};

// Get all notices
export const getAllNotices = async () => {
  console.log('[DB] Fetching all notices');
  
  try {
    const notices = await db
      .selectFrom('notice')
      .select(['id', 'message', 'createdAt', 'createdBy'])
      .orderBy('createdAt', 'desc')
      .execute();
    
    console.log(`[DB] Retrieved ${notices.length} notices`);
    
    // Format similar to the previous Airtable response
    return notices.map(notice => ({
      id: notice.id,
      Message: notice.message,
      Date: notice.createdAt,
      CreatedBy: notice.createdBy
    }));
  } catch (error) {
    console.error('[DB] Error fetching notices:', error);
    return [];
  }
};

// Create a new notice
export const createNotice = async (message: string, userEmail: string) => {
  console.log('[DB] Creating notice:', { message, userEmail });
  
  // Validate the user is authorized
  if (!userEmail) {
    console.error('[DB] No user email provided');
    throw new Error('User email is required');
  }
  
  if (!SUPERUSER_EMAILS.includes(userEmail)) {
    console.error('[DB] Unauthorized notice creation attempt:', userEmail);
    throw new Error('Unauthorized: Only superusers can create notices');
  }
  
  try {
    // Generate a new UUID for the notice
    const noticeId = uuidv4();
    const currentDate = new Date().toISOString();
    
    // Insert the notice into the database
    const result = await db
      .insertInto('notice')
      .values({
        id: noticeId,
        message: message,
        createdAt: currentDate,
        createdBy: userEmail
      })
      .returning(['id', 'message', 'createdAt', 'createdBy'])
      .executeTakeFirst();
    
    console.log('[DB] Notice created successfully:', result);
    
    // Return in a format similar to the Airtable response
    return {
      id: result.id,
      fields: {
        Message: result.message,
        Date: result.createdAt,
        CreatedBy: result.createdBy
      }
    };
  } catch (error) {
    console.error('[DB] Error creating notice:', error);
    throw error;
  }
};

// Update the last seen notice timestamp for a user
export const updateLastSeenNotice = async (channelId: string) => {
  console.log('[DB] Updating last seen notice for channel:', channelId);
  
  try {
    const currentDate = new Date().toISOString();
    
    await db
      .updateTable('user')
      .set({ lastNoticeSeenAt: currentDate })
      .where('channelId', '=', Number(channelId))
      .execute();
    
    console.log('[DB] Last seen notice updated successfully');
    
    // Return success response
    return { success: true, updatedAt: currentDate };
  } catch (error) {
    console.error('[DB] Error updating last seen notice:', error);
    throw error;
  }
}; 