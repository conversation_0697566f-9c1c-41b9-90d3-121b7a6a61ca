import Reframe from "@";

const stripeRequest = async (endpoint: string, options: RequestInit) => {
  const response = await fetch(`https://api.stripe.com/v1/${endpoint}`, {
    ...options,
    headers: {
      'Authorization': `Bearer ${Reframe.env.STRIPE_SECRET_KEY}`,
      'Content-Type': 'application/x-www-form-urlencoded',
      ...options.headers,
    },
  });

  const data = await response.json();

  if (!response.ok) {
    console.error("[STRIPE] API Error:", {
      status: response.status,
      data,
      endpoint,
      requestBody: options.body?.toString()
    });
    throw new Error(data.error?.message || `Stripe API error: ${response.status}`);
  }

  return data;
};

const stripe = {
  webhooks: {
    constructEvent: async (payload: string, signature: string, secret: string) => {
      // Basic signature validation
      if (!signature || !secret) {
        throw new Error('Missing signature or secret');
      }

      try {
        const event = JSON.parse(payload);
        // Here you'd normally verify the signature, but we'll skip for now
        // TODO: Implement proper signature verification
        return event;
      } catch (err) {
        throw new Error('Invalid payload');
      }
    }
  },

  customers: {
    create: async (email: string, metadata: Record<string, string>) => {
      const params = new URLSearchParams();
      params.append('email', email);
      
      // Add metadata
      Object.entries(metadata).forEach(([key, value]) => {
        params.append(`metadata[${key}]`, value);
      });

      return stripeRequest('customers', {
        method: 'POST',
        body: params,
      });
    }
  },

  subscriptions: {
    retrieve: async (subscriptionId: string) => {
      console.log("[STRIPE] Retrieving subscription:", subscriptionId);
      return stripeRequest(`subscriptions/${subscriptionId}`, {
        method: 'GET'
      });
    },
    
    update: async (subscriptionId: string, data: Record<string, any>) => {
      const params = new URLSearchParams();
      Object.entries(data).forEach(([key, value]) => {
        params.append(key, value.toString());
      });

      return stripeRequest(`subscriptions/${subscriptionId}`, {
        method: 'POST',
        body: params
      });
    },

    cancel: async (subscriptionId: string) => {
      // Updated to use the correct endpoint for canceling subscriptions
      // Instead of using `/cancel` endpoint, we use the update method with cancel_at_period_end=true
      const params = new URLSearchParams();
      params.append('cancel_at_period_end', 'true');
      
      return stripeRequest(`subscriptions/${subscriptionId}`, {
        method: 'POST',
        body: params
      });
    }
  },

  checkout: {
    sessions: {
      create: async (data: {
        customer: string;
        payment_method_types: string[];
        line_items: Array<{
          price_data?: {
            currency: string;
            product_data: {
              name: string;
              description?: string;
            };
            unit_amount: number;
          };
          quantity: number;
        }> | Array<{price: string; quantity: number}>;
        mode: string;
        success_url: string;
        cancel_url: string;
        metadata: Record<string, string>;
        payment_intent_data?: {
          description?: string;
        };
        discounts?: Array<{coupon: string}>;
      }) => {
        const params = new URLSearchParams();
        
        // Add required fields
        params.append('customer', data.customer);
        params.append('payment_method_types[]', data.payment_method_types[0]);
        params.append('mode', data.mode);
        params.append('success_url', data.success_url);
        params.append('cancel_url', data.cancel_url);

        // price_data for top up
        // price for subscription
        data.line_items.forEach((item, index) => {
          if (item.price_data) {
            params.append(`line_items[${index}][quantity]`, item.quantity.toString());
            params.append(`line_items[${index}][price_data][currency]`, item.price_data.currency);
            params.append(`line_items[${index}][price_data][unit_amount]`, item.price_data.unit_amount.toString());
            params.append(`line_items[${index}][price_data][product_data][name]`, item.price_data.product_data.name);
            if (item.price_data.product_data.description) {
              params.append(`line_items[${index}][price_data][product_data][description]`, item.price_data.product_data.description);
            }
          }
          else if(item.price) {
            params.append(`line_items[${index}][price]`, item.price);
            params.append(`line_items[${index}][quantity]`, item.quantity.toString());
          }
        });

        // Add metadata
        Object.entries(data.metadata).forEach(([key, value]) => {
          params.append(`metadata[${key}]`, value);
        });

        if (data.discounts) {
          data.discounts.forEach((discount, index) => {
            params.append(`discounts[${index}][coupon]`, discount.coupon);
          });
        }

        if (data.payment_intent_data) {
          Object.entries(data.payment_intent_data).forEach(([key, value]) => {
            params.append(`payment_intent_data[${key}]`, value.toString());
          });
        }

        return stripeRequest('checkout/sessions', {
          method: 'POST',
          body: params,
        });
      }
    }
  }
};

export default stripe; 