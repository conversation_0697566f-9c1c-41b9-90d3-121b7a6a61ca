import { v4 as uuid } from "npm:uuid";
import { db } from "./db.ts";
import { <PERSON>ail<PERSON>obDAO, EmailJob } from "./organic-email-db.ts";
import { getProfileText, getCoachPrompt, decryptMessages } from "../action.ts";
import { sendSimpleEmail } from "./email-sender.ts";
import { saveMessage, prepMessageHistoryAction } from "../actions/db/conversation-actions.ts";
import { geminiRequest, claudeRequest, openRouterRequest } from "./helper.ts";

// Type definitions
export type Stage = 1 | 2 | 3;
export type EmailType = "acknowledgement" | "quick_reflection" | "resource";

// Helper function for random number in range
function rand(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

// Pure function to pick email type based on stage
export function pickType(stage: Stage): EmailType {
  const r = Math.random();
  if (stage === 1)
    return r < 0.6
      ? "acknowledgement"
      : r < 0.8
      ? "resource"
      : "quick_reflection";
  if (stage === 2)
    return r < 0.6
      ? "quick_reflection"
      : r < 0.9
      ? "resource"
      : "acknowledgement";
  return r < 0.7
    ? "resource"
    : r < 0.9
    ? "quick_reflection"
    : "acknowledgement";
}

// Calculate send time with timezone awareness
export async function scheduleAt(
  stage: Stage, 
  userId: string,
  now = new Date()
): Promise<Date> {
  // Get user timezone from database
  const user = await db
    .selectFrom("user")
    .select(["timezone"])
    .where("channelId", "=", userId)
    .executeTakeFirst();
    
  const timezone = user?.timezone || "America/New_York"; // fallback
  
  // Calculate base delay - TESTING MODE: much shorter delays
  let targetTime: Date;
  
  if (stage === 1) targetTime = new Date(+now + 30000); // 30 seconds
  else if (stage === 2) targetTime = new Date(+now + 60000); // 1 minute
  else targetTime = new Date(+now + 120000); // 2 minutes
  
  // Adjust to 6am-10pm window in user's timezone
  const userTime = new Date(targetTime.toLocaleString("en-US", { timeZone: timezone }));
  const hour = userTime.getHours();
  
  if (hour < 6) {
    // Move to 6am same day
    userTime.setHours(6, 0, 0, 0);
  } else if (hour >= 22) {
    // Move to 6am next day
    userTime.setDate(userTime.getDate() + 1);
    userTime.setHours(6, 0, 0, 0);
  }
  
  // Convert back to UTC
  return new Date(userTime.toLocaleString("en-US", { timeZone: "UTC" }));
}

// Handle user turn - schedule organic email
export async function handleUserTurn(
  userId: string,
  coachId: string,
  now = new Date()
) {
  try {
    console.log(`[ORGANIC_EMAIL] Starting handleUserTurn for user ${userId}, coach ${coachId}`);
    
    // Check if user has opted out of emails
    console.log(`[ORGANIC_EMAIL] Checking email preference for user ${userId}`);
    const user = await db
      .selectFrom("user")
      .select(["emailDaily"])
      .where("channelId", "=", userId)
      .executeTakeFirst();
    
    console.log(`[ORGANIC_EMAIL] User email preference:`, user?.emailDaily);
    
    if (user?.emailDaily === 0) {
      console.log(`[ORGANIC_EMAIL] User ${userId} has opted out of daily emails`);
      return;
    }
    
    console.log(`[ORGANIC_EMAIL] Canceling any pending emails for user ${userId}`);
    await EmailJobDAO.cancelPending(userId);
    
    const first: Stage = 1;
    const emailType = pickType(first);
    const sendAtTime = await scheduleAt(first, userId, now);
    
    console.log(`[ORGANIC_EMAIL] Scheduling stage ${first} email type "${emailType}" for ${sendAtTime}`);
    
    await EmailJobDAO.insert({
      id: uuid(),
      userId: userId,
      coachId: coachId,
      stage: first,
      type: emailType,
      sendAt: sendAtTime.toISOString(),
      createdAt: now.toISOString(),
    });
    
    console.log(`[ORGANIC_EMAIL] Successfully scheduled organic email for user ${userId}`);
  } catch (error) {
    console.error(`[ORGANIC_EMAIL] Error in handleUserTurn for user ${userId}:`, error);
    throw error; // Re-throw to be caught by caller
  }
}

// Build email content using LLM
async function buildEmail(job: EmailJob): Promise<{ subject: string; body: string }> {
  // Get user profile and recent messages
  const [profileData, messagesData] = await Promise.all([
    getProfileText(job.userId),
    decryptMessages(job.userId, true) // last few messages only
  ]);
  
  const { profileText } = profileData;
  const { conversationText, timeElapsedNote } = prepMessageHistoryAction(messagesData);
  
  // Map email type to coach prompt key
  const promptKeyMap = {
    acknowledgement: "organicAcknowledgement",
    quick_reflection: "organicReflection", 
    resource: "organicResource"
  };
  
  // Get coach's prompt for this email type
  let systemPrompt: string | undefined;
  try {
    const coachPromptResponse = await getCoachPrompt(
      job.coachId, 
      promptKeyMap[job.type]
    );
    systemPrompt = coachPromptResponse.SystemPrompt;
  } catch (error) {
    console.log(`[ORGANIC_EMAIL] No prompt configured for ${promptKeyMap[job.type]}, using fallback`);
    // systemPrompt will remain undefined and we'll use the fallback
  }
  
  // Get coach metadata to determine which model to use (direct DB query for cron context)
  const coach = await db
    .selectFrom("coaches")
    .selectAll()
    .where("name", "=", job.coachId)
    .executeTakeFirst();
  
  // Extract model configuration
  let modelProvider = "google"; // Default to gemini for emails
  let modelName = "gemini-2.5-pro"; // Default model
  let determinedModelType: "claude" | "gemini" | "openrouter" = "gemini";
  
  if (coach?.metadata) {
    const metadata = typeof coach.metadata === 'string' 
      ? JSON.parse(coach.metadata) 
      : coach.metadata;
    
    // Check for messageModel configuration (preferred for messages)
    if (metadata.messageModel?.model) {
      const msgConfig = metadata.messageModel;
      modelProvider = (msgConfig.provider || "google").toLowerCase();
      modelName = msgConfig.model;
      
      // Determine model type
      if (modelProvider === "google") {
        determinedModelType = "gemini";
      } else if (modelProvider === "openrouter") {
        determinedModelType = "openrouter";
      } else if (modelProvider === "anthropic") {
        determinedModelType = "claude";
      }
    }
    // Fallback to legacy model config
    else if (metadata.model && Array.isArray(metadata.model) && metadata.model.length > 0) {
      const modelConfig = metadata.model[0];
      modelProvider = modelConfig.provider?.toLowerCase() || modelProvider;
      modelName = modelConfig.model || modelName;
      
      if (modelProvider === "google") {
        determinedModelType = "gemini";
      } else if (modelProvider === "openrouter") {
        determinedModelType = "openrouter";
      } else if (modelProvider === "anthropic") {
        determinedModelType = "claude";
      }
    }
  }
  
  console.log(`[ORGANIC_EMAIL] Using model: ${modelName} (${determinedModelType}) from provider: ${modelProvider}`);
  
  // Prepare prompt
  const userPrompt = `
    <CLIENT_PROFILE>${profileText}</CLIENT_PROFILE>
    <MESSAGE_HISTORY>${conversationText}</MESSAGE_HISTORY>
    <TIME_ELAPSED>${timeElapsedNote}</TIME_ELAPSED>
  `;
  
  // Fallback prompt if none configured
  const fallbackPrompt = `You are ${job.coachId}, a transformational AI coach. Generate a brief, warm email following up on our recent conversation. 

Format your response with XML tags:
<SUBJECT>Your email subject line here</SUBJECT>
<BODY>
Your email body content here
</BODY>`;
  const finalSystemPrompt = systemPrompt || fallbackPrompt;
  
  let emailContent: string;
  
  // Handle different model types
  if (determinedModelType === "claude") {
    // Claude format - claudeRequest expects (systemPrompt, messageArray)
    const messageArray = [{
      role: "user",
      content: userPrompt
    }];
    
    // claudeRequest returns a string directly, not a Response object
    emailContent = await claudeRequest(finalSystemPrompt, messageArray);
    
  } else if (determinedModelType === "openrouter") {
    // OpenRouter format - call with correct signature
    emailContent = await openRouterRequest(modelName, finalSystemPrompt, userPrompt);
    
  } else {
    // Gemini format (default)
    const geminiPayload = {
      system_instruction: {
        parts: { text: finalSystemPrompt }
      },
      contents: [{
        role: "user",
        parts: [{ text: userPrompt }]
      }],
      generationConfig: {
        temperature: 1.0
      },
      safetySettings: [
        { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_NONE" },
        { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_NONE" },
        { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_NONE" },
        { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_NONE" }
      ]
    };
    
    const geminiResponse = await geminiRequest([geminiPayload], modelName);
    const responseData = await geminiResponse.json();
    
    // Handle string "true"/"false" values from Gemini
    if (responseData.success !== "true" || !responseData.text1) {
      console.error("[ORGANIC_EMAIL] Gemini response error:", responseData);
      throw new Error(`Failed to generate email content: ${responseData.error || 'No text1 in response'}`);
    }
    
    emailContent = responseData.text1;
  }
  
  // Extract subject and body from XML tags (like in generateDailyAwakenings)
  const subjectMatch = emailContent.match(/<SUBJECT>([\s\S]*?)<\/SUBJECT>/);
  const bodyMatch = emailContent.match(/<BODY>([\s\S]*?)<\/BODY>/);
  
  let subject = "Following up on our conversation"; // Default fallback
  if (subjectMatch && subjectMatch[1]) {
    subject = subjectMatch[1].trim();
  }
  
  let body = emailContent; // Fallback to full content if no tags found
  if (bodyMatch && bodyMatch[1]) {
    body = bodyMatch[1].trim();
  }
  
  return { subject, body };
}

// Process organic emails - main cron function
export async function processOrganicEmails() {
  const now = new Date();
  const jobs = await db
    .selectFrom("emailJob")
    .selectAll()
    .where("sendAt", "<=", now.toISOString())
    .where("sentAt", "is", null)
    .where("cancelled", "=", 0)
    .limit(500)
    .execute();

  for (const job of jobs) {
    try {
      // Get user info for email
      const user = await db
        .selectFrom("user")
        .select(["email", "firstName"])
        .where("channelId", "=", job.userId)
        .executeTakeFirst();
        
      if (!user?.email) {
        console.log(`[ORGANIC_EMAIL] No email found for user ${job.userId}`);
        continue;
      }
      
      // Build email content
      const { subject, body } = await buildEmail(job);
      
      // Get coach name for email sender
      const coach = await db
        .selectFrom("coaches")
        .select(["name"])
        .where("name", "=", job.coachId)
        .executeTakeFirst();
      
      // Save to conversation table
      await saveMessage(
        job.userId,
        "assistant",
        body,
        new Date().toISOString(),
        "Default",
        "proactive_message", // new message type
        job.coachId
      );
      
      // Send email as simple, friend-like format
      const coachName = coach?.name || job.coachId;
      await sendSimpleEmail(user.email, subject, body, coachName);
      
      // Mark job as sent
      await db
        .updateTable("emailJob")
        .set({ sentAt: now.toISOString() })
        .where("id", "=", job.id)
        .execute();

      // Schedule next stage if applicable
      if (job.stage < 3) {
        const nextStage = (job.stage + 1) as Stage;
        await EmailJobDAO.insert({
          id: uuid(),
          userId: job.userId,
          coachId: job.coachId,
          stage: nextStage,
          type: pickType(nextStage),
          sendAt: (await scheduleAt(nextStage, job.userId, now)).toISOString(),
          createdAt: now.toISOString(),
        });
      }
    } catch (error) {
      console.error(`[ORGANIC_EMAIL] Error processing job ${job.id}:`, error);
      // Natural retry on next cron run since sent_at remains null
    }
  }
}