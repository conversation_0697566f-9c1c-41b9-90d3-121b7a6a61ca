"use client";

import React from "npm:react@canary";
import { Button, Text } from "@reframe/ui/main.tsx";
import { Logo } from "./logo.tsx";
import { motion } from "npm:framer-motion";

export const RefreshOverlay = () => {
  // Use responsive size for logo based on screen width
  const [logoSize, setLogoSize] = React.useState(80);
  
  React.useEffect(() => {
    const handleResize = () => {
      setLogoSize(globalThis.innerWidth < 640 ? 60 : 80);
    };
    
    // Set initial size
    handleResize();
    
    // Add event listener
    globalThis.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => globalThis.removeEventListener('resize', handleResize);
  }, []);

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="fixed inset-0 z-[9999] backdrop-blur-md"
      style={{
        background: 'linear-gradient(180deg, rgba(0,0,0,0.97) 0%, rgba(0,0,0,0.95) 100%)'
      }}
    >
      <div className="flex items-center justify-center min-h-screen p-4">
        <motion.div 
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="w-full max-w-md text-center space-y-6 sm:space-y-8 relative px-3"
        >
          {/* Background glow effect */}
          <div 
            className="absolute inset-0 -z-10 blur-[100px] rounded-full"
            style={{
              background: 'linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)',
              opacity: 0.3
            }}
          />

          <div className="flex justify-center">
            <Logo size={logoSize} />
          </div>

          <div className="space-y-2 sm:space-y-3">
            <div>
              <Text css="text-[1.8rem] sm:text-[2.5rem] font-light text-[#FCA311] leading-tight">
                New Version Available
              </Text>
            </div>
            
            <div>
              <Text css="text-base sm:text-lg text-gray-300/90 font-light leading-relaxed px-1">
                We've made improvements! Please refresh to get the latest features.
              </Text>
            </div>
          </div>

          <Button 
            variant="outline"
            css="w-full px-4 sm:px-6 py-2.5 sm:py-3 bg-gradient-to-r from-[#00000033] to-[#5C3B0633] border-[#FCA3114D] hover:border-[#FCA311] text-white rounded-full flex items-center justify-center space-x-2 transition-all duration-300 hover:bg-[#5C3B06] hover:border-[#FCA311] hover:scale-105"
            onClick={() => globalThis.location.reload()}
          >
            <span className="text-[#FCA311] font-light text-base sm:text-lg">
              Refresh Now
            </span>
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
}; 