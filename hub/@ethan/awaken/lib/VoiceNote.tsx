"use client";

import React, { useState, useRef, useEffect } from "npm:react@canary";
import { Button, Text } from "@reframe/ui/main.tsx";
import { animated, useSpring } from "npm:@react-spring/web@9.7.3";

// Import icons from icons.tsx
import { 
  StopIcon, 
  RefreshIcon,
  PlayIcon, 
  PauseIcon, 
  CircleXIcon, 
  MicIcon, 
  SendIcon,
  AudioVisualizerIcon
} from "./icons.tsx";
import { useVoiceRecorder } from "./hooks/useVoiceRecorder.tsx";

// Audio waveform visualization component
const AudioWaveform = ({ analyser, isRecording }: { analyser: AnalyserNode | null; isRecording: boolean }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const previousBarHeightsRef = useRef<number[]>([]); // Store previous heights for damping

  useEffect(() => {
    if (!analyser || !isRecording || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const canvasCtx = canvas.getContext("2d");
    if (!canvasCtx) return;

    // Theme consistency: Use amber color with adjusted opacity
    const barColor = "rgba(251, 191, 36, 0.6)"; // amber-400 with 60% opacity

    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    const dampingFactor = 0.2; // Adjust for more/less smoothing (0-1)
    const noiseThreshold = 60; // Minimum value to draw a bar (0-255)

    let animationFrameId: number;
    const draw = () => {
      if (!isRecording || !canvasRef.current) { 
        if(animationFrameId) cancelAnimationFrame(animationFrameId);
        return;
      }

      animationFrameId = requestAnimationFrame(draw);
      // Use getByteFrequencyData for frequency visualization
      analyser.getByteFrequencyData(dataArray); 

      canvasCtx.clearRect(0, 0, canvas.width, canvas.height);
      canvasCtx.fillStyle = barColor;

      const barWidth = (canvas.width / bufferLength) * 2.5; // Maintain bar width
      let x = 0;
      const canvasHeight = canvas.height; // Cache height
      const currentBarHeights = previousBarHeightsRef.current;

      // Initialize previous heights if it's the first frame or length mismatch
      if (currentBarHeights.length !== bufferLength) {
        previousBarHeightsRef.current = new Array(bufferLength).fill(0);
      }

      for (let i = 0; i < bufferLength; i++) {
        // Apply noise threshold (higher for low frequencies)
        const rawValue = dataArray[i];
        const lowFrequencyBinCount = Math.floor(bufferLength * 0.15); // Target ~first 15% bins
        const currentThreshold = i < lowFrequencyBinCount ? 95 : noiseThreshold; // Higher threshold (e.g., 85) for low bins

        const value = rawValue > currentThreshold ? rawValue : 0;

        // Calculate target bar height
        const targetBarHeight = (value / 255) * canvasHeight;

        // Apply damping (lerp towards target height)
        const previousHeight = currentBarHeights[i] || 0;
        const dampedBarHeight = previousHeight + (targetBarHeight - previousHeight) * dampingFactor;
        currentBarHeights[i] = dampedBarHeight; // Update stored height

        // Draw bars from the bottom up
        canvasCtx.fillRect(x, canvasHeight - dampedBarHeight, barWidth - 1, dampedBarHeight);
        x += barWidth + 1; // Maintain spacing
      }
    };

    draw();

    return () => {
      cancelAnimationFrame(animationFrameId);
      // Reset previous heights when recording stops
      previousBarHeightsRef.current = [];
    };
  }, [analyser, isRecording]);

  // Placeholder flat line when not recording - Use theme color
  useEffect(() => {
    if (!canvasRef.current || isRecording) return;
    const canvas = canvasRef.current;
    const canvasCtx = canvas.getContext("2d");
    if (!canvasCtx) return;
    canvasCtx.clearRect(0, 0, canvas.width, canvas.height);
    // Theme consistency: Use amber color with lower opacity for placeholder
    canvasCtx.fillStyle = "rgba(251, 191, 36, 0.3)"; // amber-400 with 30% opacity
    const centerY = canvas.height / 2;
    canvasCtx.fillRect(0, centerY - 0.5, canvas.width, 1); // Thinner placeholder line
  }, [isRecording]);

  return (
    <canvas
      ref={canvasRef}
      width={200}
      height={60}
      className="w-full h-[50px] sm:h-[80px]"
    />
  );
};

interface VoiceNoteProps {
  channelId: string;
  coachName: string;
  isOpen: boolean;
  onClose: () => void;
  onRecordingComplete: (audioBlob: Blob) => void;
}

// Responsive CSS for VoiceNote
if (typeof window !== "undefined") {
  const styleId = "responsive-voice-note-style";
  if (!document.getElementById(styleId)) {
    const style = document.createElement("style");
    style.id = styleId;
    style.innerHTML = `
      .responsive-voice-note-container {
        max-height: 100dvh;
        max-height: calc(100dvh - env(safe-area-inset-bottom, 0px));
        overflow-y: auto;
        overscroll-behavior: contain;
      }
      @media (max-width: 640px) {
        .responsive-voice-note-container {
          padding-left: 0.5rem !important;
          padding-right: 0.5rem !important;
          padding-bottom: max(0.5rem, env(safe-area-inset-bottom, 0px)) !important;
          border-radius: 1.25rem 1.25rem 0 0 !important;
        }
      }
    `;
    document.head.appendChild(style);
  }
}

export const VoiceNote: React.FC<VoiceNoteProps> = ({
  channelId,
  coachName,
  isOpen,
  onClose,
  onRecordingComplete,
}) => {
  const [maxHeight, setMaxHeight] = React.useState<string>('100dvh');
  const containerRef = React.useRef<HTMLDivElement>(null);

  // Use our new hook
  const {
    audioRef,
    isRecording,
    recordingTime,
    audioBlob,
    analyser,
    isPlaying,
    playbackProgress,
    audioDuration,
    isLoading,
    showSuccess,
    formatTime,
    startRecording,
    stopRecording,
    togglePlayback,
    cancelRecording,
    submitRecording,
    resetRecordingState
  } = useVoiceRecorder({ 
    channelId, 
    onRecordingComplete 
  });

  // Dynamically set maxHeight so the VoiceNote aligns with the top border of SwipeableCards
  React.useEffect(() => {
    function updateMaxHeight() {
      const cards = document.querySelector('[data-swipeable-cards-root]') as HTMLElement | null;
      if (cards) {
        const cardsRect = cards.getBoundingClientRect();
        const viewportHeight = globalThis.innerHeight;
        // Distance from bottom of viewport to top of cards
        const height = Math.max(viewportHeight - cardsRect.top, 200); // fallback min height
        setMaxHeight(height + 'px');
      } else {
        // When not used with SwipeableCards, use 45% of viewport height
        const viewportHeight = globalThis.innerHeight;
        const height = Math.max(viewportHeight * 0.45, 300); // 45% of viewport, min 300px
        setMaxHeight(height + 'px');
      }
    }
    if (isOpen) {
      updateMaxHeight();
      globalThis.addEventListener('resize', updateMaxHeight);
      // Also update if scroll changes cards position (e.g. keyboard pops up)
      globalThis.addEventListener('scroll', updateMaxHeight, true);
      return () => {
        globalThis.removeEventListener('resize', updateMaxHeight);
        globalThis.removeEventListener('scroll', updateMaxHeight, true);
      };
    }
  }, [isOpen]);

  const slideAnimation = useSpring({
    from: {
      transform: 'translateY(100%)',
      opacity: 0,
    },
    to: {
      transform: isOpen ? 'translateY(0%)' : 'translateY(100%)',
      opacity: isOpen ? 1 : 0,
    },
    delay: isOpen ? 80 : 0, // Small delay before starting the slide-in animation
    // Smoother, more gradual animation for the slide-up effect
    config: {
      mass: 1.2,      // Slightly higher mass for more weight
      tension: 180,   // Lower tension for less immediate snap
      friction: 24,   // Balanced friction for smooth movement
      clamp: false,   // Allow natural overshooting and settling
    },
  });

  // Update onClose to also handle success state
  const handleClose = () => {
    resetRecordingState(); // Will also clear success state
    onClose();
  };

  // Determine button icon and instruction text based on state
  let ButtonIcon = MicIcon;
  let instructionText = "For best results, speak for at least 30 seconds"; // Updated instruction text
  let buttonAriaLabel = "Start recording";
  
  if (isRecording) {
    ButtonIcon = StopIcon;
    instructionText = `Recording ${formatTime(recordingTime)}`;
    buttonAriaLabel = "Stop recording";
  } else if (audioBlob) {
      ButtonIcon = audioBlob ? SendIcon : MicIcon;
      instructionText = `Ready to send your message to ${coachName}`;
      buttonAriaLabel = "Send recording";
  } else if (showSuccess) {
      instructionText = "Send another message?";
  }

  // Handle main button click based on current state
  const handleMainButtonClick = () => {
    if (isLoading) return;
    if (isRecording) {
      stopRecording();
    } else if (audioBlob) {
      submitRecording();
    } else {
      startRecording(); 
    }
  };

  return (
    <animated.div
      ref={containerRef}
      style={{
        ...slideAnimation,
        height: maxHeight,
        maxHeight: maxHeight,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        // Add inner glow effect to match card styling
        boxShadow: '0 -8px 40px -10px rgba(251, 191, 36, 0.5), inset 0 1px 0 rgba(251, 191, 36, 0.1)',
      }}
      // Updated styling to match Awaken theme with black background, orange accents, and glow
      className="max-w-2xl mx-auto fixed bottom-0 left-0 right-0 bg-black border-2 border-amber-400/90 rounded-t-3xl px-5 pt-5 pb-4 z-[60] responsive-voice-note-container"
    >
      {/* Header Section */}
      <div className="grid grid-cols-[1fr_auto] items-center mb-3">
        <div className="flex items-center gap-2">
          {/* Use Mic icon in header for clarity */}
          <MicIcon className="w-5 h-5 text-amber-400" />
          <Text className="text-base font-semibold text-white">Voice Note</Text>
        </div>
        <Button
          variant="ghost"
          css="p-1 h-8 w-8 text-neutral-400 hover:text-white hover:bg-white/10 rounded-full justify-self-end" 
          onClick={handleClose}
          aria-label="Close voice note"
          disabled={isLoading}
        >
          <CircleXIcon className="w-6 h-6" />
        </Button>
      </div>
        
      {/* Playback/Waveform Area */}
      <div
        className="flex items-center justify-center w-full bg-black border border-amber-500/40 rounded-xl px-2 py-3 min-h-[100px] mb-2 sm:px-4 sm:py-5 sm:min-h-[130px] sm:mb-7"
        style={{
          boxShadow: 'inset 0 2px 8px rgba(0, 0, 0, 0.5), inset 0 0 0 1px rgba(251, 191, 36, 0.1)'
        }}
      >
        {isLoading ? (
          <div className="flex flex-col items-center justify-center w-full py-2">
            <div className="w-7 h-7 border-[3px] border-amber-400/30 border-t-amber-400 rounded-full animate-spin mb-2"></div>
            <Text className="text-sm font-medium text-amber-400 mb-1">Processing...</Text>
            <Text className="text-center text-xs text-neutral-300">This will just take a moment</Text>
          </div>
        ) : showSuccess ? (
          <div className="flex flex-col items-center justify-center w-full py-2">
            <div className="flex items-center justify-center w-8 h-8 bg-amber-400/20 rounded-full">
              <SendIcon className="w-5 h-5 text-amber-400" />
            </div>
            <Text className="text-center text-sm font-medium text-amber-400">Message sent! 🎉</Text>
            <Text className="text-center text-xs text-neutral-300">{coachName} will respond shortly in chat.</Text>
          </div>
        ) : isRecording ? (
          <div className="flex items-center w-full">
            <AudioWaveform analyser={analyser} isRecording={isRecording} />
          </div>
        ) : audioBlob ? (
          <div className="flex items-center w-full gap-3">
            <Button
              variant="ghost"
              css="p-0 h-9 w-9 rounded-full flex items-center justify-center bg-amber-400/15 hover:bg-amber-400/30 text-amber-400 flex-shrink-0 transition-colors"
              onClick={togglePlayback}
              aria-label={isPlaying ? "Pause message" : "Play message"}
            >
              {isPlaying ? (
                <PauseIcon className="w-5 h-5" />
              ) : (
                <PlayIcon className="w-5 h-5" />
              )}
            </Button>
            <div className="flex-grow h-1.5 bg-amber-400/20 rounded-full overflow-hidden relative group">
              <div
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-amber-400 to-amber-500 rounded-full transition-width duration-75 ease-linear"
                style={{ width: `${playbackProgress}%` }}
              />
            </div>
            <span className="text-neutral-400 font-mono text-xs tabular-nums w-12 text-right flex-shrink-0">
              {/* Always show the recording duration we know, regardless of metadata load status */}
              {formatTime(audioDuration)}
            </span>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center w-full h-[40px] py-2 sm:h-[50px]">
            <AudioVisualizerIcon className="w-8 h-8 text-amber-400/30 opacity-60" />
            <Text className="text-xs text-neutral-400 mt-1">Tap the mic button to record</Text>
          </div>
        )}
      </div>
        
      {/* Main Action Button Area */}
      <div className="flex items-center justify-center pt-1 mb-2 sm:pt-1 sm:mb-2">
        <Button
          variant="outline"
          css={`
            w-[72px] h-[72px] rounded-full border-2 border-amber-400/90
            bg-gradient-to-r from-amber-300 to-amber-500 hover:from-amber-500 hover:to-amber-600
            flex items-center justify-center
            shadow-lg shadow-amber-400/50 hover:shadow-amber-400/70
            transition-all duration-300 ease-out
            focus:outline-none focus:ring-2 focus:ring-amber-400/50 focus:ring-offset-2 focus:ring-offset-black
            ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
          `}
          onClick={handleMainButtonClick}
          disabled={!isOpen || isLoading} // Only disable when loading or closed, not during success
          aria-label={buttonAriaLabel} // Use dynamic aria-label
        >
          {/* Icon color adjusted for better contrast on amber background */}
          <ButtonIcon className="w-8 h-8 text-black" /> 
        </Button>
      </div>

      {/* Instruction Text & Reset Button Area */}
      <div className="min-h-[2.5rem] flex items-center justify-center px-2 pb-2 sm:min-h-[3.5rem] sm:px-4 sm:pb-4">
        <Text className="text-neutral-400 text-xs text-center"> 
          {instructionText}
          {audioBlob && !isRecording && !isLoading && !showSuccess && (
            <Button 
              variant="link" 
              // Consistent link styling
              css="text-amber-500/80 hover:text-amber-500 text-xs ml-1.5 p-0 h-auto inline-block align-baseline font-medium underline hover:no-underline" 
              onClick={resetRecordingState}
            >
              or reset
            </Button>
          )}
        </Text>
      </div>
      
      {/* Hidden Audio Element */}
      <audio 
        ref={audioRef} 
        className="hidden" 
        onPlay={() => {/* Handled in the hook */}}
        onPause={() => {/* Handled in the hook */}}
        onTimeUpdate={() => {/* Handled in the hook */}}
        onEnded={() => {/* Handled in the hook */}}
        onLoadedMetadata={() => {/* Handled in the hook */}}
        preload="metadata"
      />
    </animated.div>
  );
};
