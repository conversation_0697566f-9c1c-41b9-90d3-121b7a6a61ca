import Reframe from "@";

import { createAuth, Credentials, Google } from "@reframe/auth/auth.ts";

import { upsertUser } from "./upsert-user.ts";

import { v4 as uuid } from "npm:uuid";

const anonymous = Credentials({
  credentials: {
    appId: {
      label: "App ID",
    },
  },
  authorize(credentials) {
    console.log("credentials ", credentials);
    // if (!credentials.appId || typeof credentials.appId !== "string") {
    //   throw new Error("appId is required");
    // }

    return { email: `${uuid()}@awakan.${uuid()}.anon` };
  },
});

export const Auth = createAuth("/auth", {
  secret: Reframe.env.AUTH_SECRET,
  providers: [
    Google({
      clientId: "************-57me27e9dt01v748n2i1bjevma3va8vj.apps.googleusercontent.com",
      clientSecret: "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",
      authorization: {
        params: {
          prompt: "select_account",
          access_type: "offline"
        },
      },
    }),
    anonymous,
  ],
  pages: {
    signIn: "/auth/sign-in",
    signOut: "/auth/sign-out",
    error: "/auth/error",
  },
  callbacks: {
    signIn({ user }) {
      if (user.email) {
        return true;
      }

      return false;
    },

    jwt: async ({ token, account }) => {
      if (token.email) {
        try {
          if (account) {
            const match = /^([^@]+)@([^\.]+)\.anon$/.exec(token.email);

            if (match) {
              const [_, sessionId] = match;

              token.id = sessionId;

              return token;
            }

            const user = await upsertUser({
              email: token.email,
              name: token.name!,
              image: token.picture ?? undefined,
            });

            if (user) {
              token.id = user.id;
              token.email = user.email;
              token.name = user.name;
              token.image = user.image;
              return token;
            }
          }
        } catch (error) {
          console.log(error);
        }
      }

      return token;
    },

    session: ({ session, token }) => {
      console.log("session ", session);
      if (!session.user) {
        return session;
      }

      session.user.id = token.id;
      session.user.email = token.email;
      session.user.name = token.name;
      session.user.image = token.image;
      return session;
    },
  },
});
