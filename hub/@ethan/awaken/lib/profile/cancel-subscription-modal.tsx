"use client";

import React, { useState } from "npm:react@canary";
import { motion } from "npm:framer-motion";
import { Button, Text } from "@reframe/ui/main.tsx";
import { AlertTriangleIcon } from "../icons.tsx";
import { cancelSubscription } from "../../actions/stripe-actions.ts";
import { UnreadIcon } from "@reframe/icons/unread.ts";

interface CancelSubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  subscriptionId: string;
  currentPeriodEnd: string;
  onCancellationComplete: () => void;
}

export const CancelSubscriptionModal: React.FC<CancelSubscriptionModalProps> = ({
  isOpen,
  onClose,
  subscriptionId,
  currentPeriodEnd,
  onCancellationComplete
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  
  // Format the end date nicely
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  
  const handleCancel = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await cancelSubscription(subscriptionId);
      
      if (result.success) {
        setShowSuccess(true);
        // Notify parent component that cancellation is complete
        onCancellationComplete();
      } else {
        setError("Failed to cancel subscription. Please try again later.");
      }
    } catch (err) {
      console.error("[CANCEL_MODAL] Error:", err);
      setError(err instanceof Error ? err.message : "An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <motion.div
        initial={{ scale: 0.95 }}
        animate={{ scale: 1 }}
        exit={{ scale: 0.95 }}
        className="bg-[#1a1a1a] rounded-xl w-full max-w-md p-6 relative"
      >
        {showSuccess ? (
          // Success View
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            
            <h2 className="text-xl font-bold text-white mb-3">Subscription Canceled</h2>
            
            <p className="text-gray-300 mb-6">
              Your subscription has been canceled. You'll continue to have access to all premium features until{" "}
              <span className="text-[#FCA311] font-semibold">{formatDate(currentPeriodEnd)}</span>.
            </p>
            
            <Button 
              onClick={onClose}
              css="w-full bg-[#FCA311] text-white hover:bg-[#FCA311]/90 py-2.5"
            >
              Close
            </Button>
          </div>
        ) : (
          // Confirmation View
          <>
            <div className="text-center mb-5">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 rounded-full bg-[#FCA31120] flex items-center justify-center">
                  <AlertTriangleIcon size={24} color="#FCA311" />
                </div>
              </div>
              
              <h2 className="text-xl font-bold text-white mb-2">Cancel Subscription?</h2>
              
              <p className="text-gray-300 mb-6">
                Are you sure you want to cancel your subscription? You'll continue to have access to all premium features until{" "}
                <span className="text-[#FCA311] font-semibold">{formatDate(currentPeriodEnd)}</span>.
              </p>
              
              {error && (
                <div className="bg-red-500/20 text-red-500 p-3 rounded-md mb-4 text-sm">
                  {error}
                </div>
              )}
              
              <div className="flex gap-3">
                <Button
                  variant="ghost"
                  onClick={onClose}
                  css="flex-1 border border-gray-700 text-white"
                  disabled={isLoading}
                >
                  Keep Subscription
                </Button>
                
                <Button
                  onClick={handleCancel}
                  css="flex-1 bg-[#FCA311] text-white hover:bg-[#FCA311]/90"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex items-center justify-center">
                      <UnreadIcon css="text-xl text-white w-5 h-5 animate-spin mr-2" />
                      Canceling...
                    </span>
                  ) : (
                    "Yes, Cancel"
                  )}
                </Button>
              </div>
            </div>
          </>
        )}
      </motion.div>
    </motion.div>
  );
}; 