"use client";

import React, { useEffect, useState, useRef } from "npm:react@canary";
import { Button, Text, X, Y } from "@reframe/ui/main.tsx";
import { motion, AnimatePresence } from "npm:framer-motion";
import { Logo } from "../logo.tsx";
import {
  SparklesIcon,
  LogOutIcon,
  MessageIcon,
  UserIcon,
  CrownIcon,
  CalendarIcon,
  PhoneIcon,
  StarIcon,
  SmallBellIcon,
  EssenceIcon,
  CircleXIcon
} from "../icons.tsx";
import { PlanType, PLANS } from "../plans.ts";
import { updateDailyEmailSetting } from "../../actions/db/user-actions.ts";
import { getUserData } from "../db.ts";
import { TopUpOverlay } from "../top-up-overlay.tsx";
import { CancelSubscriptionModal } from "./cancel-subscription-modal.tsx";

// Cache for userData data by channelId
const userDataCache = new Map();

const nameMapping = {
  free: "Awaken Balance (Free)",
  basic: "Awaken Beyond",
  premium: "Awaken Boundless"
};

// Add this helper function at the top level
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export const ProfilePage = ({
  user,
  userData: initialUserData,
  onClose,
  onUpgrade,
  setIsTopUpOpen
}: {
  user: any;
  userData: any;
  onClose: () => void;
  onUpgrade: () => void;
  setIsTopUpOpen: (isOpen: boolean) => void;
}) => {
  // Use the cached data if available, otherwise use initialSubscription
  const cachedUserData = user?.channelId ? userDataCache.get(user.channelId) : null;
  const [userData, setUserData] = useState(cachedUserData || initialUserData);
  const [canCancelSubscription, setCanCancelSubscription] = useState(false);

  console.log("userData", userData, initialUserData);
  // Track if this is the first render
  const firstRenderRef = useRef(true);

  // Add state for daily email toggle - prefer localStorage first, then cached or initial subscription data
  const [isDailyEmail, setIsDailyEmail] = useState<boolean>(() => {
    // Check localStorage first for immediate value
    if (typeof window !== 'undefined') {
      const cachedEmailDaily = localStorage.getItem('emailDaily-' + user.channelId);
      if (cachedEmailDaily !== null) {
        return cachedEmailDaily === '1';
      }
    }

    // Fall back to subscription data
    return (cachedUserData?.emailDaily !== undefined) ? cachedUserData.emailDaily :
      (initialUserData?.emailDaily !== undefined) ? initialUserData.emailDaily :
        user.emailDaily === 1;
  });

  console.log("MONTHLY ESSENCE BALANCE", PLANS[userData.subscription.planId.split('_')[0] as PlanType].essencePerMonth);

  // Add state for top-up overlay

  // Add state for subscription cancellation modal
  const [showCancelModal, setShowCancelModal] = useState(false);

  // Fetch the latest subscription data when the component mounts
  useEffect(() => {
    const fetchLatestUserData = async () => {
      if (user?.channelId) {
        try {
          const userData = await getUserData(user.channelId.toString());
          if (userData) {
            setUserData(userData);

            // Update the email daily toggle state based on the subscription data
            if (userData.emailDaily !== undefined) {
              setIsDailyEmail(userData.emailDaily);
              // Store in localStorage for future reference
              localStorage.setItem('emailDaily-' + user.channelId, userData.emailDaily ? '1' : '0');
            }

            // Update the cache with the latest data
            userDataCache.set(user.channelId, userData);
          }
        } catch (error) {
          console.error('Failed to fetch subscription in profile:', error);
        }
      }
    };

    // Only fetch if this is the first time the component renders
    // This ensures it fetches whenever the profile is opened from chat.tsx
    if (firstRenderRef.current) {
      fetchLatestUserData();
      firstRenderRef.current = false;
    }
  }, [user?.channelId]);

  const isSubscribed = userData?.subscription?.planId !== "free_plan";
  
  useEffect(() => {
    if(isSubscribed && userData.currentPeriodEnd !== null && !userData.subscription.cancelAtPeriodEnd) {
      setCanCancelSubscription(true);
    }
  }, [isSubscribed, userData]);

  console.log("canCancelSubscription", canCancelSubscription);
                                

  // Add handler for daily email toggle
  const handleToggleDailyEmail = async () => {
    // Flip the local state first for immediate UI response
    const newValue = !isDailyEmail;
    setIsDailyEmail(newValue);

    // Store in localStorage immediately for smooth transitions
    localStorage.setItem('emailDaily-' + user.channelId, newValue ? '1' : '0');

    // Send to server route
    try {
      const success = await updateDailyEmailSetting(user.channelId, newValue);

      if (!success) {
        // Revert local state if request fails
        setIsDailyEmail(!newValue);
        // Also revert localStorage
        localStorage.setItem('emailDaily-' + user.channelId, (!newValue) ? '1' : '0');
        console.error("Failed to update daily email setting");
      }
    } catch (error) {
      // Revert local state if request fails
      setIsDailyEmail(!newValue);
      // Also revert localStorage
      localStorage.setItem('emailDaily-' + user.channelId, (!newValue) ? '1' : '0');
      console.error("Request error:", error);
    }
  };

  // Add handler for refreshing user data after cancellation
  const handleCancellationComplete = async () => {
    if (user?.channelId) {
      try {
        const freshUserData = await getUserData(user.channelId.toString());
        if (freshUserData) {
          setUserData(freshUserData);
          setCanCancelSubscription(false);
          // Update the cache with the latest data
          userDataCache.set(user.channelId, freshUserData);
        }
      } catch (error) {
        console.error('Failed to refresh user data after cancellation:', error);
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-3"
    >
      <motion.div
        initial={{ scale: 0.95 }}
        animate={{ scale: 1 }}
        exit={{ scale: 0.95 }}
        className="bg-[#1a1a1a] rounded-xl w-full max-w-md p-3 sm:p-4 relative max-h-[90vh] overflow-y-auto"
      >
        <button
          onClick={onClose}
          className="absolute right-2 sm:right-3 top-0 sm:top-1 p-1 rounded-full hover:bg-white/10 transition-colors"
        >
          <CircleXIcon size={24} color="#ffffff" />
        </button>

        {/* Profile Header - more compact layout */}
        <div className="flex justify-between items-center mb-3">
          <div className="flex-1">
            <h2 className="text-lg font-bold text-white mb-0.5">{user.name}</h2>
            <p className="text-xs text-gray-400 mb-1">{user.email}</p>
            <div className="flex items-center space-x-1.5 text-gray-400 text-xs">
              <CalendarIcon size={12} color="#FCA311" />
              <span>Member since {formatDate(user.createdAt)}</span>
            </div>
          </div>
          
          <div className="w-14 h-14 sm:w-16 sm:h-16 rounded-full bg-gradient-to-r from-[#FCA311] to-[#E85D04] p-1 mr-4 sm:mr-6">
            <img
              src={user.image}
              alt={user.name}
              className="w-full h-full rounded-full border-2 border-black"
            />
          </div>
        </div>

        {/* Subscription Section - more compact */}
        <div className="space-y-3">
          <div className="flex items-center justify-between bg-[#2a2a2a] rounded-lg p-2.5 border border-[#FCA31133]">
            <div className="flex items-center space-x-2">
              <div className="p-1.5 bg-[#FCA31120] rounded-lg">
                <CrownIcon size={18} color="#FCA311" />
              </div>
              <div>
                <Text css="text-base font-bold text-[#FCA311] block">
                  {nameMapping[userData.subscription.planId.split('_')[0] as keyof typeof nameMapping]}
                </Text>
                {userData.subscription.currentPeriodEnd && (
                  <Text css="text-xs text-gray-400 mt-0.5 block">
                    {userData.subscription.cancelAtPeriodEnd ? (
                      <span>Access until {formatDate(userData.subscription.currentPeriodEnd)}</span>
                    ) : (
                      <span>Renews {formatDate(userData.subscription.currentPeriodEnd)}</span>
                    )}
                  </Text>
                )}
              </div>
            </div>
          </div>

          {/* Essence Balance Section - more compact */}
          <div className="bg-[#2a2a2a] rounded-lg p-2.5 border border-[#FCA31133]">
            {/* Top section with Monthly and Added Essence */}
            <div className="flex justify-between mb-1.5">
              {/* Monthly Essence */}
              <div>
                <Text css="text-xs text-gray-400 mb-0.5 block">Monthly Essence</Text>
                <div className="flex items-baseline space-x-1">
                  <Text css="text-lg font-bold text-[#FCA311]">
                    {Math.floor(userData.monthlyEssenceBalance)}
                  </Text>
                  <Text css="text-xs text-gray-400">/ {PLANS[userData.subscription.planId.split('_')[0] as PlanType].essencePerMonth}</Text>
                </div>
              </div>
              
              {/* Added Essence */}
              <div className="text-right flex flex-col">
                <Text css="text-xs text-gray-400 mb-0.5 block">Added Essence</Text>
                <Text css="text-lg font-bold text-[#FCA311] block">
                  {Math.floor(userData.addedEssenceBalance)}
                </Text>
              </div>
            </div>
            
            {/* Separator */}
            <div className="h-px bg-gray-700 my-1"></div>
            
            {/* Total Essence bottom section */}
            <div className="flex justify-between items-start pt-1">
              <div className="flex flex-col">
                <Text css="text-sm font-bold text-white block">Total Essence</Text>
                <Text css="text-xs text-gray-400 mt-0.5 block">Available for your journey</Text>
              </div>
              <div className="text-right flex flex-col">
                <div className="flex items-center justify-end gap-1">
                  <div className="text-[#ff6b35]">
                    <EssenceIcon size={20} color="#ff6b35" />
                  </div>
                  <Text css="text-xl font-bold text-[#FCA311] block">
                    {Math.floor(userData.monthlyEssenceBalance + userData.addedEssenceBalance)}
                  </Text>
                </div>
                <Text css="text-xs text-gray-400 mt-0.5 block">
                </Text>
              </div>
            </div>
            
            {/* Top Up button for subscribed users */}
            {isSubscribed && (
              <Button
                variant="default"
                onClick={() => setIsTopUpOpen(true)}
                css="mt-1.5 bg-[#FCA311] text-white hover:bg-[#FCA311]/90 w-full py-1 flex items-center justify-center gap-1.5"
              >
                Top Up
              </Button>
            )}
          </div>

          {/* Daily Email Toggle - more compact */}
          <div className="flex items-center justify-between bg-[#2a2a2a] rounded-lg p-2.5">
            <span className="text-white text-sm font-medium">Daily Awakening Email</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={isDailyEmail}
                onChange={handleToggleDailyEmail}
                className="sr-only peer"
              />
              <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-[#FCA311] rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-[#FCA311]" />
            </label>
          </div>

          {/* Usage Stats - more compact */}
          <div className="bg-[#2a2a2a] rounded-lg p-2.5">
            <div className="grid grid-cols-2 gap-2.5">
              {/* Call Usage Stats */}
              <div className="bg-[#222222] rounded-lg p-2">
                <div className="flex items-center mb-1.5">
                  <div className="p-1 bg-[#FCA31120] rounded-lg mr-1.5">
                    <PhoneIcon size={14} color="#FCA311" />
                  </div>
                  <Text css="text-xs text-gray-300 font-medium">Call Usage</Text>
                </div>
                
                <div className="flex flex-col space-y-0.5">
                  <div className="flex justify-between items-baseline">
                    <Text css="text-xs text-gray-400">This Month:</Text>
                    <Text css="text-sm font-bold text-white">
                      {Math.floor(userData.callMinsThisPeriod)} <span className="text-xs font-normal text-gray-400">mins</span>
                    </Text>
                  </div>
                  <div className="flex justify-between items-baseline">
                    <Text css="text-xs text-gray-400">All Time:</Text>
                    <Text css="text-sm font-bold text-white">
                      {Math.floor(userData.totalCallMins)} <span className="text-xs font-normal text-gray-400">mins</span>
                    </Text>
                  </div>
                </div>
              </div>
              
              {/* Chat Usage Stats */}
              <div className="bg-[#222222] rounded-lg p-2">
                <div className="flex items-center mb-1.5">
                  <div className="p-1 bg-[#FCA31120] rounded-lg mr-1.5">
                    <MessageIcon size={14} color="#FCA311" />
                  </div>
                  <Text css="text-xs text-gray-300 font-medium">Chat Usage</Text>
                </div>
                
                <div className="flex flex-col space-y-0.5">
                  <div className="flex justify-between items-baseline">
                    <Text css="text-xs text-gray-400">This Month:</Text>
                    <Text css="text-sm font-bold text-white">
                      {userData.messagesThisPeriod} <span className="text-xs font-normal text-gray-400">msgs</span>
                    </Text>
                  </div>
                  <div className="flex justify-between items-baseline">
                    <Text css="text-xs text-gray-400">All Time:</Text>
                    <Text css="text-sm font-bold text-white">
                      {userData.totalMessages} <span className="text-xs font-normal text-gray-400">msgs</span>
                    </Text>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Cancel Link - more compact */}
        {canCancelSubscription && (
          <div className="mt-3 text-center">
            <Button 
              variant="link"
              onClick={() => setShowCancelModal(true)}
              css="text-xs text-white hover:text-red-300 hover:underline transition-colors px-2 py-1"
            >
              Cancel Subscription
            </Button>
          </div>
        )}

        {!isSubscribed && (
          <Button
            onClick={onUpgrade}
            css="w-full bg-[#FCA311] text-white hover:bg-[#FCA311]/90 mt-3 py-2"
          >
            Upgrade
          </Button>
        )}
        
        {/* Subscription Cancellation Modal */}
        {showCancelModal && (
          <CancelSubscriptionModal 
            isOpen={showCancelModal}
            onClose={() => setShowCancelModal(false)}
            subscriptionId={userData.subscription.id}
            currentPeriodEnd={userData.subscription.currentPeriodEnd}
            onCancellationComplete={handleCancellationComplete}
          />
        )}
      </motion.div>
    </motion.div>
  );
};
