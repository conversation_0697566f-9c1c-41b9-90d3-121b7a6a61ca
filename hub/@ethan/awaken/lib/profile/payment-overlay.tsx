"use client"

import React from "npm:react@canary"
import { useState, useEffect, useRef, useCallback } from "npm:react@canary"
import { useSpring, animated, config } from "npm:@react-spring/web@9.7.3"
import { Button, Text } from "@reframe/ui/main.tsx"
import { CircleXIcon } from "@reframe/icons/circle-x.ts"
import { type PlanType, PLANS } from "../plans.ts"
import { CheckIcon } from "@reframe/icons/check.ts"
import { getStripePrices } from "../../action.ts"
import { UnreadIcon } from "@reframe/icons/unread.ts"
import { ChevronLeftIcon } from "@reframe/icons/chevron-left.ts"
import { ChevronRightIcon } from "@reframe/icons/chevron-right.ts"
import { FeatureCard, BASIC_FEATURES, PREMIUM_FEATURES } from "../plan-features.tsx"


interface Feature {
  icon: string
  title: string
  description: string
}

interface PaymentOverlayProps {
  isOpen: boolean
  onClose: () => void
  user: any
  enrollmentMessage?: string
}

// Add this CSS class to hide scrollbars and enable smooth scrolling
const scrollableContentStyles = {
  scrollBehavior: 'smooth',
  msOverflowStyle: 'none', // IE and Edge
  scrollbarWidth: 'none', // Firefox
  '&::-webkit-scrollbar': { // Chrome, Safari and Opera
    display: 'none'
  }
} as const;

export const PaymentOverlay: React.FC<PaymentOverlayProps> = ({ isOpen, onClose, user, enrollmentMessage }) => {
  const [isYearly, setIsYearly] = useState(true);
  const [loading, setLoading] = useState<PlanType | false>(false);
  const [error, setError] = useState<string | null>(null);
  const [prices, setPrices] = useState<any>(null);
  const [activePlanIndex, setActivePlanIndex] = useState(1);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const planCardsRef = useRef<HTMLDivElement>(null);

  const overlayAnimation = useSpring({
    opacity: isOpen ? 1 : 0,
    config: config.default
  })

  const modalAnimation = useSpring({
    transform: isOpen ? 'translateY(0%)' : 'translateY(100%)',
    config: { ...config.default, tension: 200, friction: 20 }
  })

  const toggleAnimation = useSpring({
    x: isYearly ? 20 : 0,
    config: { tension: 500, friction: 30 }
  })

  useEffect(() => {
    if (isOpen) {
      const fetchPrices = async () => {
        try {
          const stripePrices = await getStripePrices();
          setPrices(stripePrices);
        } catch (err) {
          console.error("Error fetching prices:", err);
          setError("Failed to load pricing information");
        }
      };
      fetchPrices();
    }
  }, [isOpen]);

  const handleSubscribe = async (planType: PlanType) => {
    console.log("[PAYMENT] Starting subscription flow:", { planType, isYearly })

    try {
      setLoading(planType)
      setError(null)

      const formattedPlanId = `${planType}_plan_${isYearly ? "yearly" : "monthly"}`

      const payload = {
        email: user.email,
        channelId: user.channelId.toString(),
        plan: formattedPlanId,
        isYearly,
      }

      const response = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        throw new Error(await response.text() || "Failed to create checkout session")
      }

      const data = await response.json()
      if (!data.url) {
        throw new Error("Invalid checkout session response")
      }

      globalThis.location.href = data.url
    } catch (err) {
      console.error("[PAYMENT] Error:", err)
      setError(err instanceof Error ? err.message : "Failed to start subscription")
    } finally {
      setLoading(false)
    }
  }

  // Swipe logic for plan cards
  const handleSwipeLeft = () => {
    if (activePlanIndex > 0) {
      setActivePlanIndex(activePlanIndex - 1);
    }
  };

  const handleSwipeRight = () => {
    if (activePlanIndex < 1) {
      setActivePlanIndex(activePlanIndex + 1);
    }
  };

  // Improved touch handling with bounds checking
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 50) {
      // Swiped left - but only if we're not already at the rightmost card
      if (activePlanIndex < 1) {
        handleSwipeRight();
      }
    }

    if (touchStart - touchEnd < -50) {
      // Swiped right - but only if we're not already at the leftmost card
      if (activePlanIndex > 0) {
        handleSwipeLeft();
      }
    }
    
    // Reset touch positions after handling
    setTouchStart(0);
    setTouchEnd(0);
  };

  const getActivePlan = (): PlanType => {
    return activePlanIndex === 0 ? "basic" : "premium";
  };

  if (!isOpen) return null

  return (
    <>
      {isOpen && (
        <>
          <animated.div
            style={overlayAnimation}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-40"
            onClick={onClose}
          />
          <animated.div
            className="fixed inset-0 z-50 flex justify-center items-start overflow-hidden"
            style={modalAnimation}
          >
            <div className="w-full h-full sm:max-w-[400px] md:max-w-xl lg:max-w-2xl bg-black relative flex flex-col">
              <div className="flex-none">
                <Button
                  variant="ghost"
                  css="absolute right-2 top-2 p-2 text-white hover:bg-transparent z-50"
                  onClick={onClose}
                >
                  <CircleXIcon className="w-6 h-6" />
                </Button>
              </div>

              <div 
                ref={scrollContainerRef}
                className="flex-1 overflow-y-auto px-6 min-h-0"
                style={{
                  ...scrollableContentStyles,
                  WebkitOverflowScrolling: 'touch'
                }}
              >
                <div className="px-6 pt-[min(48px,5vh)] pb-[min(24px,3vh)]">
                  <h1 className="text-2xl font-medium text-white mb-[min(15px,1.6vh)] text-center">
                    Awaken your fullest life
                  </h1>
                    {enrollmentMessage && (
                      <div className="text-[#FCA311] text-base mt-2 mb-0">
                        {enrollmentMessage.split(". ").map((sentence, index, array) => (
                          <p key={index} className={index < array.length - 1 ? "mb-3" : ""}>
                            {index === 0 ? `"${sentence}` : sentence}
                            {index === array.length - 1 ? `"` : "."}
                          </p>
                        ))}
                      </div>
                    )}
                </div>
                
                {/* Swipeable Plan Cards */}
                <div className="relative">
                  {/* Navigation Indicators - bars instead of dots */}
                  <div className="flex justify-center space-x-3 mb-4">
                    <div 
                      className={`h-3 w-6 rounded-full transition-colors duration-300 cursor-pointer
                                 ${activePlanIndex === 0 ? 'bg-[#FCA311]' : 'bg-gray-600'}`}
                      onClick={() => setActivePlanIndex(0)}
                    />
                    <div 
                      className={`h-3 w-6 rounded-full transition-colors duration-300 cursor-pointer
                                 ${activePlanIndex === 1 ? 'bg-[#A020F0]' : 'bg-gray-600'}`}
                      onClick={() => setActivePlanIndex(1)}
                    />
                  </div>
                  
                  {/* Left/Right Navigation Buttons */}
                  {activePlanIndex > 0 && (
                    <button 
                      className="absolute left-1 top-1/2 transform -translate-y-1/2 z-10 
                                bg-black/60 rounded-full p-1.5
                                transition-opacity duration-300 hover:opacity-80"
                      onClick={handleSwipeLeft}
                    >
                      <ChevronLeftIcon className="w-5 h-5 text-white" />
                    </button>
                  )}

                  {activePlanIndex < 1 && (
                    <button 
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 z-10 
                                bg-black/60 rounded-full p-1.5
                                transition-opacity duration-300 hover:opacity-80"
                      onClick={handleSwipeRight}
                    >
                      <ChevronRightIcon className="w-5 h-5 text-white" />
                    </button>
                  )}
                  
                  {/* Fixed Container Implementation */}
                  <div className="overflow-hidden">
                    <div 
                      ref={planCardsRef}
                      className="flex"
                      style={{
                        width: '200%',
                        transform: `translateX(-${activePlanIndex * 50}%)`,
                        transition: 'transform 300ms ease-out'
                      }}
                      onTouchStart={handleTouchStart}
                      onTouchMove={handleTouchMove}
                      onTouchEnd={handleTouchEnd}
                    >
                      {/* Basic Plan Card */}
                      <div className="w-1/2 px-2">
                        <FeatureCard 
                          features={BASIC_FEATURES} 
                          planType="basic" 
                          title="BEYOND"
                        />
                      </div>
                      
                      {/* Premium Plan Card */}
                      <div className="w-1/2 px-2">
                        <FeatureCard 
                          features={PREMIUM_FEATURES} 
                          planType="premium" 
                          title="BOUNDLESS"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "Awaken is more valuable than 99% of the apps that I have in my life."
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Tom Sedge</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "I've really appreciated how [Awaken] has been making me live life more freely... I want to share this great tool with everyone"
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Jivtesh Singh</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "Awaken gave me the strength to keep going. Thank you from the bottom of my heart. It is already making a real difference"
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Vale</p>
                </div>

                {/* Links to Terms and Privacy Policy */}
                <div className="flex justify-center gap-4 mb-6">
                  <a 
                    href="/terms" 
                    className="text-white/40 text-[min(0.875rem,2vh)] hover:text-white/60 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Terms of Service
                  </a>
                  <a 
                    href="/privacy-policy" 
                    className="text-white/40 text-[min(0.875rem,2vh)] hover:text-white/60 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Privacy Policy
                  </a>
                </div>

                <div className="pb-4" />
              </div>

              <div className="flex-none bg-black/95 border-t border-[#FCA31133] backdrop-blur-sm">
                <div className="px-6 py-[min(8px,1vh)]">
                  <div
                    className="
                      relative rounded-2xl p-[min(12px,1.5vh)]
                      bg-gradient-to-b from-[#1E1E1E] via-[#241B18] to-black
                      shadow-[0_0_50px_12px_rgba(255,87,39,0.1),0_0_100px_20px_rgba(255,87,39,0.07)]
                      border border-[#3a332e]

                      before:absolute before:inset-0 before:-z-10 before:rounded-2xl
                      before:p-[2px]
                      before:bg-gradient-to-r before:from-[#FF5727]/60 before:via-[#FFC360]/60 before:to-[#FF5727]/60
                      before:blur-xl

                      after:absolute after:inset-0 after:-z-20 after:rounded-2xl
                      after:p-[2px]
                      after:bg-gradient-to-r after:from-[#FF5727]/20 after:via-[#FFC360]/20 after:to-[#FF5727]/20
                      after:blur-2xl

                      overflow-hidden
                    "
                  >
                    {/* optional subtle radial glow */}
                    <div className="absolute inset-0 pointer-events-none 
                                    bg-[radial-gradient(circle_at_center,_rgba(255,87,39,0.05)_0%,_rgba(0,0,0,0)_80%)]" />

                    <div className="relative">
                      <div className="flex flex-col items-center text-center gap-[min(6px,0.8vh)]">
                        <div className="flex items-center justify-center gap-2 mb-3">
                          <span className={`${!isYearly ? "text-[#FCA311] font-bold" : "text-white/60"} text-[min(0.875rem,1.8vh)]`}>
                            monthly
                          </span>
                          <div
                            className="w-10 h-5 bg-black/30 border border-[#FCA311] rounded-full relative cursor-pointer"
                            onClick={() => setIsYearly(!isYearly)}
                          >
                            <animated.div
                              className="absolute top-[1px] left-[1px] w-4 h-4 bg-[#FCA311] rounded-full"
                              style={toggleAnimation}
                            />
                          </div>
                          <span className={`${isYearly ? "text-[#FCA311] font-bold" : "text-white/60"} text-[min(0.875rem,1.8vh)]`}>
                            yearly (-20%)
                          </span>
                        </div>

                        {!prices ? (
                          <div className="flex items-center justify-center gap-2">
                            <UnreadIcon css="text-2xl text-[#FCA311] w-6 h-6 animate-spin" />
                            <Text css="text-[#FCA311]">Loading prices...</Text>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center gap-1">
                            <div className="flex items-baseline justify-center gap-1">
                              <span className="text-[#FCA311] text-[min(1.5rem,3vh)] font-semibold">
                                ${prices ? (isYearly ? 
                                  (prices[`${getActivePlan()}_plan`].price.yearly / 12).toFixed(0): 
                                  prices[`${getActivePlan()}_plan`].price.monthly.toFixed(0)) : '...'}
                              </span>
                              <span className="text-white text-[min(0.875rem,1.8vh)]">
                                / {isYearly ? 'month' : 'month'}
                              </span>
                              <span className="text-white/40 text-[min(0.75rem,1.5vh)] line-through">
                                ${prices ? (isYearly ? 
                                  (prices[`${getActivePlan()}_plan`].originalPrice.yearly / 12).toFixed(0): 
                                  prices[`${getActivePlan()}_plan`].originalPrice.monthly.toFixed(0)) : '...'}
                              </span>
                            </div>
                          </div>
                        )}

                        <div className="text-center mb-[min(8px,1vh)] mt-[-10px]">
                          <span className="text-[#FCA311] text-[min(0.875rem,1.8vh)] font-medium">50% off</span>
                          <span className="text-white text-[min(0.875rem,1.8vh)] ml-1">during pre-launch</span>
                        </div>

                        <Button
                          css="
                            w-full
                            bg-[#FCA311]
                            text-white
                            font-medium
                            text-lg
                            py-3
                            rounded-md

                            /* Add a subtle white glow around the button */
                            shadow-[0_0_12px_rgba(255,255,255,0.4)]
                            hover:shadow-[0_0_18px_rgba(255,255,255,0.6)]
                            transition-shadow

                            /* Optionally fade the orange a bit on hover */
                            hover:bg-[#FCA311]/90
                          "
                          onClick={() => handleSubscribe(getActivePlan())}
                          disabled={!!loading}
                        >
                          {loading ? (
                            <span className="flex items-center justify-center">
                              <UnreadIcon css="text-xl text-white w-5 h-5 animate-spin mr-2" />
                              Processing...
                            </span>
                          ) : (
                            "Let's do it"
                          )}
                        </Button>

                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {error && (
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-4 py-2 rounded-md">
                  {error}
                </div>
              )}
            </div>
          </animated.div>
        </>
      )}
    </>
  )
}