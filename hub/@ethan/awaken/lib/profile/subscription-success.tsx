"use client";

import React from "npm:react@canary";
import { motion } from "npm:framer-motion";
import { Logo } from "../logo.tsx";
import { Button } from "@reframe/ui/main.tsx";

export const SubscriptionSuccess = ({ onComplete, planType, planTitle }: { onComplete: () => void, planType: string, planTitle: string }) => {
  console.log("planType", planType);
  console.log("planTitle", planTitle);
  console.log("onComplete", onComplete);
  
  // Use the same consistent colors as the feature cards, with gradients
  const getPlanStyles = () => {
    if (planType === "basic") {
      return {
        mainColor: "#FCA311",
        glowColor: "rgba(252,163,17,0.4)",
        gradientFrom: "#FF5727",
        gradientTo: "#FFC360",
      };
    } else {
      return {
        mainColor: "#A020F0",
        glowColor: "rgba(138,43,226,0.4)",
        gradientFrom: "#8A2BE2", 
        gradientTo: "#DA70D6",
      };
    }
  };

  const styles = getPlanStyles();

  return (
    <div className="fixed inset-0 w-full min-h-svh max-h-svh bg-black overflow-hidden z-50">
      <motion.div
        className="relative flex flex-col min-h-svh p-6 items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {/* Background gradient - using plan colors */}
        <motion.div
          className="absolute w-[506px] h-[506px] rounded-full blur-[100px]"
          style={{
            top: "-180px",
            background: `linear-gradient(270deg, ${styles.gradientFrom} 43.87%, ${styles.gradientTo} 66.94%)`,
          }}
          initial={{ opacity: 0.1 }}
          animate={{ opacity: 0.4 }}
          transition={{ duration: 0.8 }}
        />

        <div className="flex flex-col items-center gap-8 z-10">
          <Logo size={80} />
          
          <motion.div 
            className="flex flex-col items-center gap-4"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <h1 className="text-4xl text-center font-light" style={{ color: styles.mainColor }}>
              Welcome to Awaken {planTitle}!
            </h1>
            <p className="text-white text-center max-w-md">
              Your subscription has been activated. You now have access to all premium features.
            </p>
          </motion.div>

          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 1, type: "spring" }}
          >
            <Button
              variant="outline"
              onClick={onComplete}
              css={`px-6 py-2 rounded-full transition-all duration-300
                  bg-gradient-to-r from-[#00000033] to-[#5C3B0633] 
                  border-[${styles.mainColor}80] text-[${styles.mainColor}] 
                  hover:bg-[${styles.mainColor}20]`}
            >
              Continue to Awaken
            </Button>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}; 