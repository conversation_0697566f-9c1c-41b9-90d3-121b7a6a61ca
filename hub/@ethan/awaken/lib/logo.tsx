"use client";

import React from "npm:react@canary";

export const Logo = ({ size = 100 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 402 402"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M257.697 310C297.014 289.498 323.857 248.358 323.857 200.947C323.857 133.066 268.828 78.0378 200.947 78.0378C133.066 78.0378 78.0378 133.066 78.0378 200.947C78.0378 248.358 104.881 289.498 144.198 310H32.1368C11.8047 278.591 0 241.148 0 200.947C0 89.9672 89.9672 0 200.947 0C311.928 0 401.895 89.9672 401.895 200.947C401.895 241.148 390.09 278.591 369.758 310H257.697ZM43.9298 326.36C48.6326 332.24 53.6589 337.851 58.9817 343.164H342.913C348.236 337.851 353.262 332.24 357.965 326.36H43.9298ZM102.792 376.332C93.9273 371.36 85.4781 365.736 77.5093 359.524H324.385C316.417 365.736 307.967 371.36 299.103 376.332H102.792ZM140.653 392.692C159.686 398.671 179.94 401.895 200.947 401.895C221.954 401.895 242.209 398.671 261.242 392.692H140.653Z"
      fill="url(#paint0_linear_3353_118)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_3353_118"
        x1="200.947"
        y1="401.895"
        x2="200.947"
        y2="1.3068e-05"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FF5727" />
        <stop offset="1" stop-color="#FFC360" />
      </linearGradient>
    </defs>
  </svg>
);
