"use client";

import { Layout } from "@/lib/layout.tsx";
import { Link } from "@reframe/ui/main.tsx";
import React from "npm:react@canary";

export const NotFoundPage = () => {
  // Mock user and userData as Layout expects them. Adjust if you have a better way to handle this for a 404 page.
  const mockUser = { channelId: "" }; 
  const mockUserData = null;

  return (
    <Layout user={mockUser} userData={mockUserData} keepDrawer={false} currentPath="/404">
      <div className="flex flex-col items-center justify-center flex-1 text-center">
        <h1 className="text-6xl font-bold text-white">404</h1>
        <p className="mt-4 text-xl text-gray-300">Oops! The page you're looking for doesn't exist.</p>
        <p className="mt-2 text-md text-gray-400">It might have been moved or deleted.</p>
        <Link to="/" className="mt-8 px-6 py-3 text-lg font-semibold text-white bg-orange-500 rounded-lg hover:bg-orange-600 transition-colors duration-300">
          Go to Homepage
        </Link>
      </div>
    </Layout>
  );
};

export default NotFoundPage;
