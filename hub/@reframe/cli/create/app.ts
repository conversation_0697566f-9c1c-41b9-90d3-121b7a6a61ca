import $ from "https://deno.land/x/dax/mod.ts";
import { parse } from "https://deno.land/std@0.200.0/flags/mod.ts";

const args = parse<{
  from?: string;
  template?: string;
}>(Deno.args);

const app = String(args._[0]);
const from = args.from ??
  `@template/${args.template ?? "starter"}`;

if (
  !app || !app.match(/@[a-z0-9-]+\/[a-z0-9-]+/i)
) {
  console.error(
    "template @<org>/<app> [--template <template>] [--from <from>]",
  );
  Deno.exit(1);
}

try {
  await Deno.stat(from);
} catch {
  console.error(`${from} not found`);
  Deno.exit(1);
}

// chec if app already exists
try {
  await Deno.stat(app);
  console.error(`${app} already exists`);
  Deno.exit(1);
} catch {}

console.log(`creating ${app} from ${from}...`);
await $`cp -r ${from} ${app}`;
console.log("done!");
