import Reframe from "@";
import { createRoute } from "@reframe/react/router.tsx";
import type { Path } from "@reframe/zero/defs.ts";
import { Editor, Node, Tree } from "./client.tsx";
import { Async } from "@reframe/react/async.tsx";
import { Page, ScrollArea, X, Y } from "@reframe/ui/main.tsx";

const readSource = (path: string, headers: Record<string, string>) => {
  // TODO: why is this needed?
  if (!headers.directive) {
    headers.directive = "server";
  }

  return Reframe.sourceGraph.read(Reframe.resolve(path, "/"), headers);
};

const listSources = async () => {
  const hashes = await Reframe.sourceGraph.serialize();

  const node: Node = {
    type: "directory",
    children: {},
  };

  for (
    const path of Object.keys(hashes).sort(
      (a, b) => a.localeCompare(b),
    )
  ) {
    if (path.startsWith("/~")) {
      continue;
    }

    const segments = path.split("/").slice(1);
    const filename = segments.pop()!.split("?")[0];

    const current = {
      path: [] as string[],
      node: node,
    };

    for (const segment of segments) {
      if (!current.node.children[segment]) {
        current.node.children[segment] = {
          type: "directory",
          children: {},
        };
      }

      const next = current.node.children[segment];
      if (next.type !== "directory") {
        throw new Error(`expected directory at ${current.path.join("/")}`);
      }

      current.node = next;
    }

    current.node.children[filename] = {
      type: "file",
    };
  }

  return node;
};

const App = createRoute(
  (Router) => (
    <Router.Route
      serve:options={() =>
        new Response(null, {
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods":
              "GET, POST, PUT, DELETE, PATCH, OPTIONS",
            "Access-Control-Allow-Headers": "*",
          },
        })}
      route:editor={(Router) => (
        <Router.Route
          route={(...segments) => (Router) => (
            <Router.Route
              layout={() => (
                <Async
                  value={async () => {
                    const source = await readSource(segments.join("/"), {});
                    return source.text();
                  }}
                  then={(content) => (
                    <Page>
                      <X css="bg-[#001c40]">
                        <Y css="p-4 text-[#cccccc]" min-x="300px" fit-x>
                          <ScrollArea>
                            <Async
                              value={listSources}
                              then={(sources) => (
                                <Tree
                                  active={segments.join("/")}
                                  node={sources}
                                  path={["/"]}
                                />
                              )}
                            />
                          </ScrollArea>
                        </Y>

                        <Editor
                          path={segments.join("/")}
                          content={content}
                        />
                      </X>
                    </Page>
                  )}
                />
              )}
            />
          )}
        />
      )}
      route:x={(Router) => (
        <Router.Route
          middleware={async (request) => {
            const url = new URL(request.url);
            const path = url.pathname.slice("/x/".length);

            const headers = Object.fromEntries(url.searchParams.entries());
            const source = await readSource(path, headers);

            return source
              .setHeader("Content-Type", "application/javascript")
              .response();
          }}
        />
      )}
      route:blob={(Router) => (
        <Router.Route
          serve={async (request) => {
            const url = new URL(request.url);
            const headers = Object.fromEntries(url.searchParams.entries());

            const hashes = await request.json() as string[];
            const sources = new Map<Path, string>();

            for (const hash of hashes) {
              const source = await Reframe.sourceGraph.blob.read(
                `/${hash}`,
                headers,
              );
              sources.set(hash as Path, await source.text());
            }

            return new Response(
              JSON.stringify(Object.fromEntries(sources.entries())),
              {
                headers: {
                  "Content-Type": "application/json",
                  "Access-Control-Allow-Origin": "*",
                  "Access-Control-Allow-Methods":
                    "GET, POST, PUT, DELETE, PATCH, OPTIONS",
                  "Access-Control-Allow-Headers": "*",
                },
              },
            );
          }}
        />
      )}
      route:debug={(Router) => (
        <Router.Route
          middleware={async (request) => {
            const url = new URL(request.url);
            const path = url.pathname.slice("/debug/".length);

            // read from data-url
            const source = await fetch(`blob:null/${path}`);

            return new Response(
              (await source.text())
                .split("\n")
                .map((line, i) => `${i + 1}: ${line}`)
                .join("\n"),
              {
                "headers": {
                  ...source.headers,
                  "Content-Type": "text/plain",
                },
              },
            );
          }}
        />
      )}
    />
  ),
);

export { App };

export const test = 15;
