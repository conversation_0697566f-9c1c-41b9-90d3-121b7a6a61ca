import type { OnMount } from "npm:@monaco-editor/react";

export type Monaco = Parameters<OnMount>[1];
export type Editor = Parameters<OnMount>[0];

export type MonacoCursor = {
  line: number;
  cursor: number;
};

export class EditorError extends Error {
  line: number;
  cursor: number;

  constructor(line: number, cursor: number, message: string) {
    super(message);
    this.line = line;
    this.cursor = cursor;
  }
}

export type ImportedDeps = Array<{
  moduleName: string;
  import?: Record<string, string>;
  packageId: {
    name: string;
    subModuleName: string;
    version: string;
  };
}>;
