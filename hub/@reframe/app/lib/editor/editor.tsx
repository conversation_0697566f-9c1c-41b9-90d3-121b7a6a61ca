import React, { useRef, useState } from "npm:react@canary";
import MonacoEditor, { OnMount } from "npm:@monaco-editor/react";
import { Button, Input, Loader, t, tw, X, Y } from "@reframe/ui/main.tsx";
import { PlusIcon } from "@reframe/icons/plus.ts";
import theme from "./tomorrow-night-blue.ts";

// import { setupMonacoEnvironment } from "./setup";
// import { createBroadcastListener } from "./broadcast-listener";
import type { Editor, ImportedDeps, Monaco } from "./common.ts";

const StyledMonacoEditor = tw(MonacoEditor, [
  t`grow`,
  `[&_.selected-node-line]:bg-primary-400/30 [&_.selected-node-line]:w-1 [&_.selected-node-line]:ml-1`,
  `[&_.error-node-line]:bg-red-400/30 [&_.error-node-line]:w-1 [&_.error-node-line]:ml-1`,
]);

// const broadcast = setupMonacoEnvironment();

const NewFileButton = ({
  onCreate,
}: {
  onCreate: (file: string) => boolean;
}) => {
  const [fileName, setFileName] = useState<string | null>(null);
  const [error, setError] = useState(false);

  if (fileName !== null) {
    return (
      <X
        css={[
          t`h-auto px-3 py-1.5 rounded-none text-neutral-600 font-normal text-sm !no-underline`,
          t`bg-neutral-200/50`,
        ]}
      >
        <Input
          bordered={false}
          outline={false}
          autoFocus
          css={["p-0 h-auto", error ? t`text-orange-400` : t``]}
          value={fileName}
          onChange={(e) => {
            setFileName(
              e.target.value
                .replaceAll(/[^a-z0-9\-./]/g, "")
                .replaceAll("//", "/"),
            );
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              if (
                !onCreate(
                  "/" +
                    fileName
                      .split("/")
                      .filter((s) => s.length > 0)
                      .join("/"),
                )
              ) {
                return setError(true);
              }

              setFileName(null);
            }

            if (e.key === "Escape") {
              setFileName(null);
            }
          }}
        />
      </X>
    );
  }

  return (
    <Button
      variant="link"
      css={[
        t`h-auto px-3 py-1.5 rounded-none text-neutral-600 font-normal text-sm !no-underline`,
        t`bg-neutral-200/50`,
      ]}
      onClick={() => {
        setError(false);
        setFileName("");
      }}
    >
      <PlusIcon css="stroke-2" />
    </Button>
  );
};

const CustomEditor = ({
  path,
  setPath,
  files,
  onChange,
  onSave,
}: {
  path: string;
  setPath: (path: string) => void;
  files: Record<
    string,
    {
      language: "typescript" | "javascript" | "json";
      content: string;
    }
  >;
  onChange: (file: string, code: string) => void;
  onSave: (
    file: string,
    compile: () => Promise<{
      code: string;
      deps: ImportedDeps;
    }>,
  ) => void;
}) => {
  const resolverMapRef = useRef<Map<string, (_: ImportedDeps) => void>>(
    new Map(),
  );

  const filesRef = useRef(files);

  const onSaveRef = useRef(onSave);
  onSaveRef.current = onSave;

  const editorRef = useRef<
    {
      editor: Editor;
      monaco: Monaco;
      // channel: BroadcastChannel;
    } | null
  >(null);

  const handleEditorDidMount: OnMount = async (editor, monaco) => {
    // const channel = createBroadcastListener(broadcast, undefined, monaco);
    // channel.addEventListener(
    //   "message",
    //   (event: {
    //     data: {
    //       type: "deps-updated";
    //       file: string;
    //       deps: ImportedDeps;
    //     };
    //   }) => {
    //     if (event.data.type === "deps-updated") {
    //       const { type, file, deps } = event.data;
    //       const resolver = resolverMapRef.current.get(file);

    //       if (resolver) {
    //         resolver(deps);
    //         resolverMapRef.current.delete(file);
    //       }
    //     }
    //   },
    // );

    editorRef.current = {
      editor,
      monaco,
      // channel,
    };

    monaco.editor.defineTheme("tomorrow-night-blue", theme);
    monaco.editor.setTheme("tomorrow-night-blue");

    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      lib: ["dom", "dom.iterable", "esnext"],
      downlevelIteration: true,
      noEmit: true,
      allowJs: true,
      resolveJsonModule: true,
      esModuleInterop: true,
      target: monaco.languages.typescript.ScriptTarget.ESNext,
      module: monaco.languages.typescript.ModuleKind.ESNext,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      strict: true,
      strictNullChecks: true,
      allowNonTsExtensions: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      jsxFactory: "React.createElement",
      jsxFragmentFactory: "React.Fragment",
      jsxImportSource: "npm:react@latest",
    });

    monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false,
      diagnosticCodesToIgnore: [2786],
    });

    monaco.languages.typescript.typescriptDefaults.setWorkerOptions({
      customWorkerPath: ".",
    });

    editor.updateOptions({
      wordWrap: "on",
      automaticLayout: true,
      minimap: {
        enabled: true,
      },
      lineNumbers: "on",
      scrollBeyondLastLine: true,
      scrollbar: {
        vertical: "auto",
        horizontal: "hidden",
      },
      renderLineHighlight: "none",
      overviewRulerBorder: false,
      overviewRulerLanes: 0,
      hideCursorInOverviewRuler: true,
    });

    editor.addAction({
      // An unique identifier of the contributed action.
      id: "save",

      // A label of the action that will be presented to the user.
      label: "Save",

      // An optional array of keybindings for the action.
      keybindings: [monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS],

      contextMenuGroupId: "navigation",

      contextMenuOrder: 1.5,

      // Method that will be executed when the action is triggered.
      // @param editor The editor instance is passed in as a convenience
      run: async () => {
        console.debug("[save] triggered");

        const model = editor.getModel();

        if (!model) {
          console.warn("[save] no model");
          throw new Error("no editor to save");
        }

        const fileName = model.uri.path;
        const content = model.getValue();

        onSaveRef.current(fileName, async () => {
          if (!filesRef.current[fileName]) {
            filesRef.current[fileName] = {
              language: "typescript",
              content: "",
            };
          }

          const file = filesRef.current[fileName]!;
          file.content = content;

          if (file.language !== "typescript") {
            throw new Error("only typescript is supported");
          }

          const promise = new Promise<ImportedDeps>((resolve) => {
            resolverMapRef.current.set(fileName, resolve);
          });

          // channel.postMessage({
          //   type: "update-deps",
          //   file: fileName,
          //   content: `
          //   import "npm:react@latest/jsx-runtime";

          //   ${file.content}
          //   `,
          // });

          console.debug("[save] waiting for deps to be updated");
          const deps = await promise;
          console.log("[save] deps updated", deps);

          const { code } = await compile({
            language: file.language,
            source: file.content,
            editor,
            monaco,
            gotoError: false,
          });

          return { code, deps };
        });
      },
    });
  };

  const file = filesRef.current[path];

  return (
    <Y css="rounded-sm">
      <X css="wrap">
        {
          /* {Object.keys(filesRef.current).map((file) => (
          <Button
            key={file}
            variant="link"
            css={[
              t`h-auto px-3 py-1.5 rounded-none text-neutral-600 font-normal text-sm !no-underline`,
              file === path
                ? t`border-b-2 border-primary-400/50`
                : t`bg-neutral-200/50`,
            ]}
            onClick={() => {
              setPath(file);
            }}
          >
            {file}
          </Button>
        ))} */
        }
        {
          /* <NewFileButton
          onCreate={(fileName) => {
            if (filesRef.current[fileName]) {
              return false;
            }

            filesRef.current[fileName] = {
              language: "typescript",
              content: "// start bulding!",
            };

            setPath(fileName);

            return true;
          }}
        /> */
        }
      </X>

      <StyledMonacoEditor
        path={path}
        defaultLanguage={"typescript"}
        defaultValue={file?.content ?? "// start typing!"}
        language={file?.language ?? "typescript"}
        theme="tomorrow-night-blue"
        onChange={(value) => {
          if (value) {
            if (!filesRef.current[path]) {
              filesRef.current[path] = {
                language: "typescript",
                content: "",
              };
            }

            const file = filesRef.current[path]!;
            file.content = value;
            onChange(path, value);
          }
        }}
        onMount={handleEditorDidMount}
        onValidate={(markers) => {
          markers
            .filter((marker) => marker.severity > 1)
            .forEach((marker) => console.debug("onValidate:", marker));
        }}
        loading={
          <X align="center" justify="center" css="text-slate-400">
            loading ...
          </X>
        }
      />
    </Y>
  );
};

export default CustomEditor;
