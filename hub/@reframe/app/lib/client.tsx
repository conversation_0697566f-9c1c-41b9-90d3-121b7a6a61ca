"use client";

import { useState } from "npm:react@canary";
import {
  Button,
  For,
  ScrollArea,
  Text,
  Textarea,
  X,
  Y,
} from "@reframe/ui/main.tsx";
import { writeSource } from "./server.ts";
import CustomEditor from "./editor/editor.tsx";
import { ChevronRightIcon } from "@reframe/icons/chevron-right.ts";
import { ChevronDownIcon } from "@reframe/icons/chevron-down.ts";
import { FileIcon } from "@reframe/icons/file.ts";
import { FolderIcon } from "@reframe/icons/folder.ts";

export const Counter = () => {
  const [count, setCount] = useState(0);

  return (
    <Button onClick={() => setCount(count + 1)}>
      {count}
    </Button>
  );
};

export type Node = {
  type: "directory";
  children: Record<string, Node>;
} | {
  type: "file";
};

export const Tree = (
  {
    node,
    path,
    active,
  }: {
    node: Node & { type: "directory" };
    path: string[];
    active: string;
  },
) => {
  const currentPath = path.slice(1).join("/");
  const [open, setOpen] = useState(
    path.length < 3 || active.startsWith(currentPath),
  );

  return (
    <Y gap={1} css="px-2">
      <X
        fit-y
        css="px-2 select-none cursor-pointer hover:bg-slate-50/20 rounded-sm"
        align="center"
        onClick={() => setOpen((open) => !open)}
      >
        {open
          ? <ChevronDownIcon width={1.75} />
          : <ChevronRightIcon width={1.75} />}

        <X fit-x fit-y align="center" gap={2} css="px-2">
          <FolderIcon size={14} width={1.75} />
          <Text css="text-base">{path.at(-1)}</Text>
        </X>
      </X>

      {open
        ? (
          <Y
            css="pl-2"
            min-y="min-content"
          >
            <Y
              fit-y
              css="border-l-[0.5px] border-l-slate-50/20 pl-2 overflow-hidden"
              gap={1}
            >
              <For
                each={Object.entries(node.children)}
                render={([name, child]) => (
                  child.type === "file"
                    ? (
                      <X
                        fit-y
                        css={[
                          "px-2 select-none cursor-pointer hover:bg-slate-50/20 rounded-sm",
                        ]}
                        onClick={() => {
                          window.location.href = "/~/editor/" + currentPath +
                            "/" + name;
                        }}
                      >
                        <X
                          fit-x
                          fit-y
                          align="center"
                          gap={2}
                          css={[
                            "px-2",
                            active === currentPath + "/" + name
                              ? "bg-slate-50/20 text-white rounded-md"
                              : "",
                          ]}
                        >
                          <FileIcon size={14} width={1.75} />
                          <Text css="text-base">{name}</Text>
                        </X>
                      </X>
                    )
                    : (
                      <Tree
                        active={active}
                        node={child}
                        path={[...path, name]}
                      />
                    )
                )}
              />
            </Y>
          </Y>
        )
        : null}
    </Y>
  );
};

export const Editor = ({
  path,
  content: _content,
}: {
  path: string;
  content: string;
}) => {
  const [content, setContent] = useState(_content);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const save = async () => {
    setSaving(true);
    try {
      await writeSource(path, content, {});
    } catch (error) {
      setError(error as Error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Y min-y="160px">
      <X fit-y align="center">
        <X
          css={[
            `h-auto px-3 py-4 rounded-none text-slate-200 font-normal text-sm !no-underline`,
          ]}
        >
          <Text>{path}</Text>
        </X>
        <Button
          css={[
            `w-[100px] py-2 text-slate-200 font-normal text-sm !no-underline bg-blue-800 hover:bg-blue-700 rounded-md`,
          ]}
          onClick={save}
        >
          {saving ? "saving..." : "save"}
        </Button>
      </X>

      <CustomEditor
        path={path}
        setPath={(path) => {
          throw new Error("not implemented");
        }}
        files={{
          [path]: {
            language: "typescript",
            content,
          },
        }}
        onChange={(path, code) => {
          setContent(code);
        }}
        onSave={save}
      />
    </Y>
  );
};
