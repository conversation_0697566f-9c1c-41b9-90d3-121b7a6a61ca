import React, { Suspense } from "npm:react@canary";

export const Await = (
    { children }: { children: React.ReactNode | Promise<React.ReactNode> },
) => {
    if (children instanceof Promise) {
        return (
            <Suspense>
                {/* @ts-expect-error Type 'Promise<ReactNode>' is not assignable to type 'ReactNode' */}
                {children}
            </Suspense>
        );
    }

    return children;
};

export const Async = <T,>(
    { value, then, catch: _catch }: {
        value: () => Promise<T>;
        then: (value: T) => React.ReactNode | Promise<React.ReactNode>;
        catch?: (error: unknown) => React.ReactNode | Promise<React.ReactNode>;
    },
) => <Await>{value().then(then, _catch)}</Await>;
