import React from "npm:react@canary";
import { Await } from "./async.tsx";
import { createContext } from "@reframe/react/server-context.tsx";
import { Outlet, Render } from "./outlet/client.tsx";
import { MaybePromise, Thenable } from "@reframe/zero/defs.ts";
import { defaultRenderer } from "@reframe/react/router/render.tsx";

type Empty = Record<string, unknown>;

type RouteInstance<Prev extends Empty, Ctx extends Empty> = {
  ui: {
    render?: (
      element: React.ReactNode,
      request: Request,
      prev: (element: React.ReactNode, request: Request) => Thenable<Response>,
    ) => Thenable<Response>;
    context?: (prev: Prev) => Thenable<Ctx>;
    page?: () => React.ReactNode;
    layout?: () => React.ReactNode;
  };

  http: {
    request?: (
      request: Request,
      context: () => Thenable<Prev & Ctx>,
    ) => Thenable<Response>;
    // todo: make it (request, ctx: { next, content, path })
    middleware: (
      request: Request,
      next: (request: Request) => Thenable<Response>,
      context: () => Thenable<Prev & Ctx>,
    ) => Thenable<Response>;
  };

  staticRoutes: Record<string, () => RouteInstance<Prev, Ctx>>;
  dynamicRoute?: (
    segment: string,
    ...rest: string[]
  ) => RouteInstance<Prev, Ctx>;
};

export type Middleware<Ctx extends Empty> = (
  request: Request,
  next: (request: Request) => Thenable<Response>,
  context: () => Thenable<Ctx>,
) => MaybePromise<Response>;

export type RouteProps<Prev extends Empty, Ctx extends Empty> =
  & {
    render?: (element: React.ReactNode, request: Request) => Thenable<Response>;
    context?: (prev: Prev) => Thenable<Ctx>;

    layout?: (
      Router: Pick<
        RouterInstance<Prev & Ctx>,
        "Context" | "Outlet" | "path" | "segments" | "search"
      >,
    ) => React.ReactElement;
    page?: (
      Router: Pick<
        RouterInstance<Prev & Ctx>,
        "Context" | "path" | "segments" | "search"
      >,
    ) => React.ReactElement;

    route?: (segment: string, ...rest: string[]) => RouteSegment<Prev & Ctx>;
    [route: `route:${string}`]: RouteSegment<Prev & Ctx>;

    middleware?: Middleware<Prev & Ctx> | Middleware<Prev & Ctx>[];
  }
  & Partial<
    Record<
      | "serve"
      | "serve:get"
      | "serve:post"
      | "serve:put"
      | "serve:delete"
      | "serve:patch"
      | "serve:options"
      | "serve:head",
      (
        request: Request,
        context: () => Thenable<Prev & Ctx>,
      ) => MaybePromise<Response>
    >
  >;

function Route<Prev extends Empty, Ctx extends Empty>(
  _: RouteProps<Prev, Ctx>,
): React.ReactElement {
  throw new Error("not implemented");
}

const Context = createContext<unknown>("router");

const thenable = <T,>(value: MaybePromise<T>): Thenable<T> =>
  value instanceof Promise ? value : ({
    then: (onFullfilled) => onFullfilled(value),
  });

const combineMiddlewares = <Ctx extends Empty>(
  middleware: Middleware<Ctx> | Middleware<Ctx>[],
): Middleware<Ctx> => {
  if (Array.isArray(middleware)) {
    return middleware.reduceRight(
      (inner, outer) => (request, next, context) =>
        outer(
          request,
          async (request) => inner(request, next, context),
          context,
        ),
      async (request, next) => next(request),
    );
  }

  return middleware;
};

const createRouteInstance = <Prev extends Empty, Ctx extends Empty>(
  element: React.ReactElement<RouteProps<Prev, Ctx>>,
  base: `/${string}`,
  segments: string[],
  search: Record<string, string | string[]> = {},
): RouteInstance<Prev, Ctx> => {
  if (element.type !== Route) {
    throw new Error(`expected Router.Route, got ${element.type}`);
  }

  const router = createRouter<Prev & Ctx>({ path: base, segments, search });

  const page = element.props.page
    ? (() => element.props.page!(router))
    : undefined;

  const layout = element.props.layout
    ? (() => element.props.layout!(router))
    : undefined;

  const context = element.props.context;

  const render = element.props.render;
  const middleware = element.props.middleware;

  const staticRoutes: Record<string, () => RouteInstance<Prev, Ctx>> = {};

  for (const path in element.props) {
    if (path.startsWith("route:")) {
      const segment = path.slice("route:".length);
      const route = element.props[path as `route:${string}`];

      staticRoutes[segment] = () =>
        createRouteInstance(
          route?.({ ...router, Self: route }),
          base === "/" ? `/${segment}` : `${base}/${segment}`,
          segments.slice(1),
          search,
        );
    }
  }

  const shouldServe = Object.keys(element.props)
    .some((key) => key === "serve" || key.startsWith("serve:"));

  return {
    // element,
    ui: {
      context,
      layout,
      page,
      render,
    },
    http: {
      request: !shouldServe ? undefined : (request, context) => {
        const method = request.method.toLowerCase() as
          | "get"
          | "post"
          | "put"
          | "delete"
          | "patch"
          | "options"
          | "head";

        const serve = element.props[`serve:${method}`] ??
          element.props.serve;

        if (serve) {
          return thenable(serve(request, context));
        }

        throw new Error(
          `${method.toUpperCase()} ${request.url} not implemented`,
        );
      },
      middleware: middleware
        ? (request, next, context) =>
          thenable(combineMiddlewares(middleware)(request, next, context))
        : ((request, next) => next(request)),
    },
    staticRoutes,
    dynamicRoute: element.props.route
      ? (segment, ...rest) => {
        const route = element.props.route!(segment, ...rest);
        return createRouteInstance(
          route({ ...router, Self: route }),
          base === "/" ? `/${segment}` : `${base}/${segment}`,
          segments.slice(1),
          search,
        );
      }
      : undefined,
  };
};

type RouterContext<T extends Empty> = {
  use: () => Thenable<T>;
  extend: <U extends Empty>(
    _: (prev: T) => Thenable<U>,
  ) => RouterContext<T & U>;
};

const createRouterContext = <Ctx extends Empty>(
  create: () => MaybePromise<Ctx>,
): RouterContext<Ctx> => {
  const value = {
    status: "pending",
  } as {
    status: "pending";
  } | {
    status: "fulfilled";
    current: MaybePromise<Ctx>;
  };

  const use = () => {
    if (value.status === "fulfilled") {
      return thenable(value.current);
    }

    const current = create();

    Reflect.set(value, "status", "fulfilled");
    Reflect.set(value, "current", current);

    return thenable(current);
  };

  const extend = <U extends Empty>(extend: (prev: Ctx) => Thenable<U>) =>
    createRouterContext(
      async () => {
        const prev = await use();
        const current = await extend(prev);
        return { ...prev, ...current };
      },
    );

  return {
    use,
    extend,
  };
};

export const isAPIRequest = (request: Request) =>
  request.headers.get("authorization") !== null ||
  (request.method.toLowerCase() !== "get" &&
    request.method.toLowerCase() !== "head");

const RError = ({ error }: { error: unknown }) => {
  return (
    <pre>{error instanceof Error
        ? error.stack
        : String(error)
        }</pre>
  );
};

function createFetch<Prev extends Empty, Ctx extends Empty>(
  route: RouteInstance<Prev, Ctx>,
) {
  return (
    request: Request,
    ctx: {
      segments: string[];
      elements: React.ReactNode[];
      context: RouterContext<Prev>;
      render: (
        element: React.ReactNode,
        request: Request,
      ) => Thenable<Response>;
    },
  ) => {
    let [segment, ...rest] = ctx.segments;

    const context = route.ui.context
      ? ctx.context.extend(route.ui.context)
      : (ctx.context as RouterContext<Prev & Ctx>);

    return route.http.middleware(
      request,
      (request): Thenable<Response> => {
        const Provider = (
          { children }: { children: React.ReactNode },
        ) => (
          <Await>
            {context.use()
              .then((value) => (
                <Context.Provider value={value}>
                  {children}
                </Context.Provider>
              ), (err) => <RError error={err} />)}
          </Await>
        );

        const render = !route.ui.render
          ? ctx.render
          : (element: React.ReactNode, request: Request) =>
            route.ui.render!(element, request, ctx.render);

        const elements = [];

        if (segment === ":~") {
          segment = rest[0];
          rest = rest.slice(1);
        } else {
          elements.push(...ctx.elements);

          if (route.ui.layout) {
            elements.push(<Provider>{route.ui.layout()}</Provider>);
          }
        }

        const next = segment === undefined
          ? undefined
          : segment in route.staticRoutes
          ? route.staticRoutes[segment]()
          : route.dynamicRoute?.(segment, ...rest);

        if (next) {
          return createFetch(next)(request, {
            segments: rest,
            elements,
            context,
            render,
          });
        }

        const shouldServe = segment === undefined &&
          (isAPIRequest(request) ||
            (route.ui.page === undefined &&
              route.http.request !== undefined));

        if (shouldServe) {
          if (route.http.request) {
            return route.http.request(request, context.use);
          }

          if (isAPIRequest(request)) {
            throw new Error(`NOT FOUND: ${request.url}`);
          }
        }

        return render(
          <Render
            layers={[
              ...elements,
              segment === undefined && route.ui.page
                ? <Provider>{route.ui.page()}</Provider>
                : null,
            ]}
          />,
          request,
        );
      },
      () => context.use(),
    );
  };
}

function createRouter<Ctx extends Empty>(
  { path, segments, search }: { path: `/${string}`; segments: string[]; search: Record<string, string | string[]> },
) {
  return {
    path,
    Route,
    Outlet,
    Context,
    segments,
    search,
  } as Omit<RouterInstance<Ctx>, "Self">;
}

export function createRoute<
  U extends { request: Request } = { request: Request },
>(
  Route: <Ctx extends U>(
    Router: Pick<RouterInstance<Ctx>, "Route" | "Self">,
  ) => React.ReactElement,
) {
  function createServer() {
    const serve = async (request: Request) => {
      const segmentPath = (pathname: string) =>
        pathname
          .split("/")
          .filter(Boolean);

      const url = new URL(request.url);
      const segments = segmentPath(url.pathname);
      
      // Parse search parameters
      const search: Record<string, string | string[]> = {};
      for (const [key, value] of url.searchParams.entries()) {
        if (search[key]) {
          // If key already exists, convert to array or add to existing array
          if (Array.isArray(search[key])) {
            (search[key] as string[]).push(value);
          } else {
            search[key] = [search[key] as string, value];
          }
        } else {
          search[key] = value;
        }
      }

      const router = createRouteInstance<Empty, { request: Request }>(
        Route({
          ...createRouter<U>({ path: "/", segments, search }),
          Self: Route,
        }),
        "/",
        segments,
        search,
      );

      const fetch = createFetch(router);

      return await fetch(request, {
        segments,
        elements: [],
        context: createRouterContext(() => ({ request, serve })),
        render: defaultRenderer,
      });
    };

    return serve;
  }

  return Object.assign(Route, { createServer });
}

export type RouteSegment<Ctx extends Empty> = (
  Router: Pick<RouterInstance<Ctx>, "Route" | "Self">,
) => React.ReactElement;

export type RouterInstance<Ctx extends Empty> = {
  path: `/${string}`;
  segments: string[];
  search: Record<string, string | string[]>;
  Route: <Next extends Empty>(
    props: RouteProps<Ctx, Next>,
  ) => React.ReactElement;
  Outlet: typeof Outlet;
  Context: ReturnType<typeof createContext<Ctx>>;
  Self: RouteSegment<Ctx>;
};
