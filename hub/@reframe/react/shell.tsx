import React, { Suspense } from "npm:react@canary";

type Script =
    & { kind: "script"; attributes?: Record<string, string> }
    & ({ src: string; onLoad?: string } | { content: string });

type Style = {
    kind: "style";
    content: string;
    attributes?: Record<string, string>;
};

export type Asset = Script | Style;

export const Shell = ({
    assets = [],
    children,
}: {
    assets?: Asset[];
    children?: React.ReactNode;
}) => {
    return (
        <html>
            <head>
                <meta charSet="utf-8" />
                <meta
                    name="viewport"
                    content="width=device-width, initial-scale=1, maximum-scale=1"
                />
                {assets.map((asset) =>
                    asset.kind === "script"
                        ? (
                            "src" in asset
                                ? (
                                    <>
                                        <script
                                            src={asset.src}
                                            {...asset.attributes}
                                        />
                                        {"onLoad" in asset && asset.onLoad
                                            ? <script>{asset.onLoad}</script>
                                            : null}
                                    </>
                                )
                                : (
                                    <script {...asset.attributes}>
                                        {asset.content}
                                    </script>
                                )
                        )
                        : <style {...asset.attributes}>{asset.content}</style>
                )}
            </head>
            <body>
                <Suspense>{children}</Suspense>
            </body>
        </html>
    );
};
