import Reframe from "@";
import React, { Suspense } from "npm:react@canary";
import { renderToReadableStream } from "npm:react-dom@canary/server.edge" with {
  directive: "client",
};
import { Path } from "@reframe/zero/defs.ts";

import {
  decodeReply,
  renderToReadableStream as createRscStream,
} from "npm:react-server-dom-webpack@canary/server.edge";

import { createFromReadableStream } from "npm:react-server-dom-webpack@canary/client.edge";
import { Asset } from "@reframe/react/shell.tsx";

export const $hack_dynamic = () => {
  import("/:/@reframe/react/bootstrap/bootstrap.js?directive=client");
  import("@reframe/zero/create/runtime.ts?directive=client");
  import("./bootstrap/initialize.ts?directive=client");
};

const Stream = async <P,>({ reader, render }: {
  reader: ReadableStreamDefaultReader<P>;
  render: (value: P) => React.ReactElement;
}) => {
  const { done, value } = await reader.read();
  if (done) {
    return null;
  }

  return (
    <>
      {render(value)}
      <Suspense>
        <Stream reader={reader} render={render} />
      </Suspense>
    </>
  );
};

const serverMap = new Proxy({}, {
  get(target, property) {
    if (typeof property === "string" && property.includes("#")) {
      const [module, name] = property.split("#");

      return {
        name,
        id: module,
        chunks: [module],
      };
    }

    return undefined;
  },
});

const toByteStream = (streamPromise: Promise<ReadableStream<Uint8Array>>) => {
  return new ReadableStream({
    type: "bytes",
    async start(controller) {
      try {
        const stream = await streamPromise;

        const reader = stream.getReader();

        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            break;
          }
          try {
            controller.enqueue(value);
          } catch (error) {
            console.log("[error]", error, new TextDecoder().decode(value));
            throw error;
          }
        }

        controller.close();
      } catch (error) {
        console.log("[error]", error);
        controller.error(error);
      }
    },
  }, { highWaterMark: 0 });
};

export const toStream = (value: unknown, options?: {
  onError: (error: unknown) => void;
}): ReadableStream<Uint8Array> => createRscStream(value, serverMap, options);

export const reply = async (request: Request) => {
  try {
    const serverAction = request.headers.get("x-react-server-action");
    if (!serverAction) {
      return new Response("not found", { status: 405 });
    }

    const isPlainText = request.headers.get("content-type")?.startsWith(
      "text/plain",
    );

    const args = await decodeReply(
      !isPlainText ? await request.formData() : await request.text(),
      serverMap,
    );
    const [path, name] = serverAction.split("#");

    // TODO(security): serverAction should be encrypted
    const module = await Reframe.import<{
      [key: string]: (...args: unknown[]) => unknown;
    }>(`/:${path}`, { directive: "server" });

    const segments = name.split(".");
    const fn = segments.reduce((obj, segment, index) => {
      if ((typeof obj === "object" || typeof obj === "function")) {
        return Reflect.get(obj, segment);
      }

      throw new Error(
        `Cannot find function ${
          segments.slice(0, index + 1).join(".")
        } at ${path}`,
      );
    }, module as object);

    if (typeof fn !== "function") {
      throw new Error(`${name} of ${path} is not a function`);
    }

    const result = await fn(...args);

    // todo: handle error, have a special error, eg: SafeError, that will be sent to the client
    // since we also control the client, we can chose to show the error or not

    return new Response(toStream(result), {
      headers: {
        "content-type": "text/x-component",
      },
    });
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const render = (element: React.ReactElement) => {
  return new ReadableStream({
    type: "bytes",
    async start(controller) {
      try {
        const onError = (error: Error) => {
          // TODO: put it to another stream and then send that stream to the client
          console.error("render-error", error);
        };

        const stream = await renderToReadableStream(element, { onError });

        const reader = stream.getReader();

        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            break;
          }

          try {
            controller.enqueue(value);
          } catch (error) {
            console.log("[error]", error, new TextDecoder().decode(value));
            throw error;
          }
        }

        controller.close();
      } catch (error) {
        console.log("[error]", error);
        controller.error(error);
      }
    },
  }, { highWaterMark: 0 });
};

globalThis.__webpack_chunk_load__ = (module: string) => {
  console.log("[__webpack_chunk_load__]", module);

  return Reframe
    .extend(() => ({ path: "/" }))
    .import(module, { directive: "client" });
};

globalThis.__webpack_require__ = (module: string) => {
  console.log("[__webpack_require__]", module);

  return Reframe
    .extend(() => ({ path: "/" }))
    .import(module, { directive: "client" });
};

export const Render = (
  {
    children,
    root = "reframe-root",
  }: React.PropsWithChildren<{
    root?: string;
  }>,
): React.ReactElement => {
  // todo: add `signal` to the options
  const r = stream(children, { root });

  const [r1, r2] = r.tee();

  const el = createFromReadableStream(
    r1.pipeThrough(
      new TransformStream({
        transform(chunk, controller) {
          if (chunk.type === "rsc") {
            controller.enqueue(chunk.data.content);
          }
        },
      }),
    )
      .pipeThrough(new TextEncoderStream()),
    {
      serverConsumerManifest: {},
    },
  );

  return (
    <Stream
      reader={r2.getReader()}
      render={(chunk) => {
        if (chunk.type === "asset" || chunk.type === "close") {
          return <></>;
        }

        if (chunk.type === "bootstrap") {
          return (
            <>
              <script
                data-reframe-entry={chunk.data.entry}
                data-reframe-root={chunk.data.root}
                data-widget={chunk.data.widget}
              >
                {chunk.data.script}
              </script>
              <div id={root}>{el}</div>
            </>
          );
        }

        if (chunk.type === "chunks") {
          return (
            <script data-path={chunk.data.path}>
              {`self.__reframe.source.register(${
                JSON.stringify(chunk.data.hashes, null, 2)
              })`}
            </script>
          );
        }

        if (chunk.type === "rsc") {
          return (
            <script data-length={chunk.data.length}>
              {`self.__reframe.rsc.controller.enqueue(${
                JSON.stringify(chunk.data.content).replace(/</g, "\\u003c")
              });`}
            </script>
          );
        }

        throw new Error(`Unknown chunk type: ${Reflect.get(chunk, "type")}`);
      }}
    />
  );
};

export type ReactServerStreamPayload =
  | {
    type: "bootstrap";
    data: {
      entry: string;
      script: string;
      root: string;
      widget: boolean;
    };
  }
  | {
    type: "asset";
    data: Asset;
  }
  | {
    type: "rsc";
    data: {
      length: string;
      content: string;
    };
  }
  | {
    type: "chunks";
    data: {
      path: Path;
      hashes: Record<string, string | null>;
    };
  }
  | {
    type: "close";
    data: null;
  };

export const stream = (
  element: React.ReactNode,
  { assets, widget = false, root }: {
    assets?: Asset[];
    widget?: boolean;
    root: string;
  },
) => {
  return new ReadableStream<ReactServerStreamPayload>({
    async start(controller) {
      const stream = toStream(element, {
        onError: (error: unknown) => {
          console.error("[serialize]", error);
          controller.error(error);
        },
      });

      const reader = stream.getReader();
      const decoder = new TextDecoder();
      const included = new Set<string>();
      const includedAssests = new Set<string>();

      const enqueueChunks = async (path: Path) => {
        const resolvedPath = Reframe.resolve(path, "/?directive=client");
        const hashes = await Reframe.sourceGraph.getAllHashes(resolvedPath);
        const newHashes: Record<string, string | null> = {};

        for (const [path, hash] of hashes.entries()) {
          if (!includedAssests.has(path)) {
            newHashes[path] = hash;
            includedAssests.add(path);
          }
        }

        controller.enqueue({
          type: "chunks",
          data: {
            path: resolvedPath,
            hashes: newHashes,
          },
        });
      };

      for (const asset of assets ?? []) {
        controller.enqueue({ type: "asset", data: asset });
      }

      const bootstrap = await Reframe.sourceGraph
        .read("/@reframe/react/bootstrap/bootstrap.js", {
          directive: "client",
        });

      controller.enqueue({
        type: "bootstrap",
        data: {
          entry: Reframe.entry.fullPath.slice(1),
          root,
          widget,
          script: await bootstrap.text(),
        },
      });

      await enqueueChunks("/~@/@reframe/zero/create/runtime.ts");
      await enqueueChunks("/~@/@reframe/react/bootstrap/initialize.ts");

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }

        const content = decoder.decode(value);

        const pattern =
          /(^|\n)[a-zA-Z0-9]+:I\["([^"]+)",(\[[^\]]*\]),"([^"]+)",1\]/g;

        const matches = Array.from(content.matchAll(pattern))
          .map((match) => match[2])
          .filter((value) => !included.has(value));

        for (const path of matches) {
          // todo: experiment if it's more optimal to enqueue chunks without awaiting
          if (!included.has(path)) {
            await enqueueChunks(path as Path);
          }

          included.add(path);
        }

        controller.enqueue({
          type: "rsc",
          data: {
            length: Number(value.length).toString(16),
            content,
          },
        });
      }

      controller.enqueue({ type: "close", data: null });
      controller.close();
    },
  });
};
