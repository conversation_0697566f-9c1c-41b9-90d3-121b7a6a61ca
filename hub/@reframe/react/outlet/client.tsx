"use client";

import React, {
  createContext as createClientContext,
  Suspense,
  useContext,
  useState,
} from "npm:react@canary";

export const OutletContext = createClientContext<React.ReactNode | null>(
  null,
);

const RouterContext = createClientContext<
  {
    layers: React.ReactNode[];
    setLayers: React.Dispatch<React.SetStateAction<React.ReactNode[]>>;
  }
>({
  layers: [],
  setLayers: () => {
    throw new Error("RouterContext not initialized");
  },
});

export const Outlet = () => (
  <Suspense>
    {useContext(OutletContext)}
  </Suspense>
);

const Combine = (
  { layers: [root, ...rest] }: { layers: React.ReactNode[] },
): React.ReactNode => {
  if (rest.length === 0) {
    return root;
  }

  return (
    <OutletContext.Provider
      value={<Combine layers={rest} />}
      children={root}
    />
  );
};

const RenderLayers = () => {
  const { layers } = useContext(RouterContext);
  return <Combine layers={layers} />;
};

export const Render = (
  { layers: initialLayers }: { layers: React.ReactNode[] },
): React.ReactNode => {
  const [layers, setLayers] = useState(initialLayers);

  return (
    <RouterContext.Provider value={{ layers, setLayers }}>
      <RenderLayers />
    </RouterContext.Provider>
  );
};

export const useRouter = () => {
  const { setLayers } = useContext(RouterContext);
  const push = (path: `/${string}`) => {
    setLayers((layers) => {
      const newLayers = [...layers];
      newLayers.pop();
      newLayers.pop();
      newLayers.push(<div>hello!</div>);

      return newLayers;
    });
  };
  return { push };
};
