(() => {
  console.warn("[time]", performance.now());
  // hey, this is a comment
  const currentScript = document.currentScript;
  const entry = currentScript.dataset.reframeEntry;
  const root = currentScript.dataset.reframeRoot;
  const widget = currentScript.dataset.widget === "true";
  const origin = currentScript.dataset.origin ?? "";

  self.__reframe = {
    origin,
    rsc: {},
    source: {},
    moduleCache: new Map(),
  };

  self.__webpack_require__ = (specifier) => {
    throw new Error(`not implemented: __webpack_require__(${specifier})`);
  };

  self.__reframe.rsc.stream = new ReadableStream({
    start(controller) {
      self.__reframe.rsc.controller = controller;
    },
  });

  const resolvers = new Map();
  const cache = new Map();

  const getSources = async (hashes) => {
    const sources = {};
    const pending = [];

    const cache = await self.caches.open("reframe-blob");

    for (const hash of hashes) {
      const response = await cache.match(`/${hash}`);
      if (response) {
        sources[hash] = await response.text();
      } else {
        pending.push(hash);
      }
    }

    if (pending.length > 0) {
      const response = await fetch(`${self.__reframe.origin}/~/blob`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(pending),
      });

      const fetchedSources = await response.json();

      for (const [hash, source] of Object.entries(fetchedSources)) {
        await cache.put(new Request(`/${hash}`), new Response(source));
        sources[hash] = source;
      }
    }

    return new Map(Object.entries(sources));
  };

  self.__reframe.source.cache = cache;
  self.__reframe.source.resolvers = resolvers;

  self.__reframe.source.resolve = (path, promise) => {
    if (!resolvers.has(path)) {
      cache.set(
        path,
        typeof promise === "string" ? Promise.resolve(promise) : promise,
      );
    } else {
      console.log("[register]", 2, path);
      const resolve = resolvers.get(path);
      resolve(promise);
    }
  };

  self.__reframe.source.register = (chunks) => {
    const hashes = Object.values(chunks).filter((hash) => hash !== null);
    const sourcesPromise = getSources(hashes);

    for (const [path, hash] of Object.entries(chunks)) {
      self.__reframe.source.resolve(
        path,
        sourcesPromise.then((sources) => sources.get(hash)),
      );
    }
  };

  self.__reframe.source.get = (path) => {
    if (!cache.has(path)) {
      console.log("[get]", 1, path);
      cache.set(
        path,
        new Promise((resolve) => resolvers.set(path, resolve)),
      );
    }

    return cache.get(path);
  };

  console.log("[__reframe.rsc.stream]", self.__reframe.rsc);

  const createMinimalRuntime = async () => {
    const status = {};
    self.__status = status;
    const importFromDom = (specifier, runtime) => {
      if (self.__reframe.moduleCache.has(specifier)) {
        return self.__reframe.moduleCache.get(specifier);
      }

      status[specifier] = "start";
      self.__reframe.moduleCache.set(
        specifier,
        self.__reframe.source.get(specifier)
          .then(async (code) => {
            status[specifier] = "code";
            const url = URL.createObjectURL(
              new Blob([code], { type: "application/javascript" }),
            );
            status[specifier] = "url";
            const unmodule = await import(url);
            status[specifier] = "unmodule";
            const module = await unmodule.default(runtime);
            status[specifier] = "module";
            URL.revokeObjectURL(url);
            status[specifier] = "end";
            module.__esModule = true;
            return module;
          }),
      );

      return self.__reframe.moduleCache.get(specifier);
    };

    const { resolvePath } = await importFromDom(
      "/~@/@reframe/zero/utils/path.ts?directive=client",
    );

    const resolve = (specifier, referrer) => {
      return resolvePath(specifier, referrer);
    };

    const Runtime = (entry) => ({
      import: (specifier) => {
        if (specifier.startsWith("node:")) {
          return Promise.resolve({});
        }

        if (specifier === "@") {
          return Promise.resolve({ default: Runtime(entry) });
        }

        return importFromDom(
          resolve(specifier, entry),
          Runtime(resolve(specifier, entry)),
        );
      },

      importMany: async (...imports) => {
        const specifiers = Array.from(new Set(imports));

        const modules = await Promise.all(
          specifiers.map(
            async (
              specifier,
            ) => [specifier, await Runtime(entry).import(specifier)],
          ),
        );

        return Object.fromEntries(modules);
      },
    });

    return Runtime;
  };

  const setup = async () => {
    const MinimalRuntime = await createMinimalRuntime();

    console.warn("[time]", 1, performance.now());
    const { createRuntime, createModuleReference } = await MinimalRuntime("/")
      .import(
        "/~@/@reframe/zero/create/runtime.ts?directive=client",
      );

    console.warn("[time]", entry, performance.now());
    const runtime = createRuntime([entry], () => ({
      read: (path, headers) => {
        throw new Error(
          `compute not implemented: read ${path} ${JSON.stringify(headers)}`,
        );
      },
      write: (path, headers) => {
        throw new Error(
          `compute not implemented: write ${path} ${JSON.stringify(headers)}`,
        );
      },
    }), () => ({
      read: (path, headers) => {
        console.warn("[blob/read]", path, headers);
        throw new Error(
          `blob not implemented: read ${path} ${JSON.stringify(headers)}`,
        );
      },
      write: (path, headers) => {
        throw new Error(
          `blob not implemented: write ${path} ${JSON.stringify(headers)}`,
        );
      },
    })).withRequire(async (specifier) => {
      self.__status[specifier] = "start";
      const code = await self.__reframe.source.get(
        specifier + "?directive=client",
      );

      if (!code) {
        return {
          default: createModuleReference(specifier, "server"),
        };
      }

      self.__status[specifier] = "code";
      const url = URL.createObjectURL(
        new Blob([code], { type: "application/javascript" }),
      );

      self.__status[specifier] = "url";
      const runnable = await import(url);
      URL.revokeObjectURL(url);
      self.__status[specifier] = "end";
      return runnable;
    });

    const { mount } = await runtime
      .import("/~@/@reframe/react/bootstrap/initialize.ts", {
        directive: "client",
      });

    let target = document.getElementById(root);

    if (widget) {
      target = target.shadowRoot;
    }

    await mount(target);
  };

  setup();
})();
