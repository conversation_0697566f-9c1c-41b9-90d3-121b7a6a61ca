import runtime from "@";
import type { Path } from "@reframe/zero/defs.ts";

import { hydrateRoot } from "npm:react-dom@canary/client";
import { createFromReadableStream } from "npm:react-server-dom-webpack@canary/client.edge";

declare global {
  var __reframe: {
    origin: string;
    rsc: {
      stream: ReadableStream<string>;
      controller: ReadableStreamDefaultController<string>;
    };
    moduleCache: Map<
      string,
      Promise<unknown> & {
        status: "fulfilled" | "rejected";
        value: unknown;
      }
    >;
    source: {
      register: (chunks: Record<string, string | null>) => void;
      get: (path: Path) => Promise<string>;
    };
  };

  var __webpack_chunk_load__: (module: string) => Promise<unknown> & {
    status: "fulfilled" | "rejected";
    value: unknown;
  };

  var __webpack_require__: (module: string) => unknown;
}

self.__webpack_chunk_load__ = (module: string) => {
  console.log("[__webpack_chunk_load__]", module);

  if (self.__reframe.moduleCache.has(module)) {
    return self.__reframe.moduleCache.get(module)!;
  }

  const promise = runtime.import(`/:${module}`, {
    directive: "client",
  }) as Promise<unknown> & {
    status: "fulfilled" | "rejected";
    value: unknown;
  };

  self.__reframe.moduleCache.set(
    module,
    promise.then(
      (value) => {
        promise.status = "fulfilled";
        promise.value = value;

        return promise;
      },
      (error) => {
        promise.status = "rejected";
        promise.value = error;

        return promise;
      },
    ) as typeof promise,
  );

  return self.__reframe.moduleCache.get(module)!;
};

self.__webpack_require__ = (module: string) => {
  console.log("[__webpack_require__]", module);

  return self.__reframe.moduleCache.get(module)!;
};

export const mount = async (root: HTMLElement) => {
  self.__DEV__ = true;
  self.process = { env: { NODE_ENV: "development" } };

  console.warn("[hydrate] create stream");

  const element = await createFromReadableStream(
    self.__reframe.rsc.stream
      .pipeThrough(new TextEncoderStream()),
    { serverConsumerManifest: {} },
  );

  console.warn("[hydrate] start", performance.now());

  await hydrateRoot(root, element, {
    onCaughtError: (...args) =>
      console.log({
        caughtError: args,
      }),
    onUncaughtError: (...args) =>
      console.log({
        uncaughtError: args,
      }),
    onRecoverableError: (error) => {
      console.warn("Logged recoverable error: " + error.message);
      console.log(error);
    },
  });

  console.log("[hydrate] done", performance.now());
};
