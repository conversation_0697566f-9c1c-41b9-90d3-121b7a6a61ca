import { Suspense } from "npm:react@canary";
import { Shell } from "@reframe/react/shell.tsx";
import { Render, render, reply } from "@reframe/react/server.tsx";
import { assets as tailwindAssets } from "@reframe/tailwind/assets.ts";

export const defaultRenderer = async (
  element: React.ReactNode,
  request: Request,
) => {
  if (request.headers.get("x-react-server-action")) {
    return reply(request);
  }

  const assets = await tailwindAssets();

  return new Response(
    render(
      <Shell assets={assets}>
        <Render>
          <Suspense>
            {element}
          </Suspense>
        </Render>
      </Shell>,
    ),
    { headers: { "content-type": "text/html" } },
  );
};
