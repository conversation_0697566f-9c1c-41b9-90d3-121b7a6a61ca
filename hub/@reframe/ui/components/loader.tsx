import { t, tw } from "../tw.tsx";

import { X } from "./layout.tsx";

export const Loader = tw(
  X,
  t`my-auto items-center grow-0 self-center [&>*]:bg-neutral-600 aspect-[3/1]`,
  {
    size: {
      default: t`h-4 max-w-[4.5rem]`,
      xs: t`h-2 max-w-[1.5rem]`,
      sm: t`h-3 max-w-[3rem]`,
      md: t`h-4 max-w-[4.5rem]`,
      lg: t`h-6 max-w-[6rem]`,
      xl: t`h-8 max-w-[7.5rem]`,
    },
    color: {
      primary: t`[&>*]:bg-primary-400/80`,
      neutral: t`[&>*]:bg-neutral-400/80`,
    },
  },
)
  .defaults({
    size: "default",
    color: "neutral",
  })
  .override(() => ({
    children: (
      <>
        <X
          css="
            min-w-0
            aspect-square
            basis-4/12
            rounded-full
            shadow-lg
            animate-bounce
            [animation-duration:800ms]
            [animation-timing-function:cubic-bezier(0.5,0,0.5,1)]
          "
        />
        <X
          css="
            min-w-0
            aspect-square
            basis-4/12
            rounded-full
            shadow-lg
            animate-bounce
            [animation-delay:200ms]
            [animation-duration:800ms]
            [animation-timing-function:cubic-bezier(0.5,0,0.5,1)]
          "
        />
        <X
          css="
            min-w-0
            aspect-square
            basis-4/12
            rounded-full
            shadow-lg
            animate-bounce
            [animation-delay:400ms]
            [animation-duration:800ms]
            [animation-timing-function:cubic-bezier(0.5,0,0.5,1)]
          "
        />
      </>
    ),
  }));
  