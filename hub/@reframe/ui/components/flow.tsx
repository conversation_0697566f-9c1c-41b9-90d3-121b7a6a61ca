const Item = <T,>(
  { item, index, render }: {
    item: T;
    index: number;
    render: (item: T, index: number) => React.ReactNode;
  },
) => <>{render(item, index)}</>;

export const For = <T,>(
  { each, render, withKey }: {
    each: T[];
    render: (item: T, index: number) => React.ReactNode;
    withKey?: (item: T, index: number) => string | number;
  },
) => {
  return (
    <>
      {each.map((item, index) => (
        <Item
          key={withKey?.(item, index) ?? index}
          item={item}
          index={index}
          render={render}
        />
      ))}
    </>
  );
};

export const If = ({
  condition,
  then,
  otherwise,
}: {
  condition: boolean;
  then: React.ReactNode;
  otherwise?: React.ReactNode;
}) => condition ? then : otherwise;

export const Show = ({
  when,
  then: Then,
  else: Else,
}: {
  when: boolean;
  then: () => React.ReactNode;
  else?: () => React.ReactNode;
}) => when ? <Then /> : Else ? <Else /> : null;
