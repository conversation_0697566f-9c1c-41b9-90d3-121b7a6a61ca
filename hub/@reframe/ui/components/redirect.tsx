"use client";

import React, { useEffect, useState } from "npm:react@canary";
import { X } from "@reframe/ui/components/layout.tsx";
import { Show } from "./flow.tsx";

// import {
//   RedirectType,
//   getRedirectTypeFromError,
//   getURLFromRedirectError,
//   isRedirectError,
// } from './redirect'

// function HandleRedirect({
//   redirect,
//   reset,
//   redirectType,
// }: {
//   redirect: string;
//   redirectType: RedirectType;
//   reset: () => void;
// }) {
//   const router = useRouter();

//   useEffect(() => {
//     React.startTransition(() => {
//       if (redirectType === RedirectType.push) {
//         router.push(redirect, {});
//       } else {
//         router.replace(redirect, {});
//       }
//       reset();
//     });
//   }, [redirect, redirectType, reset, router]);

//   return null;
// }

// const RedirectErrorBoundary1 = createErrorBoundary((error: unknown) => {
//   if (isRedirectError(error)) {
//     const url = getURLFromRedirectError(error);
//     const redirectType = getRedirectTypeFromError(error);
//     return { redirect: url, redirectType };
//   }
//   // Re-throw if error is not for redirect
//   throw error;
// });

/* <RedirectErrorBoundary1
  onError={({ redirect, redirectType, onReset }) => (
    <HandleRedirect
      redirect={redirect}
      redirectType={redirectType}
      reset={onReset}
    />
  )}
>
  hello
</RedirectErrorBoundary1>; */

export const Redirect = ({
  from = "/",
  to: _to,
  params,
  delay = 0,
  ...rest
}: {
  from?: `/${string}`;
  to: `/${string}` | `./${string}` | `../${string}`;
  params?: Record<string, string>;
  delay?: number;
  [key: `param:${string}`]: string;
}) => {
  const [remaining, setRemaining] = useState(delay);

  const to = from === "/" ? _to : `${from}${_to}`;

  useEffect(() => {
    const redirect = () => {
      const url = new URL(
        to,
        self.document.location.href,
      );

      if (params) {
        for (const key in params) {
          url.searchParams.set(key, params[key]);
        }
      }

      for (const key in rest) {
        if (key.startsWith("param:")) {
          url.searchParams.set(
            key.slice("param:".length),
            rest[key as keyof typeof rest],
          );
        }
      }

      self.document.location.href = url.toString();
    };

    if (delay === 0) {
      return redirect();
    }

    setTimeout(() =>
      setRemaining((remaining) => {
        if (remaining > 0) {
          return remaining - 1;
        }

        redirect();

        return remaining;
      }), 1000);
  }, [delay, remaining]);

  return (
    <Show
      when={delay > 0}
      then={() => (
        <X justify="center" align="center">
          <X
            css="p-8 bg-blue-200 rounded-lg"
            justify="center"
            align="center"
            fit-x
            fit-y
          >
            redirecting to {to} in ... ({remaining})
          </X>
        </X>
      )}
    />
  );
};
