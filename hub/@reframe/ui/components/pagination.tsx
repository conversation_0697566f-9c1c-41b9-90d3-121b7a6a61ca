// import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";

import { t, tw } from "../tw.tsx";
import { Button } from "./button.tsx";
import { Y } from "@reframe/ui/components/layout.tsx";

const Pagination = tw("nav", t`mx-auto flex w-full justify-center`).override(
  () => ({
    role: "navigation",
    "aria-label": "pagination",
  }),
);

const PaginationContent = tw("ul", t`flex flex-row items-center gap-1`);

const PaginationItem = tw("li", t``);

const PaginationLink = tw(Button, t``)
  .override(({ isActive }: { isActive?: boolean }) => ({
    variant: isActive ? "default" : "ghost",
  }));

const PaginationPrevious = tw(PaginationLink, t`gap-1 pl-2.5`)
  .override(({ children, css }) => ({
    "aria-label": "Go to previous page",
    size: "default",
    children: (
      <Y css={css}>
        {children}
      </Y>
    ),
  }));

const PaginationNext = tw(PaginationLink, t`gap-1 pr-2.5`)
  .override(({ children, css }) => ({
    "aria-label": "Go to next page",
    size: "default",
    children: (
      <Y css={css}>
        {children}
      </Y>
    ),
  }));

const PaginationEllipsis = tw(
  "span",
  t`flex h-9 w-9 items-center justify-center`,
)
  .override(() => ({
    "aria-hidden": true,
    children: (
      <>
        {/* <MoreHorizontal className="h-4 w-4" />, */}
        <span>{"..."}</span>
        {/* <span className="sr-only">More pages</span> */}
        <span>More pages</span>
      </>
    ),
  }));

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
};
