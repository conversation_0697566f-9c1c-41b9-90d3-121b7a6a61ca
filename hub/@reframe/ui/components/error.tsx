import React from "npm:react@canary";

export const createErrorBoundary = <
  S extends {},
>(onError: (error: unknown) => S | null) =>
  class ErrorBoundary<
    P extends {
      children: React.ReactNode;
      onError?: React.ComponentType<
        S & {
          onReset: () => void;
        }
      >;
    },
  > extends React.Component<
    P,
    { error: S | null }
  > {
    constructor(
      props: P,
    ) {
      super(props);
      this.state = { error: null };
    }

    static getDerivedStateFromError(error: unknown): { error: S | null } {
      return { error: onError(error) };
    }

    render() {
      if (this.state.error !== null) {
        const Fallback:
          | React.ComponentType<
            S & {
              onReset: () => void;
            }
          >
          | undefined = this.props.onError;

        if (Fallback) {
          return (
            <Fallback
              {...this.state.error}
              onReset={() => this.setState({ error: null })}
            />
          );
        }
      }

      return this.props.children;
    }
  };

export const ErrorBoundary = createErrorBoundary((error) => ({ error }));
