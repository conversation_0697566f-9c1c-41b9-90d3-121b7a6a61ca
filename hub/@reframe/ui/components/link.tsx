"use client";

import { CSS, tw } from "../tw.tsx";

const A = tw("a", "no-underline [color:unset]");

export const Link = ({
  from = "/",
  to: _to,
  params,
  children,
  css,
  ...rest
}: {
  from?: `/${string}`;
  to: `/${string}` | `./${string}` | `../${string}`;
  params?: Record<string, string>;
  children: React.ReactNode;
  css?: CSS;
  [key: `param:${string}`]: string;
}) => {
  const to = from === "/" ? _to : `${from}${_to}`;
  return (
    <A
      {...rest}
      href={to + (params ? "?" + new URLSearchParams(params).toString() : "")}
      css={css}
      onClick={(event) => {
        event.preventDefault();
        event.stopPropagation();

        const url = new URL(
          to,
          self.document.location.href,
        );

        if (params) {
          for (const key in params) {
            url.searchParams.set(key, params[key]);
          }
        }

        for (const key in rest) {
          if (key.startsWith("param:")) {
            url.searchParams.set(
              key.slice("param:".length),
              rest[key as keyof typeof rest],
            );
          }
        }

        self.document.location.href = url.toString();
      }}
    >
      {children}
    </A>
  );
};
