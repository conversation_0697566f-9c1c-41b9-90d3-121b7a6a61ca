"use client";

import { t, tw } from "../tw.tsx";

import { useEffect, useImperativeHandle, useRef } from "npm:react@canary";

export const Input = tw(
  "input",
  t`border-neutral-400/50 placeholder:text-neutral-400 flex w-full rounded-md border bg-transparent px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 text-neutral-700`,
  {
    bordered: {
      false: t`border-0`,
      true: t`border border-neutral-400/50 file:border-0`,
    },
    outline: {
      false:
        t`ring-0 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0`,
      true:
        t`ring-offset-primary-50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-300 focus-visible:ring-offset-2`,
    },
  },
)
  .defaults({})
  .override((props, ref) => {
    const cursor = useRef<number | null>(null);
    const inputRef = useRef<HTMLInputElement | null>(null);

    useImperativeHandle(ref, () => inputRef.current!);

    useEffect(() => {
      if (
        inputRef.current &&
        cursor.current !== null &&
        inputRef.current.type !== "email"
      ) {
        inputRef.current.setSelectionRange(cursor.current, cursor.current);
      }
    }, [props.value]);

    return {
      ref: inputRef,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
        cursor.current = e.target.selectionStart;

        return props.onChange?.(e);
      },
    };
  });
