import React, { Suspense } from "npm:react@canary";
import { Link } from "./link.tsx";

export { Link };

type RouterProps =
  & {
    layout?: (
      props: { children: React.ReactNode },
    ) => React.ReactNode;
    page?: () => React.ReactNode;
    route?: (
      segment: string,
      ...rest: string[]
    ) => (Router: WithRouter) => React.ReactNode | Promise<React.ReactNode>;
  }
  & {
    [K in `route:${string}`]?: (
      Router: WithRouter,
    ) => React.ReactNode;
  };

type RouterComponent = (props: RouterProps) => React.ReactNode;

export type WithRouter = {
  Route: RouterComponent;
  Link: typeof Link;
  path: "" | `/${string}`;
};

const segmentPath = (pathname: string) =>
  pathname
    .split("/")
    .filter(Boolean);

const createRouter = (
  segments: string[],
  root?: `/${string}`,
): WithRouter => {
  const Route: RouterComponent = ({
    layout: Layout = ({ children }) => children,
    page: Page = () => <></>,
    ...routes
  }) => {
    const [segment, ...rest] = segments;

    if (segment === undefined) {
      /**
       * this will look something like
       * components.enquque(<Layout1><Slot /></Layout1>)
       * components.enquque(<Layout2><Slot /></Layout2>)
       * ...
       * components.enquque(<Template><Page /></Template>)
       */
      return (
        <Layout>
          <Suspense>
            <Page />
          </Suspense>
        </Layout>
      );
    }

    const Component = `route:${segment}` in routes
      ? routes[`route:${segment}`]!
      : `route` in routes
      ? routes[`route`]?.(segment, ...rest)!
      : () => <></>;

    return (
      <Layout>
        <Suspense>
          <Component
            {...createRouter(
              rest,
              root ? `${root}/${segment}` : `/${segment}`,
            )}
          />
        </Suspense>
      </Layout>
    );
  };

  return {
    Route,
    Link: (props) => (
      <Link
        to={props.to.startsWith("/") && root !== undefined
          ? `${root}${props.to}`
          : props.to}
      >
        {props.children}
      </Link>
    ),
    path: root ?? "",
  };
};

export const Router = ({ request, route, root }: {
  request: Request;
  route: (
    Router: WithRouter,
  ) => React.ReactNode | Promise<React.ReactNode>;
  root?: `/${string}`;
}): React.ReactNode => {
  const url = new URL(request.url);
  const segments = root === undefined
    ? segmentPath(url.pathname)
    : url.pathname.startsWith(root)
    ? segmentPath(url.pathname.slice(root.length))
    : [];

  return route(createRouter(segments, root));
};
