import { ComponentProps } from "npm:react@canary";
import { t, tw } from "../tw.tsx";

import * as SheetPrimitive from "npm:@radix-ui/react-dialog";

const Drawer = SheetPrimitive.Root;

const DrawerTrigger = tw(SheetPrimitive.Trigger, t``);

const DrawerClose = tw(SheetPrimitive.Close, t``);

const DrawerPortal = tw(SheetPrimitive.Portal, t``);

// const SheetOverlay = React.forwardRef<
//   React.ElementRef<typeof SheetPrimitive.Overlay>,
//   React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>
// >(({ className, ...props }, ref) => (
//   <SheetPrimitive.Overlay
//     className={cn(
//       "fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
//       className
//     )}
//     {...props}
//     ref={ref}
//   />
// ))
// SheetOverlay.displayName = SheetPrimitive.Overlay.displayName

const DrawerOverlay = tw(
  SheetPrimitive.Overlay,
  t`fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0`,
);

// const sheetVariants = cva(
//   "fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",
//   {
//     variants: {
//       side: {
//         top:
//           "inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",
//         bottom:
//           "inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",
//         left:
//           "inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",
//         right:
//           "inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm",
//       },
//     },
//     defaultVariants: {
//       side: "right",
//     },
//   },
// );

// interface SheetContentProps
//   extends
//     React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,
//     VariantProps<typeof sheetVariants> {}

// const SheetContent = React.forwardRef<
//   React.ElementRef<typeof SheetPrimitive.Content>,
//   SheetContentProps
// >(({ side = "right", className, children, ...props }, ref) => (
//   <SheetPortal>
//     <SheetOverlay />
//     <SheetPrimitive.Content
//       ref={ref}
//       className={cn(sheetVariants({ side }), className)}
//       {...props}
//     >
//       {children}
//       <SheetPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary">
//         <X className="h-4 w-4" />
//         <span className="sr-only">Close</span>
//       </SheetPrimitive.Close>
//     </SheetPrimitive.Content>
//   </SheetPortal>
// ));
// SheetContent.displayName = SheetPrimitive.Content.displayName;

const DrawerContentInner = tw(
  SheetPrimitive.Content,
  t`fixed z-50 gap-4 bg-blue-100 p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500`,
  {
    side: {
      top:
        t`inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top`,
      bottom:
        t`inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom`,
      left:
        t`inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm`,
      right:
        t`inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm`,
    },
  },
).override((props) => {
  return {
    children: [
      props.children,
      //   <SheetPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-blue-200">
      //     {/* <X className="h-4 w-4" /> */}

      //     <span>{"x"}</span>
      //   </SheetPrimitive.Close>,
    ],
  };
});

const DrawerContent = tw(DrawerPortal, t``)
  .override((props: ComponentProps<typeof DrawerContentInner>) => {
    return {
      children: [
        <DrawerOverlay />,
        <DrawerContentInner {...props} />,
      ],
    };
  });

const DrawerHeader = tw(
  "div",
  t`flex flex-col space-y-2 text-center sm:text-left`,
);

const DrawerFooter = tw(
  "div",
  t`flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2`,
);

const DrawerTitle = tw(
  SheetPrimitive.Title,
  t`text-lg font-semibold text-foreground`,
);

const DrawerDescription = tw(
  SheetPrimitive.Description,
  t`text-sm text-muted-foreground`,
);

export {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerOverlay,
  DrawerPortal,
  DrawerTitle,
  DrawerTrigger,
};
