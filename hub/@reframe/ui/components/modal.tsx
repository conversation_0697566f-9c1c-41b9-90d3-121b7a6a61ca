"use client";

import * as Dialog from "npm:@radix-ui/react-dialog";
import { XIcon } from "../../icons/x.ts";
import { t, tw, CSS } from "../tw.tsx";
import { Y } from "./layout.tsx";
import React from "npm:react@canary";

const Overlay = tw(
  Dialog.Overlay,
  t`fixed inset-0 z-50 bg-neutral-900/90 transition-all duration-100 data-[state=closed]:animate-[exit_150ms_cubic-bezier(0.16, 1, 0.3,1)] data-[state=closed]:fade-out data-[state=open]:fade-in`
);

const Content = tw(
  Dialog.Content,
  t`fixed z-50 focus:outline-none bg-white flex flex-col w-full h-full gap-4 rounded-b-lg s p-6 shadow-lg animate-in data-[state=open]:fade-in-90 data-[state=open]:slide-in-from-bottom-10 data-[state=open]:sm:slide-in-from-bottom-0 justify-center`
);

const Portal = tw(Dialog.Portal, t``);

const Root = tw(Dialog.Root, t``);

const Title = tw(
  Dialog.Title,
  t`text-2xl font-medium leading-none tracking-tight`
);

export const Modal = ({
  children,
  open = false,
  setOpen = () => {},
  title,
  css,
  fullScreen,
}: {
  title?: React.ReactNode;
  children: React.ReactNode;
  open?: boolean;
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  css?: CSS;
  fullScreen?: boolean;
}) => {
  return (
    <Root
      open={open}
      onOpenChange={(bool) => {
        setOpen(bool);
      }}
    >
      <Portal>
        <Y
          css={[
            "fixed inset-0 z-50 flex items-start justify-center sm:items-center",
          ]}
        >
          <Overlay />
          <Content
            css={[
              css,
              "sm:rounded-lg",
              fullScreen
                ? "sm:h-[calc(100vh-48px)] sm:w-[calc(100vw-48px)]"
                : "sm:h-auto sm:max-w-lg sm:zoom-in-90",
            ]}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            {title ? (
              <Y css="text-center pb-6 relative items-center justify-center">
                <Title>{title}</Title>
              </Y>
            ) : null}
            {children}
          </Content>
        </Y>
      </Portal>
    </Root>
  );
};
