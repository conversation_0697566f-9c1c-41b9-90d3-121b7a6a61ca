"use client";

import { CSS } from "../tw.tsx";
import { useEffect, useState } from "npm:react@canary";
import { X } from "./layout.tsx";
import { If } from "./flow.tsx";

export function Toggle({
  value: initialValue,
  onChange,
  on,
  off,
  css,
}: {
  value?: boolean;
  onChange?: (value: boolean) => void;
  on: React.ReactNode;
  off: React.ReactNode;
  css?: CSS;
}) {
  const [value, setValue] = useState(initialValue ?? false);

  useEffect(() => {
    setValue(initialValue ?? false);
  }, [initialValue]);

  return (
    <X
      css={css}
      onClick={(event) => {
        event.stopPropagation();
        setValue((value) => !value);
        onChange?.(!value);
      }}
    >
      <If
        condition={value}
        then={on}
        otherwise={off}
      />
    </X>
  );
}
