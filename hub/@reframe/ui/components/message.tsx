import { X, Y } from "./layout";
import { Text } from "./text";
import { Loader } from "./loader";

export const Message = ({
  message,
  sender,
  actions,
  loading,
}: {
  loading?: boolean;
  sender?: {
    avatar?: string;
    name: string;
  };
  actions?: React.ReactNode;
  message?: {
    role: string;
    content: React.ReactNode;
  };
}) => {
  const isUser = message?.role === "user";

  return (
    <Y css="gap-2">
      {isUser ? null : (
        <X
          css={[
            "mb-2 ml-3 items-center text-neutral-500",
            sender?.avatar ? "gap-3" : "gap-1",
          ]}
        >
          {sender?.avatar && (
            <img
              className="h-6 w-6 rounded-full"
              src={sender.avatar}
              alt=""
              loading="lazy"
            />
          )}

          {sender?.name ? (
            <Text css="text-xs capitalize">{sender.name}</Text>
          ) : null}

          {actions}

          {!message || loading ? <Loader css="w-5" size="xs" /> : null}
        </X>
      )}
      <Y css="gap-0">
        <X
          css={["gap-2 items-start", isUser ? "justify-end" : "justify-start"]}
        >
          <Y
            css={[
              "rounded-lg w-fit max-w-full grow",
              isUser ? "items-end" : "items-start",
            ]}
          >
            {message ? message.content : null}
          </Y>
        </X>
      </Y>
    </Y>
  );
};
