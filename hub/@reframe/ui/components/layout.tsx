import { t, tw } from "@reframe/ui/tw.tsx";

export const Div = tw(
  "div",
  t`flex grow items-stretch overflow-visible`,
  {
    direction: {
      x: [
        t`group flex-row`,
        t`[&>.hug-x-container]:grow`,
        t`[&>.hug-x-contents]:shrink-0 [&>.hug-x-contents]:grow-0`,
        t`[&>.hug-y-container]:self-stretch`,
        t`[&>.hug-y-contents]:self-auto`,
      ],
      y: [
        t`group flex-col`,
        t`[&>.hug-y-container]:grow`,
        t`[&>.hug-y-contents]:shrink-0 [&>.hug-y-contents]:grow-0`,
        t`[&>.hug-x-container]:self-stretch`,
        t`[&>.hug-x-contents]:self-auto`,
      ],
    },
    justify: {
      start: t`justify-start`,
      end: t`justify-end`,
      center: t`justify-center`,
      between: t`justify-between`,
      around: t`justify-around`,
      evenly: t`justify-evenly`,
    },
    align: {
      start: t`items-start`,
      end: t`items-end`,
      center: t`items-center`,
      baseline: t`items-baseline`,
    },
    "fit-x": {
      false: t`hug-x-container min-w-0`,
      true: t`hug-x-contents min-w-[min-content]`,
    },
    "fit-y": {
      false: t`hug-y-container min-h-0`,
      true: t`hug-y-contents min-h-[min-content]`,
    },
    wrap: {
      true: t`flex-wrap`,
      false: t`flex-nowrap`,
      reverse: t`flex-wrap-reverse`,
    },
    scroll: {
      true:
        t`!overflow-auto [scrollbar-color:#aaa_transparent] [scrollbar-width:thin]`,
      false: t`overflow-visible`,
    },
    debug: {
      true: [
        t`outline-blue-600 outline-2 outline-dashed`,
        `[&_*]:outline-dashed [&_*]:outline-blue-400`,
      ],
      false: t``,
    },
  },
).override((
  {
    direction = "x",
    style: _style,
    "min-x": minWidth,
    "min-y": minHeight,
    "max-x": maxWidth,
    "max-y": maxHeight,
    gap: _gap,
  }: {
    direction?: "x" | "y";
    style?: React.CSSProperties;
    "min-x"?: string;
    "min-y"?: string;
    "max-x"?: string;
    "max-y"?: string;
    gap?: number | string | [number | string, number | string];
  },
) => {
  const style: React.CSSProperties = {
    minWidth,
    minHeight,
    maxWidth,
    maxHeight,
    ..._style,
  };

  if (_gap) {
    const gap = (Array.isArray(_gap) ? _gap : [_gap, _gap]).map((gap) =>
      typeof gap === "number" ? `${gap * 4}px` : gap
    );

    style["gap"] = direction === "x"
      ? `${gap[0]} ${gap[1]}`
      : `${gap[1]} ${gap[0]}`;
  }

  return {
    style,
  };
}).defaults({
  direction: "x",
  justify: "start",
  align: "start",
  "fit-x": false,
  "fit-y": false,
});

export const X = Div.defaults({ direction: "x" });
export const Y = Div.defaults({ direction: "y" });

export const Layer = tw(Div, t`absolute data-rf-layer rounded-[inherit]`);

export const Frame = tw(
  X,
  t`relative overflow-hidden [&>*:not(.data-rf-layer)]:z-10`,
).sub({ Layer });
