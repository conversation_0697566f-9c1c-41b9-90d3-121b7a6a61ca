import { Frame, X, Y } from "./layout.tsx";
import { <PERSON><PERSON><PERSON><PERSON> } from "./scrollarea.tsx";
import { Textarea } from "./textarea.tsx";
import { Text } from "./text.tsx";
import { Button } from "./button.tsx";
import { CSS } from "../tw.tsx";

import { PaperAirplaneIcon } from "npm:@reframe.so/icons/icons/PaperAirplaneIcon";

import { useEffect, useRef, useState } from "npm:react@canary";
import { RotateCwIcon } from "npm:@reframe.so/icons/icons/RotateCwIcon";

export const throttle = <T extends any[]>(
  fn: (...args: T) => void,
  time: number,
): (...args: T) => void => {
  // execute immediately, unless the first call is within the time
  // in which case, wait until the time has passed

  let lastCall = 0;
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return (...args: T) => {
    const now = Date.now();
    const timeSinceLastCall = now - lastCall;

    if (timeSinceLastCall >= time) {
      lastCall = now;

      return fn(...args);
    }

    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(() => {
      lastCall = Date.now();
      fn(...args);
    }, time - timeSinceLastCall);
  };
};

const isSafari = () => {
  if (typeof navigator === "undefined") {
    return false;
  }

  if (!navigator?.userAgent) {
    return false;
  }

  const ua = navigator.userAgent.toLowerCase() ?? "";

  return ua.includes("safari") && !ua.includes("chrome");
};

const gotoBottom = throttle(() => {
  if (typeof document === "undefined") {
    return;
  }

  const el = document.querySelector(
    "[data-role=chat-scroll-area]>[data-radix-scroll-area-viewport]",
  );

  if (el) {
    el.scrollTop = el.scrollHeight;
  }
}, 50);

const getRGBA = (hex: string, opacity: number) => {
  const bigint = parseInt(hex.replace("#", ""), 16);

  const values = {
    r: (bigint >> 16) & 255,
    g: (bigint >> 8) & 255,
    b: bigint & 255,
  };

  return `rgba(${values.r} ${values.g} ${values.b} / ${opacity})`;
};

export const Chat = ({
  title,
  children,
  onMessage,
  onUpload,
  css,
}: {
  title?: React.ReactNode;
  children: React.ReactNode;
  onMessage?: (message: string) => void;
  onUpload?: (file: File[]) => void;
  css: CSS;
}) => {
  const [input, setInput] = useState<string>("");
  const inputRef = useRef<HTMLTextAreaElement | null>(null);
  const fileRef = useRef<HTMLInputElement>(null);

  const placeholder = "Type a message...";

  // todo:
  // if (isSafari()) {
  //   gotoBottom();
  // }

  const handleEnter = async () => {
    if (input.trim().length === 0) {
      return;
    }

    onMessage?.(input);
    setInput("");
    setTimeout(gotoBottom, 50);
  };

  useEffect(() => {
    gotoBottom();
  }, []);

  return (
    <Y css={["p-0 gap-1 relative grow", css]}>
      <Frame
        direction="x"
        css={[
          "h-16 text-primary-50 text-lg font-normal tracking-tight items-center justify-center gap-2 bg-primary-600 shrink-0",
        ]}
      >
        <Text>{title}</Text>
        <Frame.Layer css="right-3 top-3 z-10 gap-2">
          <>
            <Button
              variant="link"
              onClick={() => {
                localStorage.removeItem("sessionId");
                window.location.reload();
              }}
              css="p-0"
            >
              <RotateCwIcon css="text-primary-100 hover:text-primary-50 w-5 h-5 stroke-[1.5]" />
            </Button>
          </>
        </Frame.Layer>
      </Frame>

      <ScrollArea
        css="child-[]/viewport:px-3 [&>div]:child-[]/viewport:!flex [&>div]:child-[]/viewport:min-h-[calc(100%+1px)]"
        data-role="chat-scroll-area"
      >
        <Y css="min-h-[212px] grow">
          <Y data-role="chat-container" css="grow gap-3 pt-4">
            {children}

            <X
              data-role="chat-scroll-anchor"
              css="h-px bg-transparent mt-2 [overflow-anchor:auto]"
            />
          </Y>
        </Y>
      </ScrollArea>
      <Frame
        direction="y"
        css="hug z-0 sticky bottom-0 bg-white shadow-md shrink-0 border-t-neutral-200 border-t rounded-b-lg px-3 pt-5 pb-1 gap-4"
      >
        <Textarea
          bordered={false}
          outline={false}
          rows={1}
          css={[
            "w-full p-0 h-auto max-h-[160px] pr-10",
            // todo: widget?.mobile ? "active:text-[16px]" : "",
          ]}
          placeholder={placeholder}
          autoFocus
          ref={inputRef}
          // disabled={loading}
          value={input}
          onChange={(e) => {
            setInput(e.target.value);
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              e.preventDefault();

              return handleEnter();
            }
          }}
        />

        <Y css="justify-center text-center text-sm text-neutral-400 tracking-wide">
          <a href={`https://retune.so`} target="_blank" rel="noreferrer">
            <Text>
              Powered by{" "}
              <Text css="text-neutral-500 font-semibold">retune.so</Text>
            </Text>
          </a>
        </Y>

        <Frame.Layer css="right-3 top-5 z-10">
          {input.length === 0
            ? (
              <>
                {
                  /* <Button
                variant="ghost"
                size="sm"
                css="h-7 w-7"
                onClick={() => fileRef.current?.click()}
              >
                <ImageIcon css="stroke-2 h-6 w-6" />
              </Button>
              <input
                ref={fileRef}
                type="file"
                style={{
                  display: "none",
                }}
                multiple
                onChange={async (e) => {
                  const files = e.target.files;

                  if (!files) {
                    return;
                  }

                  const filesArray = Array.from(files);

                  onUpload?.(filesArray);
                }}
              /> */
                }
              </>
            )
            : (
              <Button
                variant="ghost"
                // loading={loading}
                size="sm"
                css="h-7 w-7"
                onClick={handleEnter}
              >
                <PaperAirplaneIcon width={0} css="fill-primary-900 h-6 w-6" />
              </Button>
            )}
        </Frame.Layer>
      </Frame>
    </Y>
  );
};
