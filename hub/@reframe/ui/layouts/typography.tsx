import { CSS, t, tw } from "@reframe/ui/tw.tsx";

const Text = tw("span", t`leading-7`, {});

const Typography = tw("span", t`leading-7`, {
  variant: {
    largest: t`scroll-m-20 text-6xl font-extrabold tracking-tight lg:text-7xl`,
    xxxl: t`scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl`,
    xxl:
      t`scroll-m-20 pb-2 text-3xl font-semibold tracking-tight transition-colors first:mt-0`,

    xl:
      t`scroll-m-20 pb-2 text-3xl font-semibold tracking-tight transition-colors first:mt-0`,
    lg:
      t`scroll-m-20 pb-2 text-2xl font-semibold tracking-tight transition-colors first:mt-0`,

    md: t`scroll-m-20 text-xl font-semibold tracking-tight`,
    sm: t`leading-7 [&:not(:first-child)]:mt-6`,
    xs: t`text-xs leading-5`,
    blockquote: t`mt-6 border-l-2 pl-6 italic`,
    code: t`bg-neutral-200/10 text-red-600 px-1 rounded-md`,
  },
  weight: {
    bold: t`font-bold`,
    semiBold: t`font-semibold`,
    normal: t`font-normal`,
  },
  clamp: {
    1: t`-webkit-line-clamp-1 overflow-hidden`,
    2: t`-webkit-line-clamp-2 overflow-hidden`,
    3: t`-webkit-line-clamp-3 overflow-hidden`,
    6: t`-webkit-line-clamp-6 overflow-hidden`,
    none: t`-webkit-line-clamp-none overflow-visible`,
  },
  wrap: {
    wrap: t`text-wrap`,
    nowrap: t`text-nowrap`,
    balance: t`text-balance`,
    pretty: t`text-pretty`,
  },
  transform: {
    uppercase: t`uppercase`,
    lowercase: t`lowercase`,
    capitalize: t`capitalize`,
    normal: t`normal-case`,
  },
  align: {
    left: t`text-left`,
    center: t`text-center`,
    right: t`text-right`,
    justify: t`text-justify`,
  },
  size: {
    xs: t`text-xs`,
    sm: t`text-sm`,
    normal: t`text-base`,
    lg: t`text-lg`,
    xl: t`text-xl`,
    xl2: t`text-2xl`,
    xl3: t`text-3xl`,
    xl4: t`text-4xl`,
    xl5: t`text-5xl`,
    xl6: t`text-6xl`,
  },
})
  .defaults({
    variant: "sm",
    clamp: "none",
    wrap: "wrap",
    transform: "normal",
    align: "left",
  })
  .override(() => ({}));

export { Text, Typography };
