import { twMerge } from "npm:tailwind-merge";

export const t = (strings: TemplateStringsArray) => {
  return strings[0]!;
};

export type CSS = string | CSS[] | null | undefined;

type AS = keyof JSX.IntrinsicElements | ((props: any, ref?: any) => any);

type ElementType1<N extends keyof JSX.IntrinsicElements> =
  JSX.IntrinsicElements[N] extends {
    ref?: infer R;
  } ? R extends React.Ref<infer E> ? E extends HTMLElement ? E
      : never
    : never
    : never;

type HTMLElementType<N extends AS> = N extends keyof JSX.IntrinsicElements
  ? ElementType1<N>
  : N extends (props: infer P) => any ? P extends {
      ref?: infer R;
    } ? R extends React.Ref<infer E> ? E extends HTMLElement ? E
        : never
      : never
    : never
  : never;

type ElementProps<E extends AS> = E extends keyof JSX.IntrinsicElements
  ? JSX.IntrinsicElements[E]
  : E extends (props: infer P) => any ? P
  : never;

type VariantProps<V extends Record<string, Record<string, CSS>>> = {
  [K in keyof V]?: CastBoolean<keyof V[K]>;
};

export type MakeProps<
  E extends AS,
  V extends Record<string, Record<string, CSS>>,
> = React.PropsWithChildren<
  VariantProps<V> & { css?: CSS } & Omit<ElementProps<E>, keyof VariantProps<V>>
>;

type CastBoolean<T> = T extends "true" ? true : T extends "false" ? false : T;

const merge = (classes: CSS) => {
  if (!classes) {
    return "";
  }

  if (typeof classes === "string") {
    return classes;
  }

  const result: CSS[] = [];

  for (const css of classes) {
    if (css) {
      result.push(css);
    }
  }

  return twMerge(result);
};

export const px = (unit: number | string) =>
  typeof unit === "number" ? `${unit}px` : unit;

export const pick = <T extends object, K extends keyof T>(
  obj: T,
  keys: K[],
): Pick<T, K> => {
  return Object.fromEntries(keys.map((key) => [key, obj[key]])) as Pick<T, K>;
};

export const omit = <T extends object, K extends keyof T>(
  obj: T,
  keys: K[],
): Omit<T, K> => {
  return Object.fromEntries(
    Object.entries(obj).filter(([key]) => !keys.includes(key as K)),
  ) as Omit<T, K>;
};

export function tw<
  E extends AS,
  V extends Record<string, Record<string, CSS>> = {},
>(
  As: E,
  base?: CSS,
  variants?: V,
) {
  const createComponent = <Extra extends {} = {}>(args: {
    useOverride?: (
      props: MakeProps<E, V> & Extra,
      ref?: React.Ref<HTMLElementType<E>>,
    ) => MakeProps<E, V>;
    defaults?: Partial<VariantProps<V>>;
  }) => {
    const { useOverride, defaults } = args;

    const Component = (
      initalProps: MakeProps<E, V> & Extra,
      ref?: React.Ref<HTMLElementType<E>>,
    ) => {
      const overrides = useOverride?.(initalProps, ref);

      const {
        children = undefined,
        css = "" as CSS,
        className = "",
        ...props
      } = {
        ...defaults,
        ...initalProps,
        ...overrides,
      };

      const classes = [base];

      const rest = { ...props };

      if (variants) {
        for (const [key, value] of Object.entries(variants)) {
          if (props[key] !== undefined) {
            delete rest[key];

            const variant = value[String(props[key])];
            if (variant) {
              classes.push(variant);
            }
          }
        }
      }

      classes.push(css);

      return (
        <As
          ref={ref}
          className={merge([className, classes])}
          {...rest}
        >
          {children}
        </As>
      );
    };

    return Object.assign(Component, {
      factory: {
        base,
        variants,
        defaults,
      },

      sub: <S extends Record<string, AS>>(sub: S) =>
        Object.assign(createComponent(args), sub),

      override: <Extra extends {} = {}>(
        useOverride: (
          props: MakeProps<E, V> & Extra,
          ref?: React.Ref<HTMLElementType<E>>,
        ) => MakeProps<E, V>,
      ) =>
        createComponent({
          ...args,
          useOverride,
        }),

      defaults: (newDefaults: Partial<VariantProps<V>>) =>
        createComponent({
          ...args,
          defaults: {
            ...defaults,
            ...newDefaults,
          },
        }),
    });
  };

  return createComponent({});
}

tw.t = t;
