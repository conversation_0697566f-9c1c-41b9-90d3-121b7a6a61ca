import { t } from "./main.ts";

const t1 = t.object({
  a: t.number(),
  b: t.string(),
  c: t.tuple([
    t.union([
      t.object({ type: t.literal("foo"), foo: t.number() }),
      t.object({ type: t.literal("bar"), bar: t.string() }),
    ]),
    t.array(t.number()),
  ]),
}) satisfies t.Object<{
  a: t.Number;
  b: t.String;
  c: t.<PERSON><[
    t.Union<[
      t.Object<{
        type: t.Literal<"foo">;
        foo: t.Number;
      }>,
      t.Object<{
        type: t.Literal<"bar">;
        bar: t.String;
      }>,
    ]>,
    t.Array<t.Number>,
  ]>;
}>;

type T1 = typeof t1;

type T11 = t.Type<T1>;
