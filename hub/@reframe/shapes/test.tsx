import { RefsOf } from "./core.ts";
import { t } from "./main.ts";

type Tx1 = t.WithRef<t.Access<t.Ref<"a">, ["name", 0]>, {
  a: t.Object<{ name: t.Array<t.Literal<44>> }>;
}>;

type Tx2 = t.KindOut<Tx1, {
  a: t.Object<{ name: t.Array<t.Literal<43>> }>;
}>;

type T = t.Union<
  [
    t.Access<t.Ref<"a">, ["name", 0]>,
    // t.Literal<"hello">,
    // t.Literal<"world">,
    // t.Literal<42>,
    // t.<PERSON>,
    // t.Object<{ name: t.String; value: t.Number }>,
    // t.Object<{ name: t.Number }>,
    // t.Object<{ name: t.Array<t.String> }>,
    // // t.<PERSON><[t.Ref<"a">]>,
    // t.<PERSON><[t.Ref<"b">]>,
    // t.Access<
    //   t.Object<{
    //     name: t.<PERSON>rray<t.Literal<"baz">>;
    //     foo: t.<PERSON><[
    //       t.Number,
    //       t.Object<{
    //         foo: t.Literal<"bar">;
    //       }>,
    //     ]>;
    //   }>,
    //   ["name", 0]
    // >,
    // t.Access<t.Ref<"b">, ["foo", 1, "foo"]>,
  ]
>;

type TT = t.Out<T>;

type Z = t.Tuple<[t.Ref<"a">]>;

type T1 = t.KindOut<
  t.Union<
    [
      t.Literal<42>,
      t.Literal<"hello">,
      t.Literal<"world">,
      // t.String,
    ]
  >,
  {}
>;

type K = t.KindOut<T, {
  b: t.Object<{
    name: t.Array<t.Literal<57>>;
    foo: t.Tuple<[
      t.Number,
      t.Object<{
        foo: t.Literal<"bar">;
      }>,
    ]>;
  }>;
  a: t.Object<{ name: t.Array<t.Literal<43>> }>;
}>;

/* ---- test ---- */

const r3 = {
  b: t.object({
    a: t.number(),
    b: t.object({
      x: t.number(),
      y: t.number(),
      z: t.ref("b"),
    }),
  }),
};

const b3 = t.withRef(
  t.ref("b"),
  r3,
);

type T3 = t.KindOut<typeof b3, RefsOf<typeof b3>>;
type T3x = t.KindOut<t.Literal<33>, {}>;

console.log(b3);

const t5 = t.withRef(
  t.object({
    a: t.number(),
    c: t.boolean(),
    x: t.array(t.number()),
    y: t.record(
      t.string(),
      t.number(),
    ),
    y1: t.record(
      t.literal("yo"),
      t.tuple([
        t.number(),
        t.null(),
        t.ref("b"),
        t.undefined(),
        t.literal(42),
      ]),
    ),
    b: t.object({
      x: t.number(),
      y: t.number(),
      z: t.ref("b").optional(),
    }),
  }),
  r3,
);

type T5 = t.Out<typeof t5>;

const errors = ShapeError.merge(t5.validate({
  a: 1,
  c: true,
  x: [
    "1",
  ],
  y1: {
    yoo: [
      1,
      null,
      { a: 1, b: {} },
      undefined,
    ],
  },
  b: {
    y: 3,
    z: {
      b: {
        z: {},
      },
    },
  },
}));

console.log(ShapeError.format(errors.errors, true));

const x = t.tuple([
  t.number(),
  t.literal("foo"),
  t.array(t.number()),
  t.tuple([t.literal(42), t.number()]),
  t.object({
    a: t.number(),
    b: t.literal("bar"),
    c: t.object({
      d: t.array(
        t.object({
          e: t.number(),
          f: t.tuple([t.number(), t.literal(true)]),
          g: t.null(),
          h: t.undefined(),
          i: t.record(t.string(), t.array(t.literal(42))),
          j: t.record(t.string(), t.object({ foo: t.literal("bar") })),
        }),
      ),
    }),
  }),
]) satisfies t.Tuple<[
  t.Number,
  t.Literal<"foo">,
  t.Array<t.Number>,
  t.Tuple<[t.Literal<42>, t.Number]>,
  t.Object<{
    a: t.Number;
    b: t.Literal<"bar">;
    c: t.Object<{
      d: t.Array<
        t.Object<{
          e: t.Number;
          f: t.Tuple<[t.Number, t.Literal<true>]>;
          g: t.Null;
          h: t.Undefined;
          i: t.Record<t.String, t.Array<t.Literal<42>>>;
          j: t.Record<
            t.String,
            t.Object<{ foo: t.Literal<"bar"> }>
          >;
        }>
      >;
    }>;
  }>,
]>;

const test = x;

type Test = t.Type<typeof test>;

// const discriminate = (
//   value: unknown,
//   shapes: t.Compiled<any, any>[],
//   // paths to discriminate
//   discriminant: string[],
// ) => {
//   // if shapes contains refs, throw error
//   for (const shape of shapes) {
//     if (shape.type === "ref") {
//       throw new Error(
//         "refs should have been resolved before calling discriminate",
//       );
//     }
//   }

//   if (value === null) {
//     return t.filter((shape) => shape.type === "null");
//   }

//   if (value === undefined) {
//     return t.filter((shape) => shape.type === "undefined");
//   }

//   if (typeof value === "boolean") {
//     const matches = t.filter((shape) =>
//       shape.type === "literal" && shape.const === value
//     );

//     if (matches.length > 0) {
//       return matches;
//     }

//     return t.filter((shape) => shape.type === "boolean");
//   }

//   if (typeof value === "number") {
//     const matches = t.filter((shape) =>
//       shape.type === "literal" && shape.const === value
//     );

//     if (matches.length > 0) {
//       return matches;
//     }

//     return t.filter((shape) => shape.type === "number");
//   }

//   if (typeof value === "string") {
//     const matches = t.filter((shape) =>
//       shape.type === "literal" && shape.const === value
//     );

//     if (matches.length > 0) {
//       return matches;
//     }

//     // TODO: template matching

//     return t.filter((shape) => shape.type === "string");
//   }

//   if (typeof value === "object" && Array.isArray(value)) {
//     const m1 = t.filter((shape) => shape.type === "array");

//     if (m1.length <= 1) {
//       return m1;
//     }

//     // discriminate with length first
//     const length = value.length;

//     const m2 = m1.filter((shape) => {
//       return shape.type === "array" ||
//         shape.type === "tuple" && shape.items.length === length;
//     });

//     if (m2.length <= 1) {
//       return m2;
//     }

//     let m3 = m2;
//     for (const path of discriminants) {
//       m3 = m2.filter((shape) => {
//         return match(shape.path(path), value);
//       });

//       if (m3.length <= 1) {
//         return m3;
//       }
//     }
//   }
// };

const t4 = t
  .recursive(
    "tree",
    (tree) =>
      t.object({
        value: t.number(),
        left: tree,
        right: tree,
      }),
  );

const tree = t.record(
  t.string(),
  t.ref("tree"),
);

const t44 = t4.refs;

type Tree = t.KindOut<typeof tree, { tree: typeof tree }>;
type Tree4 = t.Type<typeof t4>;
type Tree12 = Tree["left"]["x"]["y"]["z"];

type n = Tree["left"][0]["right"]["left"];

type Tree1 = {
  value: number;
  left: Tree1[];
  right: Tree1;
};

type LISP = LISP[];
type X = LISP[0][0][0][0][0][0][0][0][0][0][0][0][0][0][0][0];

const lisp = t.array(t.ref("l"));
type LISP1 = t.KindOut<typeof lisp, { l: typeof lisp }>;

type LISP2 = t.KindOut<
  t.Array<t.Ref<"lisp">>,
  { lisp: t.Array<t.Ref<"lisp">> }
>;

const x22 = [1, 2] satisfies Record<number, number>;

// t.union([
//   t.string(),
//   t.number(),
//   t.boolean(),
//   t.null(),
//   // t.array(t.ref("json")),
//   t.tuple([t.string(), t.ref("json")]),
//   ,
// ]);

type Join<
  T extends { h: string; t: string[] },
> = T extends { h: infer H; t: infer R } ? R extends any[] ? [H, ...R] : never
  : never;

type LazyArray<T extends string[]> = T extends
  [infer H extends string, ...infer R extends string[]]
  ? Join<{ h: Uppercase<H>; t: LazyArray<R> }>
  : [];

type X11 = LazyArray<["a", "b", "c"]>;

const j0 = t.withRef(
  t.ref("a"),
  {
    a: t.number(),
  },
);

const j1 = t.withRef(
  t.ref("json"),
  {
    json: t.union([
      t.literal("x"),
      t.array(t.ref("json")),
    ]),
  },
);

const j3 = t.withRef(
  t.ref("json"),
  {
    json: t.union([
      t.literal("x"),
      t.tuple([t.ref("json")]),
    ]),
  },
);

const j4 = t.withRef(
  t.ref("json"),
  {
    json: t.union([
      t.number(),
      t.ref("array"),
    ]),
    array: t.array(t.union([
      t.number(),
      t.array(t.ref("json")),
    ])),
  },
);

const json = t.union([
  t.null(),
  t.number(),
  t.string(),
  t.boolean(),
  t.array(t.ref("json")),
  t.record(t.string(), t.ref("json")),
]);

const test1 = t
  .withRef(
    t.object({
      a: t.ref("a"),
      b: t.ref("b"),
    }),
    {
      a: t.object({
        name: t.literal("[a]"),
        a: t.number(),
        b: t.record(
          t.union([t.literal("foo"), t.literal("bar")]),
          t.tuple([
            t.number(),
            t.string(),
            t.array(t.ref("a")),
          ]),
        ),
      }),
      b: t.object({
        // ".": t.ref("a"),
        // ".a": t.access(t.ref("a"), ["a"]),
        // ".b": t.access(t.ref("a"), ["b"]),
        // ".b.foo": t.access(t.ref("a"), ["b", "foo"]),
        // ".b.bar": t.access(t.ref("a"), ["b", "bar"]),
        // ".b.baz": t.access(t.ref("a"), ["b", "baz"]),
        ".b.foo.0": t.access(t.ref("a"), ["b", "foo", 0]),
        ".b.foo.1": t.access(t.ref("a"), ["b", "foo", 1]),
        ".b.foo.2": t.access(t.ref("a"), ["b", "foo", 2]),
        ".b.foo.3": t.access(t.ref("a"), ["b", "foo", 3]),
        ".b.foo.2.0.name": t.access(t.ref("a"), ["b", "foo", 2, 0, "name"]),
      }),
    },
  );

type Test1 = t.Type<typeof test1>["b"];

const j = t.withRef(t.ref("json"), { json });

const j2 = t.withRef(
  t.ref("json"),
  {
    json: t.union([t.string(), t.array(t.ref("json"))]),
  },
);

type J = t.Type<typeof j>;
type J1 = t.Type<typeof j1>;
type J2 = t.Type<typeof j2>;
// type J3 = t.Type<typeof j3>;
type J4 = t.Type<typeof j4>;

type J22 = t.KindOut<
  t.Ref<"json">,
  {
    json: t.Array<t.Ref<"json">>;
  }
>;

const jj = {
  x: [7, "", null],
  y: [true, [1, 2, 3], null, {
    z: "new Date()",
  }],
} satisfies J;

type SS = t.KindOut<
  t.Object<{
    x: t.Literal<"x">;
    y: t.Array<t.Ref<"x">>;
    z: t.Ref<"y">;
    w: t.Ref<"x">;
    zz: t.Ref<"z">;
  }>,
  {
    x: t.Object<{
      name: t.Literal<"x">;
    }>;
    y: t.Array<
      t.Object<{
        xx: t.Ref<"x">;
        zz: t.Ref<"z">;
      }>
    >;
    z: t.Object<{
      name: t.Literal<"z">;
      xx: t.Ref<"x">;
      yy: t.Ref<"y">;
      zz: t.Ref<"z">;
    }>;
  }
>;

const u = t.union([
  t.literal("hello"),
  t.literal("world"),
  t.string(),
  t.number(),
  t.literal(true),
  t.object({ name: t.string(), value: t.number() }),
  t.object({ name: t.number() }),
  t.object({ name: t.number() }),
  t.array(t.string()),
  t.array(t.number()),
  t.object({ name: t.array(t.string()) }),
  t.object({ name: t.array(t.number()) }),
]);

type k1 = t.KindOut<t.Ref<"y">, {
  y: t.Array<
    t.Union<[
      t.Literal<"hello">,
      t.Ref<"y">,
    ]>
  >;
  z: t.Object<{
    x: t.Tuple<[
      t.Number,
      t.Ref<"z">,
    ]>;
  }>;
}>;

type k2 = t.KindOut<t.Ref<"z">, {
  y: t.Tuple<[
    t.Union<[
      t.Literal<"hello">,
      t.Ref<"y">,
    ]>,
  ]>;
  z: t.Object<{
    x: t.Tuple<[
      t.Number,
      t.Ref<"z">,
    ]>;
  }>;
}>;

type IntersectKindHelper<
  T extends t.Shape[],
  Acc,
  Defs extends Record<string, t.Shape>,
> = T extends [infer First, ...infer Rest]
  ? First extends t.Shape
    ? Rest extends t.Shape[]
      ? IntersectKindHelper<Rest, Acc & t.KindOut<First, Defs>, Defs>
    : never
  : never
  : Acc;

type IntersectKindOut<
  T extends t.Shape[],
  Defs extends Record<string, t.Shape>,
> = IntersectKindHelper<T, unknown, Defs>;

type I1 = IntersectKindOut<[
  t.Object<{
    name: t.String;
    a: t.Object<{
      x: t.Array<t.Number>;
    }>;
  }>,
  t.Object<{
    value: t.Number;
    a: t.Object<{
      x: t.Array<t.Number>;
    }>;
  }>,
  // this should not be allowed
  // how to handle this?
  t.Array<t.Number>,
], {}>;

type Flat<T> = T extends infer U ? { [K in keyof U]: U[K] } : never;
type U = Flat<
  Record<string, {}> & {
    a: {};
    b: {};
    c: {};
  }
>;
type U1 = Record<"foo" | "bar", {}>;
type U4 = keyof U1;

type U2 = "foo" extends "foo" | "bar" ? true : false;
type U3 = [] extends (infer U)[] ? U : "";

type M<A, B> = {
  [K in keyof A | keyof B]: K extends keyof A ? K extends keyof B ? [A[K], B[K]]
    : A[K]
    : K extends keyof B ? B[K]
    : never;
};

type M1 = M<
  Record<"foo" | "bar" | "baz1", string>,
  Record<"foo" | "bar2" | "baz", boolean>
>;

type M2 = [[1, 2, 3]["length"], (number[])["length"]];

/**
 * I<A,B>
 *   A: Primitive
 *     -> Same<A,B>
 *   A: Tuple
 *     B: Tuple | Array<T>
 *       -> A['length'] extends B['length'] ? { [K in keyof A]: I<A[K],B[K]> } : never
 *     -> never
 *   A: Array<T>
 *     B: Tuple -> ^
 *     B: Array<T>
 *       -> Array<I<T,B>>
 *   A: Record<KA,VA>
 *     B: Record<KB,VB> | Object
 *       -> { K in keyof A | keyof B: K in A ? K in B ? I<A[K],B[K]> : A[K] : B[K] }
 *   A: Ref<R>
 */

type IntersectTypes<
  A,
  B,
> = null;

const lisp1 = t.recursive(
  "lisp",
  (lisp) =>
    t.union([
      t.string(),
      t.array(lisp),
    ]),
);

const lisp2 = t.withRef(
  t.ref("lisp"),
  {
    lisp: t.union([t.string(), t.array(t.ref("lisp"))]),
  },
);

type L1 = t.Type<
  typeof lisp1
>;

type L2 = t.Type<
  typeof lisp2
>;

type LU = L1 extends L2 ? true : false;

const m = t.withRef(
  t.object({
    A: t.array(t.ref("a")),
    Z: t.ref("z"),
  }),
  {
    x: t.array(t.ref("a")),
    a: t.object({
      a: t.number(),
      b: t.string(),
      c: t.array(t.ref("test")),
    }),
    test: t.object({
      x: t.ref("x"),
      y: t.string(),
    }),
    z: t.ref("z"),
  },
);

type Mx = t.Type<typeof m>;

type F1 = (_: 1) => void;
type G1 = (_: 1 | 2) => void;

type FG1 = F1 extends G1 ? [F1, "->", G1]
  : G1 extends F1 ? [G1, "->", F1]
  : never;

type F2 = (() => 1) | (() => 2);
type G2 = () => 1 | 2;

type FG2 = F2 extends G2 ? [F2, "->", G2]
  : G2 extends F2 ? [G2, "->", F2]
  : never;
