import createIcon from "@reframe/icons/create-icon.ts";

export const ChatIcon = createIcon("Chat", [
  ["path", {
    d: "M15 4.36364C17.8209 4.36364 20.5263 5.48425 22.521 7.47895C24.5158 9.47366 25.6364 12.1791 25.6364 15C25.6364 17.8209 24.5158 20.5263 22.521 22.521C20.5263 24.5157 17.8209 25.6364 15 25.6364H7.48068C6.84041 25.6364 6.41411 25.6358 6.08394 25.6154C5.76328 25.5955 5.61167 25.56 5.51549 25.5243L5.10408 26.6312L5.51674 25.5247C5.27864 25.4359 5.06241 25.297 4.88272 25.1173C4.70303 24.9376 4.56407 24.7214 4.47527 24.4833C4.43962 24.3869 4.40442 24.2354 4.38464 23.9166C4.36417 23.5866 4.36364 23.1604 4.36364 22.5193V15C4.36364 12.1791 5.48425 9.47366 7.47895 7.47895C9.47366 5.48425 12.1791 4.36364 15 4.36364ZM24.1924 5.80761C21.7544 3.36964 18.4478 2 15 2C11.5522 2 8.24558 3.36964 5.80761 5.80761C3.36964 8.24558 2 11.5522 2 15V22.5552C1.99999 23.151 1.99998 23.651 2.02554 24.0629C2.0522 24.4928 2.10985 24.9035 2.26018 25.308L2.26064 25.3092C2.46786 25.8648 2.79209 26.3693 3.21138 26.7886C3.63066 27.2079 4.13519 27.5321 4.69076 27.7394L4.69201 27.7398C5.09663 27.8902 5.50786 27.9478 5.93766 27.9745C6.34974 28 6.84977 28 7.44503 28H15C18.4478 28 21.7544 26.6304 24.1924 24.1924C26.6304 21.7544 28 18.4478 28 15C28 11.5522 26.6304 8.24558 24.1924 5.80761Z",
  }],
  ["path", {
    d: "M20.6133 13.5226C20.6133 12.8699 20.0842 12.3408 19.4315 12.3408H10.5678C9.91513 12.3408 9.38601 12.8699 9.38601 13.5226C9.38601 14.1753 9.91513 14.7045 10.5678 14.7045H19.4315C20.0842 14.7045 20.6133 14.1753 20.6133 13.5226ZM16.1815 19.4317C16.1815 18.779 15.6523 18.2499 14.9996 18.2499H10.5678C9.91513 18.2499 9.38601 18.779 9.38601 19.4317C9.38601 20.0844 9.91513 20.6135 10.5678 20.6135H14.9996C15.6523 20.6135 16.1815 20.0844 16.1815 19.4317Z",
  }],
]);

// <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
//   <path fill-rule="evenodd" clip-rule="evenodd" d="M15 4.36364C17.8209 4.36364 20.5263 5.48425 22.521 7.47895C24.5158 9.47366 25.6364 12.1791 25.6364 15C25.6364 17.8209 24.5158 20.5263 22.521 22.521C20.5263 24.5157 17.8209 25.6364 15 25.6364H7.48068C6.84041 25.6364 6.41411 25.6358 6.08394 25.6154C5.76328 25.5955 5.61167 25.56 5.51549 25.5243L5.10408 26.6312L5.51674 25.5247C5.27864 25.4359 5.06241 25.297 4.88272 25.1173C4.70303 24.9376 4.56407 24.7214 4.47527 24.4833C4.43962 24.3869 4.40442 24.2354 4.38464 23.9166C4.36417 23.5866 4.36364 23.1604 4.36364 22.5193V15C4.36364 12.1791 5.48425 9.47366 7.47895 7.47895C9.47366 5.48425 12.1791 4.36364 15 4.36364ZM24.1924 5.80761C21.7544 3.36964 18.4478 2 15 2C11.5522 2 8.24558 3.36964 5.80761 5.80761C3.36964 8.24558 2 11.5522 2 15V22.5552C1.99999 23.151 1.99998 23.651 2.02554 24.0629C2.0522 24.4928 2.10985 24.9035 2.26018 25.308L2.26064 25.3092C2.46786 25.8648 2.79209 26.3693 3.21138 26.7886C3.63066 27.2079 4.13519 27.5321 4.69076 27.7394L4.69201 27.7398C5.09663 27.8902 5.50786 27.9478 5.93766 27.9745C6.34974 28 6.84977 28 7.44503 28H15C18.4478 28 21.7544 26.6304 24.1924 24.1924C26.6304 21.7544 28 18.4478 28 15C28 11.5522 26.6304 8.24558 24.1924 5.80761Z" fill="#8D8D91"/>
//   <path fill-rule="evenodd" clip-rule="evenodd" d="M20.6133 13.5226C20.6133 12.8699 20.0842 12.3408 19.4315 12.3408H10.5678C9.91513 12.3408 9.38601 12.8699 9.38601 13.5226C9.38601 14.1753 9.91513 14.7045 10.5678 14.7045H19.4315C20.0842 14.7045 20.6133 14.1753 20.6133 13.5226ZM16.1815 19.4317C16.1815 18.779 15.6523 18.2499 14.9996 18.2499H10.5678C9.91513 18.2499 9.38601 18.779 9.38601 19.4317C9.38601 20.0844 9.91513 20.6135 10.5678 20.6135H14.9996C15.6523 20.6135 16.1815 20.0844 16.1815 19.4317Z" fill="#8D8D91"/>
// </svg>
