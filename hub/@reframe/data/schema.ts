import * as t from "./t.ts";

const user = t
  .table({
    id: t.number(),
    name: t.string(),
    email: t.string(),
    active: t.union([t.boolean(), t.null()]),
    createdAt: t.date(),
    metadata: t.json(),
  })
  .primary("id")
  .unique("email")
  .index("createdAt")
  .index("active");

const org = t
  .table({
    id: t.number(),
    slug: t.string(),
    ownerId: t.number(),
    name: t.string(),
    createdAt: t.date(),
  })
  .primary("id")
  .unique("slug")
  .index("ownerId");

const membership = t
  .table({
    orgId: t.number(),
    userId: t.number(),
    role: t.union([
      t.literal("admin"),
      t.literal("editor"),
      t.literal("viewer"),
    ]),
  })
  .primary("orgId", "userId")
  .index("orgId")
  .index("userId")
  .index("role");

const channel = t
  .table({
    id: t.number(),
    orgId: t.number(),
    name: t.string(),
    createdAt: t.date(),
  })
  .primary("id")
  .unique("orgId", "name")
  .index("orgId");

const message = t
  .table({
    id: t.number(),
    channelId: t.number(),
    threadId: t.number(),
    repliedToId: t.number(),
    userId: t.number(),
    text: t.string(),
    reactionsCount: t.json(),
    createdAt: t.date(),
  })
  .primary("id")
  .index("channelId")
  .index("threadId")
  .index("repliedToId")
  .index("userId");

const reaction = t
  .table({
    emoji: t.string(),
    messageId: t.number(),
    userId: t.number(),
    createdAt: t.date(),
  })
  .primary("emoji", "messageId", "userId")
  .index("messageId")
  .index("userId");

const test = t.table({
  a: t.string(),
  b: t.number(),
  c: t.date(),
})
  .primary("a");

const db = t
  .db({
    user,
    org,
    membership,
    channel,
    message,
    reaction,
    test,
  })
  .auth({
    schema: t.object({
      user: t.object({
        id: t.number(),
        email: t.string(),
        role: t.union([
          t.literal("admin"),
          t.literal("user"),
          t.literal("anonymous"),
        ]),
      }),
    }),
    encode: async (auth) => JSON.stringify(auth),
    decode: async (token) => JSON.parse(token),
  })
  .relations({
    user: (t) => ({
      orgs: t.org.many((user) => ({ ownerId: user.id })),
      messages: t.message.many((user) => ({ userId: user.id })),
      reactions: t.reaction.many((user) => ({ userId: user.id })),
      memberships: t.membership.many((user) => ({ userId: user.id })),
    }),

    org: (t) => ({
      owner: t.user.one((org) => ({ id: org.ownerId })),
      channels: t.channel.many((org) => ({ orgId: org.id })),
      memberships: t.membership.many((org) => ({ orgId: org.id })),

      membership: t.membership.one((org, auth) => ({
        orgId: org.id,
        userId: auth.user.id,
      })),
    }),

    membership: (t) => ({
      org: t.org.one((membership) => ({ id: membership.orgId })),
      user: t.user.one((membership) => ({ id: membership.userId })),
    }),

    channel: (t) => ({
      org: t.org.one((channel) => ({ id: channel.orgId })),
      messages: t.message.many((channel) => ({ channelId: channel.id })),
    }),

    message: (t) => ({
      channel: t.channel.one((message) => ({ id: message.channelId })),
      user: t.user.one((message) => ({ id: message.userId })),
      thread: t.message.one((message) => ({ id: message.threadId })),
      repliedTo: t.message.one((message) => ({ id: message.repliedToId })),
      reactions: t.reaction.many((message) => ({ messageId: message.id })),
    }),

    test: (t) => ({
      user: t.user.one((test) => ({ id: test.b })),
      org: t.org.one((test) => ({ id: test.b })),
    }),
  })
  .permissions({
    test: {
      read: (auth, row) =>
        t.and(
          t.eq(row.c, new Date()),
          t.eq(row.a, "1"),
          t.isNotNull(row.user),
          t.isNotNull(row.org.id),
          t.isNotNull(row.org.ownerId),
          t.isNotNull(row.org.membership.user.email),
          t.isNotNull(row.org.membership.user.name),
          t.isNotNull(row.org.membership.org.membership),
        ),
    },
    user: {
      create: (auth, data) => t.eq(auth.user.role, "admin"),

      read: (auth, row) =>
        t.and(
          t.or(
            t.eq(auth.user.role, "admin"),
            t.eq(row.id, auth.user.id),
          ),
          t.eq(row.active, true),
        ),

      update: (auth, row, update) =>
        t.or(
          t.eq(auth.user.role, "admin"),
          t.and(
            t.eq(row.id, auth.user.id),
            // TODO: implement column level permissions
            // t.doesNotExist(update.email),
            // t.doesNotExist(update.createdAt),
            // t.doesNotExist(update.active),
          ),
        ),

      delete: (auth, row) => t.or(t.eq(auth.user.role, "admin")),
    },
    org: {
      create: (auth, data) =>
        t.and(
          t.eq(data.ownerId, auth.user.id),
          // TODO: implement column level permissions
          // t.doesNotExist(data.id),
        ),

      read: (auth, row) =>
        t.or(
          t.isNotNull(row.owner),
          t.and(
            t.eq(row.membership.userId, auth.user.id),
            t.isNotNull(row.membership),
          ),
        ),

      update: (auth, row, update) =>
        t.or(
          t.isNotNull(row.owner.id),
          t.eq(row.membership.role, "admin"),
          t.and(
            t.eq(row.ownerId, auth.user.id),
            // TODO: implement column level permissions
            // t.doesNotExist(update.id),
            // t.doesNotExist(update.createdAt),
          ),
        ),

      delete: (auth, row) =>
        t.or(
          t.eq(auth.user.role, "admin"),
          t.eq(row.ownerId, auth.user.id),
        ),
    },
  });

export { db };
