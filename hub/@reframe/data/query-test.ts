import { t as s } from "@reframe/shapes/main.ts";
import * as t from "./t.ts";

import { db } from "./schema.ts";
import { combine, createReference, from, joinable } from "./query.ts";

const org = from(db.tables.org, "org");
const user = from(db.tables.user, "user");
const membership = from(db.tables.membership, "membership");

const query = org
  .joinOne(
    "owner",
    user,
    (owner, org) => t.eq(owner.id, org.ownerId),
  )
  .joinOne(
    "membership",
    membership
      .select((m) => ({
        yooo: m.userId,
        ...m,
      })),
    (membership, org) =>
      t.and(
        t.eq(org.id, membership.orgId),
        t.eq(org.owner.id, membership.userId),
      ),
  )
  .joinMany(
    "members",
    user
      .joinOne(
        "membership",
        membership.where((m) => t.neq(m.role, "editor")),
        (membership, user) => t.and(t.eq(membership.userId, user.id)),
      ),
    (user, org) => t.and(t.eq(user.membership.orgId, org.id)),
  )
  .select((org) => ({
    ...org,
    foo: {
      c: org.owner.id,
      d: org.id,
    },
  }))
  .select((org) => ({
    ...org,
    bar: combine(org.foo),
  }))
  .where((org) =>
    t.or(
      t.isNotNull(org.membership.role),
      t.and(t.isNotNull(org.name), t.isNotNull(org.membership.yooo)),
    )
  );

const args = new Map();

const sql = t.compileSql(
  t.compile(
    joinable(createReference(query), {}).select(combine),
    {
      scope: t.createScope(["t"]),
      args,
    },
  ),
);

console.log(sql);

import { client } from "./test/nested-read-raw.ts";

console.log(await client.execute(`explain query plan ${sql}`));

const schema = t.jsonSchema(
  t.object({
    id: t.number(),
    slug: t.string(),
    ownerId: t.number(),
    name: t.string(),
    createdAt: t.date(),
    owner: t.object({
      id: t.number(),
      name: t.string(),
      email: t.string(),
      active: t.boolean(),
      createdAt: t.date(),
      metadata: t.json(),
    }),
    membership: t.object({
      userId: t.number(),
      orgId: t.number(),
      role: t.string(),
      yooo: t.number(),
    }),
    members: t.jsonSchema(
      t.array(
        t.object({
          id: t.number(),
          name: t.string(),
          email: t.string(),
          active: t.boolean(),
          createdAt: t.date(),
          metadata: t.json(),
          membership: t.object({
            userId: t.number(),
            orgId: t.number(),
            role: t.string(),
          }),
        }),
      ),
    ),
    foo: t.object({
      c: t.number(),
      d: t.number(),
    }),
    bar: t.jsonSchema(t.object({
      c: t.number(),
      d: t.number(),
    })),
  }),
);

const rows = await client.execute({ sql, args: Object.fromEntries(args) });
console.log({ args: Object.fromEntries(args) });
for (const row of rows.rows) {
  console.log(JSON.parse(row["t_$"]));
  const json = schema.read(row["t_$"]);
  console.log(json);
}
