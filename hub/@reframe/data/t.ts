import {
  Client as LibsqlClient,
  Config,
  createClient,
  Value as LibsqlValue,
} from "npm:@libsql/client/node";

import {
  Compilable,
  COMPILE,
  createScope,
  Expression,
} from "./compiler/core.ts";

export {
  and,
  eq,
  gt,
  gte,
  is,
  isNot,
  isNotNull,
  isNull,
  like,
  lt,
  lte,
  neq,
  or,
} from "./compiler/operator.ts";

export { db } from "./schema/db.ts";
export { table } from "./schema/table.ts";

type Accessed<T> = {
  [K in keyof T]?: Accessed<T[K]>;
};

const whereToPredicates = <
  T extends s.Shape,
  Q extends Where<s.Type<T>>,
>(
  lhs: Variable<T>,
  rhs: Q,
): Expression<boolean>[] => {
  const predicates = [] as Expression<boolean>[];

  for (const [key, value] of Object.entries(rhs)) {
    switch (key) {
      case "$neq":
        predicates.push(neq(
          lhs as Expression<
            boolean | number | string | null
          >,
          value as string | number | boolean | null,
        ));
        break;

      case "$isNotNull":
        predicates.push(
          isNotNull(
            lhs as Expression<
              string | number | boolean | null
            >,
          ),
        );

        break;

      case "$gt":
        predicates.push(gt(
          lhs as Expression<
            number | string | null
          >,
          value as number | string,
        ));
        break;

      case "$lt":
        predicates.push(lt(
          lhs as Expression<
            number | string | null
          >,
          value as number | string,
        ));
        break;

      case "$gte":
        predicates.push(gte(
          lhs as Expression<
            number | string | null
          >,
          value as number | string,
        ));
        break;

      case "$lte":
        predicates.push(lte(
          lhs as Expression<
            number | string | null
          >,
          value as number | string,
        ));
        break;

      case "$like":
        predicates.push(like(
          lhs as Expression<string | null>,
          value as string,
        ));
        break;

      default:
        if (
          typeof value === "string" || typeof value === "number" ||
          typeof value === "boolean"
        ) {
          predicates.push(eq(
            Reflect.get(lhs, key as string) as Expression<
              boolean | number | string | null
            >,
            value,
          ));

          continue;
        }

        if (value === null) {
          predicates.push(isNull(Reflect.get(lhs, key as string) as Expression<
            string | null
          >));

          continue;
        }

        predicates.push(
          ...whereToPredicates(
            Reflect.get(lhs, key as string) as Variable<any>,
            Reflect.get(rhs, key as string) as Where<any>,
          ),
        );
        break;
    }
  }

  return predicates;
};

export * from "./schema/data-types.ts";

export const createVariable = <T extends Variable<any>>(
  name: string,
  path: string = "$",
) => {
  const accessed = new Map<string, unknown>();

  return new Proxy(
    {},
    {
      has: (_, key) =>
        [
          COMPILE,
          s.KIND,
        ].includes(key as any),

      get: (_, key) => {
        if (key === COMPILE) {
          return () => ({
            sql: `(${name} ->> '${path}')`,
            args: {},
          });
        }

        if (key === "$accesed") {
          const $accessed = {};

          for (const [key, value] of accessed.entries()) {
            Reflect.set($accessed, key, (value as any).$accesed);
          }

          return $accessed;
        }

        if (typeof key === "string") {
          if (!accessed.has(key)) {
            accessed.set(key, createVariable(name, `${path}.${key}`));
          }

          return accessed.get(key);
        }

        return undefined;
      },
    },
  ) as T;
};

export const db_ = <
  Tables extends Record<string, Table>,
>(tables: Tables) => ({
  auth: <Auth extends s.Shape>(auth: {
    schema: Auth;
    encode: (auth: s.Type<Auth>) => Promise<string>;
    decode: (auth: string) => Promise<s.Out<Auth>>;
  }) => ({
    relations: <
      Relations extends DBRelations<Tables, Auth>,
    >(
      relationsFn: (
        t: {
          [U in keyof Tables]: Tables[U] & {
            many: (query: JoinMany<Tables[U]>) => Many<Tables, U>;
            one: (query: JoinOne<Tables[U]>) => One<Tables, U>;
          };
        },
      ) => Relations,
    ) => ({
      permissions: <Permissions extends DBPermissions<Tables, Auth, Relations>>(
        permissions: Permissions,
      ): DB<Tables, Auth, Relations, Permissions> => {
        const t = {} as {
          [U in keyof Tables]: Tables[U] & {
            many: (query: JoinMany<Tables[U]>) => Many<Tables, U>;
            one: (query: JoinOne<Tables[U]>) => One<Tables, U>;
          };
        };

        for (const [_name, _table] of Object.entries(tables)) {
          const name = _name as keyof Tables;
          const table = _table as Tables[typeof name];
          t[name] = {
            ...table,
            one: (query) => ({
              type: "one",
              table: name,
              join: query,
            }),
            many: (query) => ({
              type: "many",
              table: name,
              join: query,
            }),
          };
        }

        const relations = relationsFn(t);

        return {
          tables,
          auth,
          relations,
          permissions,
          connect: (config) => {
            const $client = createClient(config);

            return {
              signUp: async (payload, opts) => {
                const token = await auth.encode(payload);
                return token;
              },
              signIn: async (token) => {
                const $auth = await auth.decode(token);

                const client = {
                  $auth: $auth,
                  $client,
                };

                const createTableClient = <T extends keyof Tables & string>(
                  tableName: T,
                ): TableClient<Tables, Auth, Relations, T> => {
                  const table = tables[tableName];
                  const schema = s.object<Tables[T]["columns"]>(
                    table.columns,
                  );

                  const scope = createScope(["todo"]);

                  return ({
                    create: async (data) => {
                      const encoded = schema.write(data);

                      const tokens = { data: `data${counter()}` };

                      const columnNames = Object.keys(table.columns)
                        .join(", ");

                      const columnValues = Object.keys(table.columns)
                        .map((key) => `(:data ->> '$.${key}')`)
                        .join(", ");

                      const permission = permissions[tableName]?.create ??
                        (() => ({
                          [COMPILE]: () => ({ sql: "false", args: {} }),
                        }));

                      const permissionClause = permission(
                        createVariable(":auth"),
                        createVariable(":data"),
                      ).$compile({
                        indent: 0,
                        counter,
                      });

                      const sql = `
                        with ${tokens.data} (${columnNames}) as (
                          values (${columnValues})
                        )
                        insert into ${tableName}
                        select ${tokens.data}.* from ${tokens.data}
                        where ${permissionClause.sql}
                        returning *
                      `;

                      console.log(sql);
                      console.log(
                        {
                          ...permissionClause.args,
                          data: JSON.stringify(encoded),
                          auth: JSON.stringify($auth),
                        },
                      );

                      const { rows } = await $client.execute({
                        sql,
                        args: {
                          ...permissionClause.args,
                          data: JSON.stringify(encoded),
                          auth: JSON.stringify($auth),
                        },
                      });

                      if (rows.length === 0) {
                        // when permission is denied
                        throw new Error(`failed to insert: permission denied`);
                      }

                      return schema.read(
                        rows[0]! as {
                          [K in keyof Tables[T]["columns"]]: s.In<
                            Tables[T]["columns"][K]
                          >;
                        },
                      );
                    },

                    readMany: async (query) => {
                      console.log(query);
                      // here we build the query
                      // convert query into table format
                      // and also create the schema
                      return compileReadManyQuery(
                        tables,
                        auth.schema,
                        relations,
                        permissions,
                        tableName,
                        query,
                      );

                      const columnNames = Object.keys(table.columns)
                        .join(", ");

                      const columnValues = Object.keys(table.columns)
                        .map((key) => `(:data ->> '$.${key}')`)
                        .join(", ");

                      const permission = permissions[tableName]?.read ??
                        (() => ({
                          [COMPILE]: () => ({ sql: "false", args: {} }),
                        }));

                      const permissionClause = permission(
                        createVariable(":auth"),
                        createVariable("row"),
                      ).$compile({
                        indent: 0,
                        counter,
                      });

                      // const sql = `
                      //   with ${tokens.data} (${columnNames}) as (
                      //     values (${columnValues})
                      //   )
                      //   insert into ${tableName}
                      //   select ${tokens.data}.* from ${tokens.data}
                      //   where ${permissionClause.sql}
                      //   returning *
                      // `;

                      const sql = `
                        

                        
                          select json_object('id', id, 'email', email) as "data"
                          from user
                      `;

                      console.log(sql);

                      console.log(
                        {
                          ...permissionClause.args,
                          auth: JSON.stringify($auth),
                        },
                      );

                      const { rows } = await $client.execute({
                        sql,
                        args: {
                          ...permissionClause.args,
                          auth: JSON.stringify($auth),
                        },
                      });

                      console.log(rows);

                      // if (rows.length === 0) {
                      //   // when permission is denied
                      //   throw new Error(`failed to insert: permission denied`);
                      // }

                      return schema.read(
                        rows[0]! as {
                          [K in keyof Tables[T]["columns"]]: s.In<
                            Tables[T]["columns"][K]
                          >;
                        },
                      );
                    },
                  });
                };

                for (const [tableName, table] of Object.entries(tables)) {
                  Reflect.set(
                    client,
                    tableName,
                    createTableClient(tableName),
                  );
                }

                return client as Client<Tables, Auth, Relations>;
              },
            };
          },
        };
      },
    }),
  }),
});
