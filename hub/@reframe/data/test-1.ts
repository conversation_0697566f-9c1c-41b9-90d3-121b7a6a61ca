import { db } from "./schema.ts";
import { t } from "@reframe/shapes/main.ts";

try {
  const connection = db
    .connect({ url: ":memory:" });

  const token = await connection
    .signUp({
      user: {
        id: 1,
        email: "",
        role: "admin",
      },
    });

  const client1 = await connection.signIn(token);

  const result1 = await client1.$client.executeMultiple(`
    create table user (
      id integer not null,
      name text not null,
      email text not null,
      active boolean,
      createdAt integer not null,
      metadata json not null,
      primary key (id)
    );

    create unique index user_email on user(email);
    create index user_createdAt on user(createdAt);
    create index user_active on user(active);
  `);

  const createUser1 = await client1.user.create({
    id: 1,
    name: "Alice",
    email: "<EMAIL>",
    active: true,
    createdAt: new Date(),
    metadata: {},
  });
  console.log({ createUser1 });

  const createUser2 = await client1.user.create({
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    active: false,
    createdAt: new Date(),
    metadata: { occupation: "builder" },
  });

  console.log({ createUser2 });

  const readUser1 = await client1.user.read();

  console.log({ readUser1 });

  // const { rows: createUser } = await client.$client.execute({
  //   sql: `
  //   with r99(id, slug, ownerId, name, createdAt) as (
  //     values (:id, :slug, :ownerId, :name, :createdAt)
  //   )
  //   insert into org
  //   select r99.* from r99
  //   join user on user.id = r99.ownerId
  //   where user.id = :$auth$user$id
  //   returning *
  //   `,
  //   args: {
  //     id: 1,
  //     slug: "foo",
  //     ownerId: 1,
  //     name: "bar",
  //     createdAt: Date.now(),
  //     $$auth$user$id: 1,
  //   },
  // });

  const { rows: users } = await client.$client.execute(`select * from user`);
  // const { rows: orgs } = await client.$client.execute(`select * from org`);
  // const { rows: apps } = await client.$client.execute(`select * from app`);

  console.log({ users });

  // console.log({ createOrg });

  // const { rows: createApp } = await client.$client.execute({
  //   sql: `
  //     with data(id, orgId, name, createdAt) as (
  //       values (:id, :orgId, :name, :createdAt)
  //     )
  //     insert into app
  //     select data.* from data
  //     join org on org.id = data.orgId
  //     join user on user.id = org.ownerId

  //     where user.id = :$auth$user$id

  //     returning *
  //     `,
  //   args: {
  //     id: 2,
  //     orgId: 1,
  //     name: "baz",
  //     createdAt: Date.now(),
  //     $$auth$user$id: 1,
  //   },
  // });

  // console.log({ createApp });

  // await client.$client.execute({
  //   sql: `insert into user (
  //       id, name, email, createdAt, boolean, metadata
  //     ) values (
  //      :id, :name, :email, :createdAt, :boolean, :metadata
  //     )`,
  //   args: {
  //     id: 1,
  //     name: "foo",
  //     email: "<EMAIL>",
  //     createdAt: Date.now(),
  //     boolean: true,
  //     metadata: JSON.stringify({
  //       foo: "bar",
  //       hello: "world",
  //       values: [1, 2, 3],
  //     }),
  //   },
  // });

  // await client.$client.execute({
  //   sql: `insert into org (
  //       id, slug, ownerId, name, createdAt
  //     ) values (
  //      :id, :slug, :ownerId, :name, :createdAt
  //     )`,
  //   args: {
  //     id: 1,
  //     slug: "foo",
  //     ownerId: 1,
  //     name: "bar",
  //     createdAt: Date.now(),
  //   },
  // });

  // const r1 = await client.user.read(); // returns all rows with types

  // console.log(r1);

  // // .unique("slug")
  // // .unique("ownerId", "name")
  // // { id } | { slug } | { ownerId, name }

  // const r2 = await client.org
  //   .unique({ slug: "foo" });

  // console.log(r2);
} catch (error) {
  console.error(error, error.stack);
}
