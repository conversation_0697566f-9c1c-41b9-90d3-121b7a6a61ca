import { expect } from "jsr:@std/expect";
import { db } from "./schema.ts";

const connection = db.connect({ url: ":memory:" });

const admin = await connection
  .signUp({
    user: {
      id: 1,
      email: "<EMAIL>",
      role: "admin",
    },
  });

const yoongi = await connection
  .signUp({
    user: {
      id: 2,
      email: "<EMAIL>",
      role: "user",
    },
  });

const minju = await connection
  .signUp({
    user: {
      id: 3,
      email: "<EMAIL>",
      role: "user",
    },
  });

const newyork = await connection
  .signUp({
    user: {
      id: 4,
      email: "<EMAIL>",
      role: "anonymous",
    },
  });

//// **** user ****///

// u1. dj as an admin, can create a yoongi user
Deno.test("create user as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds yoongi to db", async () => {
    const createUser = await client.user.create({
      id: 2,
      name: "Yoong<PERSON>",
      email: "<EMAIL>",
      active: true,
      createdAt: new Date(),
      metadata: {},
    });

    expect(createUser).toMatchObject({
      id: 2,
      name: "Yoongi",
      email: "<EMAIL>",
      active: true,
      createdAt: new Date(),
      metadata: {},
    });
  });

  await t.step("reads yoongi from db", async () => {
    const yoongi = await client.user.read();

    expect(yoongi).toMatchObject([
      {
        id: 2,
        name: "Yoongi",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
    ]);
  });
});

// u2. dj as an admin, can create multiple users (minju, newyork)

Deno.test("create multiple users as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds multiple users to db", async () => {
    const createUser = await client.user.create([
      {
        id: 3,
        name: "Minju",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
      {
        id: 4,
        name: "Newyork",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
    ]);

    expect(createUser).toMatchObject([
      {
        id: 3,
        name: "Minju",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
      {
        id: 4,
        name: "Newyork",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
    ]);
  });
});

// u3. yoongi as an user, cannot create a user

Deno.test("create user as an user", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("adds minju to db as yoongi", async () => {
    try {
      await client.user.create({
        id: 3,
        name: "Minju",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// u4. dj as admin, can not create a user with same email as yoongi

Deno.test("create user with same email as existing user", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds minjus email to db when exists", async () => {
    try {
      await client.user.create({
        id: 3,
        name: "Minju",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("user with email already exists");
    }
  });
});

// u5. dj as an admin, can read all users

Deno.test("reads all users as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("reads all users from db", async () => {
    const users = await client.user.read();

    expect(users).toMatchObject([
      {
        id: 1,
        name: "Admin",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
      {
        id: 2,
        name: "Yoongi",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
      {
        id: 3,
        name: "Minju",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
      {
        id: 4,
        name: "Newyork",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
    ]);
  });
});

// u6. dj as an admin, can read a single user

Deno.test("reads single user as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("reads yoongi from db", async () => {
    const yoongi = await client.user.unique({ id: 2 });

    expect(yoongi).toMatchObject(
      {
        id: 2,
        name: "Yoongi",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
    );
  });
});

// u6. yoongi as an user, can read own user

Deno.test("reads own user as an user", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("reads yoongi from db as yoongi", async () => {
    const yoongi = await client.user.read();

    expect(yoongi).toMatchObject(
      {
        id: 2,
        name: "Yoongi",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
    );
  });
});

// u7. yoongi as an user, cannot read another user minju

Deno.test("reads another user as an user", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("reads minju from db as yoongi", async () => {
    try {
      await client.user.unique({ id: 3 });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// u8. dj as an admin, can update yoongi user without unique fields

Deno.test("updates user as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("updates yoongi on db as admin", async () => {
    const updateUser = await client.user.update(
      { id: 2 },
      { name: "Yoongi Gadha", active: false },
    );

    expect(updateUser).toMatchObject(
      {
        id: 2,
        name: "Yoongi Gadha",
        email: "<EMAIL>",
        active: false,
        createdAt: new Date(),
        metadata: {},
      },
    );
  });
});

// u9. dj as an admin, can update yoongi user unique fields but cannot use duplicate unique field values

Deno.test("updates user as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("updates yoongi from db", async () => {
    try {
      await client.user.update(
        { id: 2 },
        { email: "<EMAIL>" },
      );
      throw new Error("this is not allowed");
    } catch (error) {
      expect(error.message).toEqual("user with email already exists");
    }
  });
});

// u10. dj as an admin, can update multiple users non unique fields

Deno.test("updates multiple users as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("updates multiple users from db", async () => {
    const updateUsers = await client.user.updateMany([
      { id: 3 },
      { active: false },
      { id: 4 },
      { active: true },
    ]);

    expect(updateUsers).toMatchObject([
      {
        id: 3,
        name: "Minju",
        email: "<EMAIL>",
        active: false,
        createdAt: new Date(),
        metadata: {},
      },
      {
        id: 4,
        name: "Newyork",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {},
      },
    ]);
  });
});

// u11. yoongi as an user, can update own user without unique fields

Deno.test("updates own user non unique fields", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates own non unique fields", async () => {
    const updateUser = await client.user.update(
      { id: 2 },
      {
        name: "Yoongi Neka",
        active: true,
        metadata: {
          occupation: "singer",
        },
      },
    );

    expect(updateUser).toMatchObject(
      {
        id: 2,
        name: "Yoongi Neka",
        email: "<EMAIL>",
        active: true,
        createdAt: new Date(),
        metadata: {
          occupation: "singer",
        },
      },
    );
  });
});

// u12. yoongi as an user, can not update own user unique fields

Deno.test("updates own user unique fields", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates own email", async () => {
    try {
      await client.user.update(
        { id: 2 },
        { email: "blahblah" },
      );
      throw new Error("cannot update unique fields");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// u13. yoongi as an user, cannot update another user minju

Deno.test("updates another user", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates minju on db as yoongi", async () => {
    try {
      await client.user.update(
        { id: 3 },
        { active: true },
      );
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// u14. dj as an admin, can delete yoongi user if no orgs

Deno.test("deletes user as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("deletes yoongi from db", async () => {
    const deleteUser = await client.user.delete({ id: 2 });

    expect(deleteUser).toMatchObject(
      {
        id: 2,
        name: "Yoongi Neka",
        email: "",
        active: true,
        createdAt: new Date(),
        metadata: {
          occupation: "singer",
        },
      },
    );
  });
});

// u15. dj as an admin, can delete yoongi user with orgs (not allowed)

Deno.test("deletes user with orgs as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("deletes yoongi with orgs from db", async () => {
    try {
      await client.user.delete({ id: 2 });
      throw new Error("This action should not be allowed");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// u16. yoongi as an user, can delete own user only if no orgs

Deno.test("deletes own user without orgs as an user", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes self from db", async () => {
    const deleteUser = await client.user.delete({ id: 2 });

    expect(deleteUser).toMatchObject(
      {
        id: 2,
        name: "Yoongi Neka",
        email: "",
        active: true,
        createdAt: new Date(),
        metadata: {
          occupation: "singer",
        },
      },
    );
  });
});

// u17. yoongi as an user, can delete own user with orgs(not allowed)

Deno.test("deletes own user with orgs as an user", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes self with orgs from db", async () => {
    try {
      await client.user.delete({ id: 2 });
      throw new Error("This action should not be allowed");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// u18. yoongi as an user, cannot delete another user minju

Deno.test("deletes another user as an user", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes minju from db as yoongi", async () => {
    try {
      await client.user.delete({ id: 3 });
      throw new Error("should not be allowed");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

//// **** user ****///

//recreate the users

Deno.test("recreate deleted users", async (t) => {
  const client = await connection.signIn(admin);

  const createUser = await t.step(
    "adds multiple users to db for further tests",
    async () => {
      const createUser = await client.user.create([
        {
          id: 2,
          name: "Yoongi",
          email: "<EMAIL>",
          active: true,
          createdAt: new Date(),
          metadata: {},
        },
        {
          id: 3,
          name: "Minju",
          email: "<EMAIL>",
          active: true,
          createdAt: new Date(),
          metadata: {},
        },
        {
          id: 4,
          name: "Newyork",
          email: "<EMAIL>",
          active: true,
          createdAt: new Date(),
          metadata: {},
        },
      ]);

      expect(createUser).toMatchObject([
        {
          id: 3,
          name: "Minju",
          email: "<EMAIL>",
          active: true,
          createdAt: new Date(),
          metadata: {},
        },
        {
          id: 4,
          name: "Newyork",
          email: "<EMAIL>",
          active: true,
          createdAt: new Date(),
          metadata: {},
        },
      ]);
    },
  );
});

//// **** org **** ////

// o1. create single org as yoongi user
Deno.test("creates org as an user", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds yoongi's org to db", async () => {
    const createOrg = await client.org.create({
      id: 1,
      ownerId: 2,
      name: "Yoongis org",
      slug: "yoongis-org",
      createdAt: new Date(),
    });

    expect(createOrg).toMatchObject({
      id: 1,
      ownerId: 2,
      name: "Yoongis org",
      slug: "yoongis-org",
      createdAt: new Date(),
    });
  });
});

// o2. create multiple orgs as yoongi user
Deno.test("creates multiple orgs as an user", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds yoongi's org to db", async () => {
    const createOrg = await client.org.create([
      {
        id: 2,
        ownerId: 2,
        name: "yoongis org-1",
        slug: "yoongis-org-1",
        createdAt: new Date(),
      },
      {
        id: 3,
        ownerId: 2,
        name: "Yoongis org-2",
        slug: "yoongis-org-2",
        createdAt: new Date(),
      },
    ]);

    expect(createOrg).toMatchObject([
      {
        id: 2,
        ownerId: 2,
        name: "yoongis org-1",
        slug: "yoongis-org-1",
        createdAt: new Date(),
      },
      {
        id: 3,
        ownerId: 2,
        name: "Yoongis org-2",
        slug: "yoongis-org-2",
        createdAt: new Date(),
      },
    ]);
  });
});

// o3. create single org for yoongi user as minju user(not allowed)

Deno.test("creates org for another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("adds yoongi's org to db as minju", async () => {
    try {
      await client.org.create({
        id: 4,
        ownerId: 2,
        name: "Yoongis org-3",
        slug: "yoongis-org-3",
        createdAt: new Date(),
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// o4. create an already existing org for yoongi user(not allowed)

Deno.test("creates already existing org", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds yoongi's org to db", async () => {
    try {
      await client.org.create({
        id: 5,
        ownerId: 2,
        name: "Yoongis org-2",
        slug: "yoongis-org-2",
        createdAt: new Date(),
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("org with slug already exists");
    }
  });
});

// o5. create single org for yoongi user as dj admin

Deno.test("creates single org for a user as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds yoongi's org to db as admin", async () => {
    const createOrg = await client.org.create({
      id: 4,
      ownerId: 2,
      name: "Yoongis org-5",
      slug: "yoongis-org-5",
      createdAt: new Date(),
    });

    expect(createOrg).toMatchObject({
      id: 4,
      ownerId: 2,
      name: "Yoongis org-5",
      slug: "yoongis-org-5",
      createdAt: new Date(),
    });
  });
});

// o6. read yoongis-org as yoongi user

Deno.test("reads single org as an user", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("reads yoongi's org from db", async () => {
    const org = await client.org.unique({ slug: "yoongis-org" });

    expect(org).toMatchObject(
      {
        id: 1,
        ownerId: 2,
        name: "Yoongis org",
        slug: "yoongis-org",
        createdAt: new Date(),
      },
    );
  });
});

// o7. read all yoongi's orgs as yoongi user

Deno.test("reads all orgs as an user", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("reads all orgs from db", async () => {
    const orgs = await client.org.read();

    expect(orgs).toMatchObject([
      {
        id: 1,
        ownerId: 2,
        name: "Yoongis org",
        slug: "yoongis-org",
        createdAt: new Date(),
      },
      {
        id: 2,
        ownerId: 2,
        name: "yoongis org-1",
        slug: "yoongis-org-1",
        createdAt: new Date(),
      },
      {
        id: 3,
        ownerId: 2,
        name: "Yoongis org-2",
        slug: "yoongis-org-2",
        createdAt: new Date(),
      },
      {
        id: 4,
        ownerId: 2,
        name: "Yoongis org-5",
        slug: "yoongis-org-5",
        createdAt: new Date(),
      },
    ]);
  });
});

// o8. read yoongis-org as minju user(not allowed)

Deno.test("reads single org as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("reads yoongi's org from db as minju", async () => {
    try {
      await client.org.unique({ slug: "yoongis-org" });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// o9. read all yoongi's orgs as dj admin

Deno.test("reads all orgs as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("reads all orgs from db as admin", async () => {
    const orgs = await client.org.read({
      ownerId: 2,
    });

    expect(orgs).toMatchObject([
      {
        id: 1,
        ownerId: 2,
        name: "Yoongis org",
        slug: "yoongis-org",
        createdAt: new Date(),
      },
      {
        id: 2,
        ownerId: 2,
        name: "yoongis org-1",
        slug: "yoongis-org-1",
        createdAt: new Date(),
      },
      {
        id: 3,
        ownerId: 2,
        name: "Yoongis org-2",
        slug: "yoongis-org-2",
        createdAt: new Date(),
      },
      {
        id: 4,
        ownerId: 2,
        name: "Yoongis org-5",
        slug: "yoongis-org-5",
        createdAt: new Date(),
      },
    ]);
  });
});

// o10. read all orgs as dj admin
Deno.test("reads all orgs as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("reads all orgs from db as admin", async () => {
    const orgs = await client.org.read();

    expect(orgs).toMatchObject([
      {
        id: 1,
        ownerId: 2,
        name: "Yoongis org",
        slug: "yoongis-org",
        createdAt: new Date(),
      },
      {
        id: 2,
        ownerId: 2,
        name: "yoongis org-1",
        slug: "yoongis-org-1",
        createdAt: new Date(),
      },
      {
        id: 3,
        ownerId: 2,
        name: "Yoongis org-2",
        slug: "yoongis-org-2",
        createdAt: new Date(),
      },
      {
        id: 4,
        ownerId: 2,
        name: "Yoongis org-5",
        slug: "yoongis-org-5",
        createdAt: new Date(),
      },
    ]);
  });
});

// o11. read shared yoongi-org as minju user
Deno.test("reads shared org as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("reads shared org from db as minju", async () => {
    const orgs = await client.org.read({
      ownerId: 2,
      shared: true,
    });

    expect(orgs).toMatchObject([
      {
        id: 1,
        ownerId: 2,
        name: "Yoongis org",
        slug: "yoongis-org",
        createdAt: new Date(),
      },
    ]);
  });
});

// o12. read (shared with minju but) not shared org as newyork user(not allowed)
Deno.test("reads not shared org as another user", async (t) => {
  const client = await connection.signIn(newyork);

  await t.step("reads not shared org from db as minju", async () => {
    try {
      await client.org.read({
        ownerId: 2,
        shared: false,
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// o13. update yoongis-org non unique fields as yoongi user

Deno.test("updates org as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates yoongi's org from db", async () => {
    const updateOrg = await client.org.update(
      { slug: "yoongis-org-2" },
      { name: "Yoongis org-10" },
    );

    expect(updateOrg).toMatchObject(
      {
        id: 3,
        ownerId: 2,
        name: "Yoongis org-10",
        slug: "yoongis-org-2",
        createdAt: new Date(),
      },
    );
  });
});

// o14. update yoongis-org unique fields as yoongi user (???)

Deno.test("updates org unique fields as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates yoongi's org from db", async () => {
    const updateOrg = await client.org.update(
      { slug: "yoongis-org-2" },
      { slug: "yoongis-org-15" },
    );

    expect(updateOrg).toMatchObject(
      {
        id: 3,
        ownerId: 2,
        name: "Yoongis org-10",
        slug: "yoongis-org-15",
        createdAt: new Date(),
      },
    );
  });
});

// o14. update multiple yoongi's orgs non unique fields as yoongi user

Deno.test("updates all orgs as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates all orgs from db", async () => {
    const updateOrg = await client.org.updateMany([
      { slug: "yoongis-org" },
      { name: "Yoongis org-0" },
      { slug: "yoongis-org-1" },
      { name: "Yoongis org-101" },
    ]);

    expect(updateOrg).toMatchObject([
      {
        id: 1,
        ownerId: 2,
        name: "Yoongis org-0",
        slug: "yoongis-org",
        createdAt: new Date(),
      },
      {
        id: 2,
        ownerId: 2,
        name: "Yoongis org-101",
        slug: "yoongis-org-1",
        createdAt: new Date(),
      },
    ]);
  });
});

// o15. update yoongis-org as minju user(not allowed)

Deno.test("updates org as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("updates yoongi's org from db as minju", async () => {
    try {
      await client.org.update(
        { slug: "yoongis-org-2" },
        { name: "Yoongis org-10" },
      );
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// o16. update yoongis-org (even not shared) as dj admin

Deno.test("updates org as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("updates yoongi's org from db as admin", async () => {
    const updateOrg = await client.org.update(
      { slug: "yoongis-org-5" },
      { name: "Yoongis org-50" },
    );

    expect(updateOrg).toMatchObject(
      {
        id: 4,
        ownerId: 2,
        name: "Yoongis org-50",
        slug: "yoongis-org-5",
        createdAt: new Date(),
      },
    );
  });
});

// o17. update all yoongi's orgs as dj admin

Deno.test("updates all orgs as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("updates all orgs from db as admin", async () => {
    const updateOrg = await client.org.updateMany([
      { slug: "yoongis-org" },
      { name: "Yoongis org-00" },
      { slug: "yoongis-org-1" },
      { name: "Yoongis org-01" },
    ]);

    expect(updateOrg).toMatchObject([
      {
        id: 1,
        ownerId: 2,
        name: "Yoongis org-00",
        slug: "yoongis-org",
        createdAt: new Date(),
      },
      {
        id: 2,
        ownerId: 2,
        name: "Yoongis org-01",
        slug: "yoongis-org-1",
        createdAt: new Date(),
      },
    ]);
  });
});

// o18. update only shared orgs as minju user

Deno.test("updates shared org as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("updates shared org from db as minju", async () => {
    const updateOrg = await client.org.update(
      { slug: "yoongis-org" },
      { name: "Yoongis org" },
    );

    expect(updateOrg).toMatchObject(
      {
        id: 1,
        ownerId: 2,
        name: "Yoongis org",
        slug: "yoongis-org",
        createdAt: new Date(),
      },
    );
  });
});

// o19. update (shared with minju but) not shared orgs as newyork user(not allowed)

Deno.test("updates not shared org as another user", async (t) => {
  const client = await connection.signIn(newyork);

  await t.step("update not shared org from db as ny", async () => {
    try {
      await client.org.update(
        { slug: "yoongis-org-1" },
        { name: "Yoongis org-001" },
      );
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// o20. delete single yoongis-org as yoongi user if no channels

Deno.test("deletes an org without channel as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes yoongi's org from db", async () => {
    const deleteOrg = await client.org.delete({ slug: "yoongis-org-1" });

    expect(deleteOrg).toMatchObject(
      {
        id: 2,
        ownerId: 2,
        name: "Yoongis org-01",
        slug: "yoongis-org-1",
        createdAt: new Date(),
      },
    );
  });
});

// o21. delete yoongis-orgs as yoongi user with channels (not allowed)

Deno.test("deletes an org with channels as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes yoongi's org from db", async () => {
    try {
      await client.org.delete({ slug: "yoongis-org" });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// o22. delete non-shared yoongis-org as minju user(not allowed)

Deno.test("deletes an org as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("deletes yoongi's org from db as minju", async () => {
    try {
      await client.org.delete({ slug: "yoongis-org-1" });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// o23. delete only shared orgs as minju user(not allowed)

Deno.test("deletes shared org as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("deletes shared org from db as minju", async () => {
    try {
      await client.org.delete({
        slug: "yoongis-org",
        shared: true,
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// o24. delete all yoongi's orgs as yoongi user with channels (not allowed)

Deno.test("deletes all orgs with channels as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes all yoongi's orgs from db", async () => {
    try {
      await client.org.delete({ ownerId: 2 });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// o25. deletes single yoongis-org (even not shared) as dj admin if no channels

Deno.test("deletes org as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("deletes yoongi's org from db as admin", async () => {
    const deleteOrg = await client.org.delete({ slug: "yoongis-org-5" });

    expect(deleteOrg).toMatchObject(
      {
        id: 4,
        ownerId: 2,
        name: "Yoongis org-50",
        slug: "yoongis-org-5",
        createdAt: new Date(),
      },
    );
  });
});

// o26. delete (shared with minju but) not shared orgs as newyork user(not allowed)

Deno.test("deletes not shared org as another user", async (t) => {
  const client = await connection.signIn(newyork);

  await t.step("deletes not shared org from db as ny", async () => {
    try {
      await client.org.delete({ slug: "yoongis-org-1" });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// o25. delete all yoongi's orgs as yoongi user if no channels

Deno.test("deletes all orgs without channel as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes all yoongi's orgs from db", async () => {
    const deleteOrg = await client.org.delete({ ownerId: 2 });

    expect(deleteOrg).toMatchObject([
      {
        id: 2,
        ownerId: 2,
        name: "Yoongis org-01",
        slug: "yoongis-org-1",
        createdAt: new Date(),
      },
      {
        id: 3,
        ownerId: 2,
        name: "Yoongis org-10",
        slug: "yoongis-org-2",
        createdAt: new Date(),
      },
    ]);
  });
});

//// **** org **** ////

//recreate the orgs again for testing further

Deno.test("recreate deleted orgs", async (t) => {
  const client = await connection.signIn(admin);

  const createOrg = await t.step(
    "adds multiple orgs to db for further tests",
    async () => {
      const createOrg = await client.org.create([
        {
          id: 1,
          ownerId: 2,
          name: "Yoongis org",
          slug: "yoongis-org",
          createdAt: new Date(),
        },
        {
          id: 2,
          ownerId: 2,
          name: "yoongis org-1",
          slug: "yoongis-org-1",
          createdAt: new Date(),
        },
        {
          id: 3,
          ownerId: 2,
          name: "Yoongis org-2",
          slug: "yoongis-org-2",
          createdAt: new Date(),
        },
        {
          id: 4,
          ownerId: 2,
          name: "Yoongis org-5",
          slug: "yoongis-org-5",
          createdAt: new Date(),
        },
      ]);

      expect(createOrg).toMatchObject([
        {
          id: 2,
          ownerId: 2,
          name: "yoongis org-1",
          slug: "yoongis-org-1",
          createdAt: new Date(),
        },
        {
          id: 3,
          ownerId: 2,
          name: "Yoongis org-2",
          slug: "yoongis-org-2",
          createdAt: new Date(),
        },
        {
          id: 4,
          ownerId: 2,
          name: "Yoongis org-5",
          slug: "yoongis-org-5",
          createdAt: new Date(),
        },
      ]);
    },
  );
});

// **** membership role **** //

// r1. create membership for yoongi as editor in yoongis-org as dj admin

Deno.test("creates membership as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds membership to db as admin", async () => {
    const createMembership = await client.membership.create({
      userId: 2,
      orgId: 1,
      role: "editor",
    });

    expect(createMembership).toMatchObject(
      {
        userId: 2,
        orgId: 1,
        role: "editor",
      },
    );
  });
});

// r2. create membership for minju as viewer in yoongis-org as yoongi user

Deno.test("creates membership as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("adds membership to db as owner", async () => {
    const createMembership = await client.membership.create({
      userId: 3,
      orgId: 1,
      role: "viewer",
    });

    expect(createMembership).toMatchObject(
      {
        userId: 3,
        orgId: 1,
        role: "viewer",
      },
    );
  });
});

// r3. create membership for newyork as viewer in yoongis-org as minju user(not allowed)

Deno.test("creates membership as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("adds membership to db as minju", async () => {
    try {
      await client.membership.create({
        userId: 4,
        orgId: 1,
        role: "viewer",
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// r4. create membership for minju if already exists in yoongis-org

Deno.test("creates existing membership", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds membership to db as admin", async () => {
    try {
      await client.membership.create({
        userId: 3,
        orgId: 1,
        role: "viewer",
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("membership already exists");
    }
  });
});

// r5. create membership for newyork in all yoongi's orgs

Deno.test("creates multiple memberships as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds multiple memberships to db as admin", async () => {
    const createMembership = await client.membership.create([
      {
        userId: 4,
        orgId: 1,
        role: "viewer",
      },
      {
        userId: 4,
        orgId: 2,
        role: "viewer",
      },
      {
        userId: 4,
        orgId: 3,
        role: "viewer",
      },
    ]);

    expect(createMembership).toMatchObject([
      {
        userId: 4,
        orgId: 1,
        role: "viewer",
      },
      {
        userId: 4,
        orgId: 2,
        role: "viewer",
      },
      {
        userId: 4,
        orgId: 3,
        role: "viewer",
      },
    ]);
  });
});

// r6. read all memberships in yoongis-org as dj admin

Deno.test("reads all memberships as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("reads all memberships from db as admin", async () => {
    const memberships = await client.membership.read({ orgId: 1 });

    expect(memberships).toMatchObject([
      {
        userId: 2,
        orgId: 1,
        role: "editor",
      },
      {
        userId: 3,
        orgId: 1,
        role: "viewer",
      },
      {
        userId: 4,
        orgId: 1,
        role: "viewer",
      },
    ]);
  });
});

// r7. read all memberships in yoongis-org as yoongi user

Deno.test("reads all memberships as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("reads all memberships from db as owner", async () => {
    const memberships = await client.membership.read({ orgId: 1 });

    expect(memberships).toMatchObject([
      {
        userId: 2,
        orgId: 1,
        role: "editor",
      },
      {
        userId: 3,
        orgId: 1,
        role: "viewer",
      },
      {
        userId: 4,
        orgId: 1,
        role: "viewer",
      },
    ]);
  });
});

// r8. read all memberships in yoongis-org as minju user(not allowed)

Deno.test("reads all memberships as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("reads all memberships from db as minju", async () => {
    try {
      await client.membership.read({ orgId: 1 });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// r8. update membership for yoongi as admin in yoongis-org as dj admin

Deno.test("updates membership as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("updates membership from db as admin", async () => {
    const updateMembership = await client.membership.update(
      { userId: 2, orgId: 1 },
      { role: "admin" },
    );

    expect(updateMembership).toMatchObject(
      {
        userId: 2,
        orgId: 1,
        role: "admin",
      },
    );
  });
});

// r9. update membership for minju as editor in yoongis-org as yoongi user

Deno.test("updates membership as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates membership from db as owner", async () => {
    const updateMembership = await client.membership.update(
      { userId: 3, orgId: 1 },
      { role: "editor" },
    );

    expect(updateMembership).toMatchObject(
      {
        userId: 3,
        orgId: 1,
        role: "editor",
      },
    );
  });
});

// r10. update membership for minju as admin in yoongis-org as minju user(not allowed)

Deno.test("updates membership as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("updates membership from db as minju", async () => {
    try {
      await client.membership.update(
        { userId: 3, orgId: 1 },
        { role: "admin" },
      );
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// r11. delete membership for yoongi in yoongis-org as dj admin

Deno.test("deletes membership as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("deletes membership from db as admin", async () => {
    const deleteMembership = await client.membership.delete({
      userId: 2,
      orgId: 1,
    });

    expect(deleteMembership).toMatchObject(
      {
        userId: 2,
        orgId: 1,
        role: "admin",
      },
    );
  });
});

// r12. delete membership for minju in yoongis-org as yoongi user

Deno.test("deletes membership as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes membership from db as owner", async () => {
    const deleteMembership = await client.membership.delete({
      userId: 3,
      orgId: 1,
    });

    expect(deleteMembership).toMatchObject(
      {
        userId: 3,
        orgId: 1,
        role: "editor",
      },
    );
  });
});

// r13. delete membership for yoongi in yoongis-org as yoongi user (not allowed)

Deno.test("deletes membership as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes membership from db as owner", async () => {
    try {
      await client.membership.delete({
        userId: 2,
        orgId: 1,
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// **** membership role **** //

//recreate the memberships again for testing further

Deno.test("recreate deleted memberships", async (t) => {
  const client = await connection.signIn(admin);

  const createMembership = await t.step(
    "adds multiple memberships to db for further tests",
    async () => {
      const createMembership = await client.membership.create([
        {
          userId: 2,
          orgId: 1,
          role: "admin",
        },
        {
          userId: 3,
          orgId: 1,
          role: "editor",
        },
        {
          userId: 4,
          orgId: 1,
          role: "viewer",
        },
      ]);

      expect(createMembership).toMatchObject([
        {
          userId: 3,
          orgId: 1,
          role: "editor",
        },
        {
          userId: 4,
          orgId: 1,
          role: "viewer",
        },
      ]);
    },
  );
});

// **** channel **** //

// c1. create channel y0 in yoongis-org as dj admin

Deno.test("creates channel as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds channel to db as admin", async () => {
    const createChannel = await client.channel.create({
      id: 1,
      orgId: 1,
      name: "y0",
      createdAt: new Date(),
    });

    expect(createChannel).toMatchObject(
      {
        id: 1,
        orgId: 1,
        name: "y0",
        createdAt: new Date(),
      },
    );
  });
});

// c2. create channel y1 in yoongis-org as yoongi user

Deno.test("creates channel as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("adds channel to db as owner", async () => {
    const createChannel = await client.channel.create({
      id: 2,
      orgId: 1,
      name: "y1",
      createdAt: new Date(),
    });

    expect(createChannel).toMatchObject(
      {
        id: 2,
        orgId: 1,
        name: "y1",
        createdAt: new Date(),
      },
    );
  });
});

// c3. create channel y11 in yoongis-org as minju user(not allowed)

Deno.test("creates channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("adds channel to db as minju", async () => {
    try {
      await client.channel.create({
        id: 3,
        orgId: 1,
        name: "y11",
        createdAt: new Date(),
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// c4. create channel y1 in yoongis-org as yoongi user with same name as existing channel(not allowed)

Deno.test("creates channel with same name", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("adds channel to db as owner", async () => {
    try {
      await client.channel.create({
        id: 4,
        orgId: 1,
        name: "y1",
        createdAt: new Date(),
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("channel already exists");
    }
  });
});

// c5. create multiple channels [y10, y100] in yoongis-org as yoongi user

Deno.test("creates multiple channels as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("adds multiple channels to db as owner", async () => {
    const createChannel = await client.channel.create([
      {
        id: 5,
        orgId: 1,
        name: "y10",
        createdAt: new Date(),
      },
      {
        id: 6,
        orgId: 1,
        name: "y100",
        createdAt: new Date(),
      },
    ]);

    expect(createChannel).toMatchObject([
      {
        id: 5,
        orgId: 1,
        name: "y10",
        createdAt: new Date(),
      },
      {
        id: 6,
        orgId: 1,
        name: "y100",
        createdAt: new Date(),
      },
    ]);
  });
});

// c6. read all channels in yoongis-org as dj admin

Deno.test("reads all channels as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("reads all channels from db as admin", async () => {
    const channels = await client.channel.read();

    expect(channels).toMatchObject([
      {
        id: 1,
        orgId: 1,
        name: "y0",
        createdAt: new Date(),
      },
      {
        id: 2,
        orgId: 1,
        name: "y1",
        createdAt: new Date(),
      },
      {
        id: 5,
        orgId: 1,
        name: "y10",
        createdAt: new Date(),
      },
      {
        id: 6,
        orgId: 1,
        name: "y100",
        createdAt: new Date(),
      },
    ]);
  });
});

// c7. read a single channel y1 in yoongis-org as yoongi user

Deno.test("reads single channel as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("reads single channel from db as owner", async () => {
    const channel = await client.channel.read({ id: 2 });

    expect(channel).toMatchObject(
      {
        id: 2,
        orgId: 1,
        name: "y1",
        createdAt: new Date(),
      },
    );
  });
});

// c8. read all channels in yoongis-org as yoongi user

Deno.test("reads all channels as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("reads all channels from db as owner", async () => {
    const channels = await client.channel.read();

    expect(channels).toMatchObject([
      {
        id: 1,
        orgId: 1,
        name: "y0",
        createdAt: new Date(),
      },
      {
        id: 2,
        orgId: 1,
        name: "y1",
        createdAt: new Date(),
      },
      {
        id: 5,
        orgId: 1,
        name: "y10",
        createdAt: new Date(),
      },
      {
        id: 6,
        orgId: 1,
        name: "y100",
        createdAt: new Date(),
      },
    ]);
  });
});

// c9. read all shared channels in yoongis-org as minju user

Deno.test("reads shared channels as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("reads shared channels from db as minju", async () => {
    const channels = await client.channel.read({ orgId: 1, shared: true });

    expect(channels).toMatchObject([
      {
        id: 1,
        orgId: 1,
        name: "y0",
        createdAt: new Date(),
      },
      {
        id: 2,
        orgId: 1,
        name: "y1",
        createdAt: new Date(),
      },
      {
        id: 5,
        orgId: 1,
        name: "y10",
        createdAt: new Date(),
      },
      {
        id: 6,
        orgId: 1,
        name: "y100",
        createdAt: new Date(),
      },
    ]);
  });
});

// c10. read a single non-shared y0 channel in yoongis-org as minju user (not allowed)

Deno.test("reads non-shared channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("reads non-shared channel from db as minju", async () => {
    try {
      await client.channel.read({ id: 1 });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// c11. update all channels in yoongis-org as dj admin

Deno.test("updates all channels as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("updates all channels from db as admin", async () => {
    const updateChannels = await client.channel.updateMany([
      { id: 1, name: "y0-0" },
      { id: 2, name: "y1-1" },
      { id: 5, name: "y10-10" },
      { id: 6, name: "y100-100" },
    ]);

    expect(updateChannels).toMatchObject([
      {
        id: 1,
        orgId: 1,
        name: "y0-0",
        createdAt: new Date(),
      },
      {
        id: 2,
        orgId: 1,
        name: "y1-1",
        createdAt: new Date(),
      },
      {
        id: 5,
        orgId: 1,
        name: "y10-10",
        createdAt: new Date(),
      },
      {
        id: 6,
        orgId: 1,
        name: "y100-100",
        createdAt: new Date(),
      },
    ]);
  });
});

// c12. update a channel y10 in yoongis-org as yoongi user

Deno.test("updates a channel as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates a channel from db as owner", async () => {
    const updateChannel = await client.channel.update(
      { id: 5 },
      { name: "y10-10" },
    );

    expect(updateChannel).toMatchObject(
      {
        id: 5,
        orgId: 1,
        name: "y10-10",
        createdAt: new Date(),
      },
    );
  });
});

// c13. update all channels in yoongis-org as yoongi user

Deno.test("updates all channels as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates all channels from db as owner", async () => {
    const updateChannels = await client.channel.updateMany([
      { id: 1, name: "y0-0" },
      { id: 2, name: "y1-1" },
      { id: 5, name: "y10-10" },
      { id: 6, name: "y100-100" },
    ]);

    expect(updateChannels).toMatchObject([
      {
        id: 1,
        orgId: 1,
        name: "y0-0",
        createdAt: new Date(),
      },
      {
        id: 2,
        orgId: 1,
        name: "y1-1",
        createdAt: new Date(),
      },
      {
        id: 5,
        orgId: 1,
        name: "y10-10",
        createdAt: new Date(),
      },
      {
        id: 6,
        orgId: 1,
        name: "y100-100",
        createdAt: new Date(),
      },
    ]);
  });
});

// c14. update a shared channel y1 in yoongis-org as minju user(not allowed)

Deno.test("updates shared channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("updates shared channel from db as minju", async () => {
    try {
      await client.channel.update(
        { id: 2 },
        { name: "y1-1" },
      );
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// c15. update a non-shared channel y0 in yoongis-org as minju user (not allowed)

Deno.test("updates non-shared channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("updates non-shared channel from db as minju", async () => {
    try {
      await client.channel.update(
        { id: 1 },
        { name: "y0-0" },
      );
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// c16. delete a channel y10 in yoongis-org as yoongi user

Deno.test("deletes a channel as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes a channel from db as owner", async () => {
    const deleteChannel = await client.channel.delete({ id: 5 });

    expect(deleteChannel).toMatchObject(
      {
        id: 5,
        orgId: 1,
        name: "y10-10",
        createdAt: new Date(),
      },
    );
  });
});

// c17. delete a shared channel y1 in yoongis-org as minju user(not allowed)

Deno.test("deletes shared channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("deletes shared channel from db as minju", async () => {
    try {
      await client.channel.delete({ id: 2 });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// c18. delete a non-shared channel y0 in yoongis-org as minju user (not allowed)

Deno.test("deletes non-shared channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("deletes non-shared channel from db as minju", async () => {
    try {
      await client.channel.delete({ id: 1 });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// c19. delete all channels in yoongis-org as yoongi user

Deno.test("deletes all channels as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes all channels from db as owner", async () => {
    const deleteChannel = await client.channel.delete({ orgId: 1 });

    expect(deleteChannel).toMatchObject([
      {
        id: 2,
        orgId: 1,
        name: "y1-1",
        createdAt: new Date(),
      },
      {
        id: 6,
        orgId: 1,
        name: "y100-100",
        createdAt: new Date(),
      },
    ]);
  });
});

// c20. delete all channels in yoongis-org as dj admin

Deno.test("deletes all channels as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("deletes all channels from db as admin", async () => {
    const deleteChannel = await client.channel.delete({ orgId: 1 });

    expect(deleteChannel).toMatchObject([
      {
        id: 1,
        orgId: 1,
        name: "y0-0",
        createdAt: new Date(),
      },
      {
        id: 5,
        orgId: 1,
        name: "y10-10",
        createdAt: new Date(),
      },
    ]);
  });
});

// **** channel **** //

//recreate the channels again for testing further

Deno.test("recreate deleted channels", async (t) => {
  const client = await connection.signIn(admin);

  const createChannel = await t.step(
    "adds multiple channels to db for further tests",
    async () => {
      const createChannel = await client.channel.create([
        {
          id: 1,
          orgId: 1,
          name: "y0",
          createdAt: new Date(),
        },
        {
          id: 2,
          orgId: 1,
          name: "y1",
          createdAt: new Date(),
        },
        {
          id: 5,
          orgId: 1,
          name: "y10",
          createdAt: new Date(),
        },
        {
          id: 6,
          orgId: 1,
          name: "y100",
          createdAt: new Date(),
        },
      ]);

      expect(createChannel).toMatchObject([
        {
          id: 1,
          orgId: 1,
          name: "y0",
          createdAt: new Date(),
        },
        {
          id: 2,
          orgId: 1,
          name: "y1",
          createdAt: new Date(),
        },
        {
          id: 5,
          orgId: 1,
          name: "y10",
          createdAt: new Date(),
        },
        {
          id: 6,
          orgId: 1,
          name: "y100",
          createdAt: new Date(),
        },
      ]);
    },
  );
});

// **** message **** //

// m1. create message in y1 channel in yoongis-org as yoongi user

Deno.test("creates message as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("adds message to db as owner", async () => {
    const createMessage = await client.message.create({
      id: 1,
      channelId: 2,
      threadId: 1,
      repliedToId: 1,
      userId: 2,
      text: "hello",
      reactionsCount: 0,
      createdAt: new Date(),
    });

    expect(createMessage).toMatchObject(
      {
        id: 1,
        channelId: 2,
        threadId: 1,
        repliedToId: 1,
        userId: 2,
        text: "hello",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    );
  });
});

// m2. create message in y1 channel in yoongis-org as minju user

Deno.test("creates message as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("adds message to db as minju", async () => {
    const createMessage = await client.message.create({
      id: 2,
      channelId: 2,
      threadId: 2,
      repliedToId: 2,
      userId: 3,
      text: "hi",
      reactionsCount: 0,
      createdAt: new Date(),
    });

    expect(createMessage).toMatchObject(
      {
        id: 2,
        channelId: 2,
        threadId: 2,
        repliedToId: 2,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    );
  });
});

// m3. create multiple repliedto in y1 channel in yoongis-org as minju user

Deno.test("creates multiple repliedto as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("adds multiple repliedto to db as minju", async () => {
    const createMessage = await client.message.create([
      {
        id: 3,
        channelId: 2,
        threadId: 3,
        repliedToId: 1,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
      {
        id: 4,
        channelId: 2,
        threadId: 4,
        repliedToId: 2,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    ]);

    expect(createMessage).toMatchObject([
      {
        id: 3,
        channelId: 2,
        threadId: 3,
        repliedToId: 1,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
      {
        id: 4,
        channelId: 2,
        threadId: 4,
        repliedToId: 2,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    ]);
  });
});

// m4. create message in y0 - non-shared channel in yoongis-org as minju user (not allowed)

Deno.test("creates message in non-shared channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("adds message to db as minju", async () => {
    try {
      await client.message.create({
        id: 5,
        channelId: 1,
        threadId: 5,
        repliedToId: 5,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// m5. create message in y1 channel in yoongis-org as yoongi user with same id (not allowed)

Deno.test("creates message with same id", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("adds message to db as owner", async () => {
    try {
      await client.message.create({
        id: 1,
        channelId: 2,
        threadId: 1,
        repliedToId: 1,
        userId: 2,
        text: "hello",
        reactionsCount: 0,
        createdAt: new Date(),
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("message already exists");
    }
  });
});

// m6. create message in y0 - non-shared channel in yoongis-org as dj admin

Deno.test("creates message in non-shared channel as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds message to db as admin", async () => {
    const createMessage = await client.message.create({
      id: 6,
      channelId: 1,
      threadId: 6,
      repliedToId: 6,
      userId: 2,
      text: "hello",
      reactionsCount: 0,
      createdAt: new Date(),
    });

    expect(createMessage).toMatchObject(
      {
        id: 6,
        channelId: 1,
        threadId: 6,
        repliedToId: 6,
        userId: 2,
        text: "hello",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    );
  });
});

// m7. read all messages in y1 channel in yoongis-org as yoongi user

Deno.test("reads all messages as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("reads all messages from db as owner", async () => {
    const messages = await client.message.read({ channelId: 2 });

    expect(messages).toMatchObject([
      {
        id: 1,
        channelId: 2,
        threadId: 1,
        repliedToId: 1,
        userId: 2,
        text: "hello",
        reactionsCount: 0,
        createdAt: new Date(),
      },
      {
        id: 3,
        channelId: 2,
        threadId: 3,
        repliedToId: 1,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
      {
        id: 4,
        channelId: 2,
        threadId: 4,
        repliedToId: 2,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    ]);
  });
});

// m8. read all messages in y1 channel in yoongis-org as minju user

Deno.test("reads all messages as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("reads all messages from db as minju", async () => {
    const messages = await client.message.read({ channelId: 2 });

    expect(messages).toMatchObject([
      {
        id: 1,
        channelId: 2,
        threadId: 1,
        repliedToId: 1,
        userId: 2,
        text: "hello",
        reactionsCount: 0,
        createdAt: new Date(),
      },
      {
        id: 3,
        channelId: 2,
        threadId: 3,
        repliedToId: 1,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
      {
        id: 4,
        channelId: 2,
        threadId: 4,
        repliedToId: 2,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    ]);
  });
});

// m9. read all messages in y0 - non-shared channel in yoongis-org as minju user (not allowed)

Deno.test("reads all messages in non-shared channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("reads all messages from db as minju", async () => {
    try {
      await client.message.read({ channelId: 1 });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// m10. read all messages in y0 - non-shared channel in yoongis-org as dj admin

Deno.test("reads all messages in non-shared channel as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("reads all messages from db as admin", async () => {
    const messages = await client.message.read({ channelId: 1 });

    expect(messages).toMatchObject([
      {
        id: 6,
        channelId: 1,
        threadId: 6,
        repliedToId: 6,
        userId: 2,
        text: "hello",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    ]);
  });
});

// m11. update message in y1 channel in yoongis-org as yoongi editor

Deno.test("updates message as an editor", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates message from db as owner", async () => {
    const updateMessage = await client.message.update(
      { id: 1 },
      { text: "hi" },
    );

    expect(updateMessage).toMatchObject(
      {
        id: 1,
        channelId: 2,
        threadId: 1,
        repliedToId: 1,
        userId: 2,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    );
  });
});

// m12. update message in y1 channel in yoongis-org as minju user

Deno.test("updates message as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("updates message from db as minju", async () => {
    const updateMessage = await client.message.update(
      { id: 3 },
      { text: "hello" },
    );

    expect(updateMessage).toMatchObject(
      {
        id: 3,
        channelId: 2,
        threadId: 3,
        repliedToId: 1,
        userId: 3,
        text: "hello",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    );
  });
});

// m13. update message in y0 - non-shared channel in yoongis-org as minju user (not allowed)

Deno.test("updates message in non-shared channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("updates message from db as minju", async () => {
    try {
      await client.message.update(
        { id: 6 },
        { text: "hi" },
      );
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// m14. update message in y0 - non-shared channel in yoongis-org as dj admin

Deno.test("updates message in non-shared channel as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("updates message from db as admin", async () => {
    const updateMessage = await client.message.update(
      { id: 6 },
      { text: "hello" },
    );

    expect(updateMessage).toMatchObject(
      {
        id: 6,
        channelId: 1,
        threadId: 6,
        repliedToId: 6,
        userId: 2,
        text: "hello",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    );
  });
});

// m15. delete any message in y1 channel in yoongis-org as yoongi user

Deno.test("deletes message as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("deletes message from db as owner", async () => {
    const deleteMessage = await client.message.delete({ id: 1 });

    expect(deleteMessage).toMatchObject(
      {
        id: 1,
        channelId: 2,
        threadId: 1,
        repliedToId: 1,
        userId: 2,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    );
  });
});

// m16. delete message in y1 channel in yoongis-org as minju user sent by minju

Deno.test("deletes message as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("deletes message from db as minju", async () => {
    const deleteMessage = await client.message.delete({ id: 2 });

    expect(deleteMessage).toMatchObject(
      {
        id: 2,
        channelId: 2,
        threadId: 2,
        repliedToId: 2,
        userId: 3,
        text: "hi",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    );
  });
});

// m17. delete message in y1 channel in yoongis-org as minju user sent by yoongi (not allowed)

Deno.test("deletes message as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("deletes message from db as minju", async () => {
    try {
      await client.message.delete({ id: 1 });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

// m18. delete message in y10 - non-shared channel in yoongis-org as dj admin

Deno.test("deletes message in non-shared channel as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("deletes message from db as admin", async () => {
    const deleteMessage = await client.message.delete({ id: 6 });

    expect(deleteMessage).toMatchObject(
      {
        id: 6,
        channelId: 1,
        threadId: 6,
        repliedToId: 6,
        userId: 2,
        text: "hello",
        reactionsCount: 0,
        createdAt: new Date(),
      },
    );
  });
});

// **** message **** //

//recreate the messages again for testing further

Deno.test("recreate deleted messages", async (t) => {
  const client = await connection.signIn(admin);

  const createMessage = await t.step(
    "adds multiple messages to db for further tests",
    async () => {
      const createMessage = await client.message.create([
        {
          id: 1,
          channelId: 2,
          threadId: 1,
          repliedToId: 1,
          userId: 2,
          text: "hello",
          reactionsCount: 0,
          createdAt: new Date(),
        },
        {
          id: 2,
          channelId: 2,
          threadId: 2,
          repliedToId: 2,
          userId: 3,
          text: "hi",
          reactionsCount: 0,
          createdAt: new Date(),
        },
        {
          id: 4,
          channelId: 2,
          threadId: 4,
          repliedToId: 2,
          userId: 3,
          text: "hi",
          reactionsCount: 0,
          createdAt: new Date(),
        },
        {
          id: 6,
          channelId: 1,
          threadId: 6,
          repliedToId: 6,
          userId: 2,
          text: "hello",
          reactionsCount: 0,
          createdAt: new Date(),
        },
      ]);

      expect(createMessage).toMatchObject([
        {
          id: 1,
          channelId: 2,
          threadId: 1,
          repliedToId: 1,
          userId: 2,
          text: "hello",
          reactionsCount: 0,
          createdAt: new Date(),
        },
        {
          id: 2,
          channelId: 2,
          threadId: 2,
          repliedToId: 2,
          userId: 3,
          text: "hi",
          reactionsCount: 0,
          createdAt: new Date(),
        },
        {
          id: 4,
          channelId: 2,
          threadId: 4,
          repliedToId: 2,
          userId: 3,
          text: "hi",
          reactionsCount: 0,
          createdAt: new Date(),
        },
        {
          id: 6,
          channelId: 1,
          threadId: 6,
          repliedToId: 6,
          userId: 2,
          text: "hello",
          reactionsCount: 0,
          createdAt: new Date(),
        },
      ]);
    },
  );
});

// **** reaction **** //

//e1. create a reaction on message in y1 channel in yoongis-org as yoongi user

Deno.test("creates reaction as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("adds reaction to db as owner", async () => {
    const createReaction = await client.reaction.create({
      messageId: 1,
      userId: 2,
      emoji: "👍",
      createdAt: new Date(),
    });

    expect(createReaction).toMatchObject(
      {
        messageId: 1,
        userId: 2,
        emoji: "👍",
        createdAt: new Date(),
      },
    );
  });
});

//e2. create a reaction on message in y1 channel in yoongis-org as minju user

Deno.test("creates reaction as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("adds reaction to db as minju", async () => {
    const createReaction = await client.reaction.create({
      messageId: 1,
      userId: 3,
      emoji: "👍",
      createdAt: new Date(),
    });

    expect(createReaction).toMatchObject(
      {
        messageId: 1,
        userId: 3,
        emoji: "👍",
        createdAt: new Date(),
      },
    );
  });
});

//e3. create multiple reactions on single message in y1 channel in yoongis-org as minju user

Deno.test("creates multiple reactions on single message", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("adds multiple reactions to db as minju", async () => {
    const createReaction = await client.reaction.create([
      {
        messageId: 1,
        userId: 3,
        emoji: "👍",
        createdAt: new Date(),
      },
      {
        messageId: 1,
        userId: 3,
        emoji: "👎",
        createdAt: new Date(),
      },
    ]);

    expect(createReaction).toMatchObject([
      {
        messageId: 1,
        userId: 3,
        emoji: "👍",
        createdAt: new Date(),
      },
      {
        messageId: 1,
        userId: 3,
        emoji: "👎",
        createdAt: new Date(),
      },
    ]);
  });
});

//e4. create a reaction on message in y0 - non-shared channel in yoongis-org as minju user (not allowed)

Deno.test("creates reaction in non-shared channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("adds reaction to db as minju", async () => {
    try {
      await client.reaction.create({
        messageId: 6,
        userId: 3,
        emoji: "👍",
        createdAt: new Date(),
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

//e5. create same reaction on the same message id in y1 channel in yoongis-org as minju user (not allowed)

Deno.test("creates same reaction on the same message", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("adds reaction to db as minju", async () => {
    try {
      await client.reaction.create({
        messageId: 1,
        userId: 3,
        emoji: "👍",
        createdAt: new Date(),
      });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("reaction already exists");
    }
  });
});

//e6. create a reaction on message in y0 - non-shared channel in yoongis-org as dj admin

Deno.test("creates reaction in non-shared channel as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("adds reaction to db as admin", async () => {
    const createReaction = await client.reaction.create({
      messageId: 6,
      userId: 2,
      emoji: "👍",
      createdAt: new Date(),
    });

    expect(createReaction).toMatchObject(
      {
        messageId: 6,
        userId: 2,
        emoji: "👍",
        createdAt: new Date(),
      },
    );
  });
});

//e7. read all reactions on message in y1 channel in yoongis-org as yoongi user

Deno.test("reads all reactions as an owner", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("reads all reactions from db as owner", async () => {
    const reactions = await client.reaction.read({ messageId: 1 });

    expect(reactions).toMatchObject([
      {
        messageId: 1,
        userId: 2,
        emoji: "👍",
        createdAt: new Date(),
      },
      {
        messageId: 1,
        userId: 3,
        emoji: "👍",
        createdAt: new Date(),
      },
      {
        messageId: 1,
        userId: 3,
        emoji: "👎",
        createdAt: new Date(),
      },
    ]);
  });
});

//e8. read all reactions on message in y1 channel in yoongis-org as minju user

Deno.test("reads all reactions as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("reads all reactions from db as minju", async () => {
    const reactions = await client.reaction.read({ messageId: 1 });

    expect(reactions).toMatchObject([
      {
        messageId: 1,
        userId: 2,
        emoji: "👍",
        createdAt: new Date(),
      },
      {
        messageId: 1,
        userId: 3,
        emoji: "👍",
        createdAt: new Date(),
      },
      {
        messageId: 1,
        userId: 3,
        emoji: "👎",
        createdAt: new Date(),
      },
    ]);
  });
});

//e9. read all reactions on message in y0 - non-shared channel in yoongis-org as minju user (not allowed)

Deno.test("reads all reactions in non-shared channel as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("reads all reactions from db as minju", async () => {
    try {
      await client.reaction.read({ messageId: 6 });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

//e10. read all reactions on message in y0 - non-shared channel in yoongis-org as dj admin

Deno.test("reads all reactions in non-shared channel as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("reads all reactions from db as admin", async () => {
    const reactions = await client.reaction.read({ messageId: 6 });

    expect(reactions).toMatchObject([
      {
        messageId: 6,
        userId: 2,
        emoji: "👍",
        createdAt: new Date(),
      },
    ]);
  });
});

//e11. update reaction on message in y1 channel in yoongis-org as yoongi editor(not allowed)

Deno.test("updates reaction as an editor", async (t) => {
  const client = await connection.signIn(yoongi);

  await t.step("updates reaction from db as owner", async () => {
    try {
      await client.reaction.update(
        { messageId: 1, userId: 2 },
        { emoji: "👎" },
      );
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

//e12. update reaction on message in y1 channel in yoongis-org as minju user(not allowed)

Deno.test("updates reaction as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("updates reaction from db as minju", async () => {
    try {
      await client.reaction.update(
        { messageId: 1, userId: 3 },
        { emoji: "👎" },
      );
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

//e13. delete own reaction on message in y1 channel in yoongis-org as minju user

Deno.test("deletes own reaction as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("deletes own reaction from db as minju", async () => {
    const deleteReaction = await client.reaction.delete({
      messageId: 1,
      userId: 3,
    });

    expect(deleteReaction).toMatchObject(
      {
        messageId: 1,
        userId: 3,
        emoji: "👍",
        createdAt: new Date(),
      },
    );
  });
});

//e14. delete others reaction on message in y1 channel in yoongis-org as minju user(not allowed)

Deno.test("deletes others reaction as another user", async (t) => {
  const client = await connection.signIn(minju);

  await t.step("deletes others reaction from db as minju", async () => {
    try {
      await client.reaction.delete({ messageId: 1, userId: 2 });
      throw new Error("should not reach here");
    } catch (error) {
      expect(error.message).toEqual("not allowed");
    }
  });
});

//e15. delete reaction on message in y0 - non-shared channel in yoongis-org as dj admin

Deno.test("deletes reaction in non-shared channel as admin", async (t) => {
  const client = await connection.signIn(admin);

  await t.step("deletes reaction from db as admin", async () => {
    const deleteReaction = await client.reaction.delete({
      messageId: 6,
      userId: 2,
    });

    expect(deleteReaction).toMatchObject(
      {
        messageId: 6,
        userId: 2,
        emoji: "👍",
        createdAt: new Date(),
      },
    );
  });
});

// **** reaction **** //

// nested tests //

// n1. read all reactions on all messages in y1 channel in yoongis-org as yoongi user
