import { db } from "./schema.ts";
import { t as s } from "@reframe/shapes/main.ts";
import * as t from "./t.ts";

const Exp = Symbol.for("Column.Expression");
interface Column<T = unknown> extends t.Expression<T> {
  [Exp]?: t.Expression<T>;
}

const isColumn = (value: unknown): value is Column =>
  typeof value === "object" && value !== null && Exp in value;

const column = <T extends t.Expression<unknown>>(
  as: T,
  exp?: t.Expression<unknown>,
) => {
  const c = {
    ...as,
    [Exp]: exp,
  };

  return c;
};

type TableColumns =
  | Column<unknown>
  | Composite<{
    [key: string]: TableColumns;
  }>;

const Composite = Symbol.for("Composite");

type Composite<T extends Record<string, TableColumns>> =
  & {
    [Composite]: T;
  }
  & Column<typeof Composite>
  & T;

const composite = <T extends Record<string, TableColumns>>(
  column: t.Expression<unknown>,
  columns: T,
) => {
  return ({
    ...column,

    [Composite]: columns,
    ...columns,
  }) as Composite<T>;
};

type Selection = TableColumns | {
  [key: string]: Selection;
};

type SelectionColumns<S extends Selection> = S extends {
  [key: string]: Selection;
} ? Composite<{ [K in keyof S]: SelectionColumns<S[K]> }>
  : S extends Column<unknown> ? S
  : never;

const selectionToColumns = <S extends Selection>(
  selection: S,
): SelectionColumns<S> => {
  if (isColumn(selection)) {
    return selection as SelectionColumns<S>;
  }

  return composite(
    column({
      [s.OUT]: {},
      [t.COMPILE]: (opts) => ["1"],
    }),
    Object.fromEntries(
      Object.entries(selection)
        .map(([key, value]) => [
          key,
          selectionToColumns(value),
        ]),
    ),
  ) as SelectionColumns<S>;
};

type Merge<A extends TableColumns, B extends { [key: string]: TableColumns }> =
  A extends Composite<infer T> ? Composite<T & B> : A;

type Combine<C extends TableColumns> = C extends { [Composite]: infer T } ? {
    [K in keyof T & string]: T[K] extends TableColumns ? Combine<T[K]> : never;
  }
  : s.Out<C>;

const combine = <C extends TableColumns>(
  columns: C,
): Column<Combine<C>> => {
  return column({
    [s.OUT]: {},
    [t.COMPILE]: (opts) => {
      return [
        ["case when", t.compile(columns, opts), "is null then null else", [
          `json_object(`,
          t.INDENT,
          Object.entries(columns)
            .map(([key, value], index) => {
              if (Composite in value) {
                return [
                  index === 0 ? " " : [t.NODENT, ","],
                  `'${key}',`,
                  t.compile(combine(value), opts),
                ];
              }

              return [
                index === 0 ? " " : [t.NODENT, ","],
                `'${key}',`,
                t.compile(value, opts),
              ];
            }),
          t.UNDENT,
          `)`,
        ], "end"],
      ];
    },
  });
};

const mapColumns = <T extends TableColumns>(
  columns: T,
  fn: <C extends Column<unknown>>(value: C) => C,
): T => {
  if (Composite in columns) {
    return composite(
      fn(columns),
      Object.fromEntries(
        Object.entries(columns[Composite])
          .map(([key, value]) => [key, mapColumns(value, fn)]),
      ),
    ) as T;
  }

  return fn(columns) as T;
};

interface Table<
  Base extends TableColumns = TableColumns,
  Joins extends Record<
    string,
    {
      table: Table;
      on: (right: TableColumns, left: TableColumns) => t.Expression<boolean>;
    }
  > = Record<
    string,
    {
      table: Table;
      on: (right: TableColumns, left: TableColumns) => t.Expression<boolean>;
    }
  >,
  Selected extends TableColumns | null = null,
> extends t.Compilable {
  base: Base;
  joins: Joins;

  columns: Selected extends null
    ? Merge<Base, { [K in keyof Joins]: Joins[K]["table"]["columns"] }>
    : Selected;

  where: (c: (t: this["columns"]) => t.Expression<boolean>) => this;

  joinOne: <K extends string, Right extends Table>(
    name: K,
    right: Right,
    on: (
      right: Right["columns"],
      left: this["columns"],
    ) => t.Expression<boolean>,
  ) => Table<
    Base,
    & Joins
    & {
      [J in K]: {
        table: Right;
        on: (right: TableColumns, left: TableColumns) => t.Expression<boolean>;
      };
    },
    Selected
  >;

  joinMany: <K extends string, Right extends Table>(
    name: K,
    right: Right,
    on: (
      right: Right["columns"],
      left: this["columns"],
    ) => t.Expression<boolean>,
  ) => Table<
    Merge<
      Base,
      {
        [J in K]: Column<Combine<Right["columns"]>[]>;
      }
    >,
    Joins
  >;

  select: <Select extends Selection>(
    _: (_: this["columns"]) => Select,
  ) => Table<
    Base,
    Joins,
    SelectionColumns<Select>
  >;

  combine: () => Table<Base, Joins, Column<Combine<this["columns"]>>>;
}

const test1 = () => {
  const org_: Table<
    Composite<{
      id: Column<number>;
      slug: Column<string>;
      ownerId: Column<number>;
    }>,
    {}
  > = {} as any;

  const user_: Table<
    Composite<{
      id: Column<number>;
      name: Column<string>;
      email: Column<string>;
    }>,
    {}
  > = {} as any;

  const membership_: Table<
    Composite<{
      userId: Column<number>;
      orgId: Column<number>;
      role: Column<"admin" | "editor">;
    }>,
    {}
  > = {} as any;

  const t1 = org_
    .joinMany(
      "members",
      user_
        .joinOne(
          "membership",
          membership_,
          (m, u) => t.eq(m.userId, u.id),
        )
        .where((row) => t.eq(row.membership.role, "editor")),
      (user, org) => t.eq(user.membership.orgId, org.id),
    )
    .joinOne(
      "owner",
      user_,
      (user, org) => t.eq(user.id, org.ownerId),
    );

  type T1 = Combine<typeof t1["columns"]>;
};

const flatten = <
  T extends Selection,
>(columns: T): Column<unknown>[] => {
  if (isColumn(columns)) {
    if (Composite in columns) {
      return [
        columns,
        ...Object.values(columns[Composite]).flatMap(flatten),
      ];
    }

    return [columns];
  }

  return Object.values(columns).flatMap(flatten);
};

const merge = <
  A extends TableColumns,
  B extends { [key: string]: TableColumns },
>(a: A, b: B): Merge<A, B> => {
  if (Composite in a) {
    return composite(a, { ...a[Composite], ...b }) as Merge<A, B>;
  }

  return a as Merge<A, B>;
};

const table = <
  Base extends TableColumns,
  Joins extends Record<
    string,
    {
      table: Table;
      on: (right: TableColumns, left: TableColumns) => t.Expression<boolean>;
    }
  >,
  Selection extends TableColumns,
>(
  from: string,
  base: Base,
  joins: Joins,
  columns: Table<Base, Joins, Selection>["columns"],
  whereClause: t.Expression<boolean> | null,
) => {
  const self: Table<
    Base,
    Joins,
    Selection
  > = {
    base,
    joins,
    columns,
    where: (c) =>
      table(
        from,
        base,
        joins,
        columns,
        whereClause ? t.and(whereClause, c(columns)) : c(columns),
      ),
    joinOne: <K extends string, Right extends Table>(
      name: K,
      right: Right,
      on: (
        right: Right["columns"],
        left: typeof columns,
      ) => t.Expression<boolean>,
    ) => {
      const rightColumns = mapColumns(
        right.columns,
        (c) =>
          column(
            {
              [s.OUT]: {},
              [t.COMPILE]: (opts) =>
                t.compile(c, {
                  ...opts,
                  counter: opts.counter.prefix(name + "_"),
                }),
            } as typeof c,
          ),
      );

      return table<
        Base,
        & Joins
        & {
          [J in K]: {
            table: Right;
            on: (
              right: TableColumns,
              left: TableColumns,
            ) => t.Expression<boolean>;
          };
        },
        Selection
      >(
        from,
        base,
        {
          ...joins,
          [name]: {
            table: right,
            on: on(
              rightColumns,
              columns,
            ),
          },
        },
        // update columns
        merge(columns, { [name]: rightColumns }),
        whereClause,
      );
    },
    select: (c) => {
      return table(
        from,
        base,
        joins,
        selectionToColumns(c(columns)),
        whereClause,
      );
    },
    combine: () => self.select((row) => combine(row)),

    [t.COMPILE]: (opts) => {
      return [
        `select`,
        t.INDENT,

        Array.from(new Set([...flatten(base), ...flatten(columns)]))
          .map((column, index) => [
            index === 0 ? " " : [t.NODENT, ","],
            column[Exp] !== undefined
              ? [t.compile(column[Exp]!, opts), "as", t.compile(column, opts)]
              : t.compile(column, opts),
          ]),

        t.UNDENT,
        `from`,
        from,
        Object.entries(joins)
          .map(([key, value]) =>
            [
              t.NODENT,
              `left join`,
              "(",
              [
                t.INDENT,
                t.compile(value.table, {
                  ...opts,
                  counter: opts.counter.prefix(key + "_"),
                }),
                t.UNDENT,
              ],
              ") as",
              key,
              `on`,
              t.compile(
                value.on,
                opts,
              ),
            ] as t.SQL
          ),
        whereClause
          ? [
            t.NODENT,
            "where",
            t.compile(whereClause, opts),
          ]
          : [],
      ];
    },
  };

  return self;
};

const from = <
  T extends t.Table,
>(
  base: T,
  name: string,
): Table<
  Composite<{ [K in keyof T["columns"]]: Column<s.Out<T["columns"][K]>> }>,
  {}
> => {
  const columns = Object.entries(base.columns)
    .reduce(
      (acc, [key, value]) => {
        return {
          ...acc,
          [key]: column({
            [s.OUT]: {},
            [t.COMPILE]: (opts) => `${opts.counter.getPrefix()}${key}`,
          }, {
            [s.OUT]: {},
            [t.COMPILE]: (opts) => [key],
          }),
        };
      },
      {} as {
        [K in keyof T["columns"]]: Column<s.Out<T["columns"][K]>>;
      },
    );

  const c = composite(
    column({
      [s.OUT]: {},
      [t.COMPILE]: (opts) => opts.counter.getPrefix(),
    }, {
      [s.OUT]: {},
      [t.COMPILE]: (opts) => ["1"],
    }),
    columns,
  );

  return table(
    name,
    c,
    {},
    c,
    null,
  );
};

const org = from(db.tables.org, "org");
const user = from(db.tables.user, "user");
const membership = from(db.tables.membership, "membership");

const t3 = org
  .joinOne(
    "members",
    user
      .joinOne(
        "membership",
        membership,
        (m, u) => t.eq(m.userId, u.id),
      )
      .where((row) => t.eq(row.membership.role, "editor")),
    (user, org) => t.eq(user.membership.orgId, org.id),
  )
  .joinOne(
    "owner",
    user,
    (user, org) => t.eq(user.id, org.ownerId),
  )
  .select((org) => ({ ...org, owner2: org.owner }))
  .where((r) => t.isNotNull(r.owner2.email))
  .combine();

const t4 = org
  .joinOne(
    "owner",
    user,
    (user, org) => t.eq(user.id, org.ownerId),
  )
  .joinOne(
    "owner2",
    user,
    (user, org) => t.eq(user.id, org.ownerId),
  )
  .joinOne(
    "membership",
    membership
      .select((m) => ({
        yooo: m.userId,
        ...m,
      })),
    (membership, org) =>
      t.and(
        t.eq(org.id, membership.orgId),
        t.eq(org.owner.id, membership.userId),
      ),
  )
  .where((org) =>
    t.or(
      t.isNotNull(org.owner.id),
      t.isNotNull(org.membership.role),
      t.isNotNull(org.name),
    )
  )
  .select(({ owner, membership, ...org }) => ({
    org,
    owner,
    membership,
    role2: membership.role,
  }));

const sql = t.compileSql(
  t.compile(t3, {
    counter: t.createCounter().prefix("t_"),
    args: new Map(),
  }),
);

console.log(
  sql,
);

import { client } from "./test/nested-read-raw.ts";

console.log(await client.execute(`explain query plan ${sql}`));
