import { createRoute } from "@reframe/react/router.tsx";
import {
  ReactServerStreamPayload,
  Render,
  render,
  reply,
  stream,
} from "@reframe/react/server.tsx";
import { Shell } from "@reframe/react/shell.tsx";
import { Suspense } from "npm:react@canary";
import { assets as tailwindAssets } from "@reframe/tailwind/assets.ts";

import {
  type ServerSentEventMessage,
  ServerSentEventStream,
} from "https://deno.land/std@0.224.0/http/server_sent_event_stream.ts";

const App = createRoute<{ request: Request }>((Router) => (
  <Router.Route
    render={async (element, request) => {
      const root = "millionleads-root";

      if (request.headers.get("x-react-server-action")) {
        return reply(request);
      }

      const assets = await tailwindAssets();

      if (request.headers.get("accept") === "text/event-stream") {
        return new Response(
          stream(element, { root, assets })
            .pipeThrough(
              new TransformStream<
                ReactServerStreamPayload,
                ServerSentEventMessage
              >({
                transform(chunk, controller) {
                  controller.enqueue({
                    event: chunk.type,
                    data: JSON.stringify(chunk.data),
                  });
                },
              }),
            )
            .pipeThrough(new ServerSentEventStream()),
          { headers: { "content-type": "text/event-stream" } },
        );
      }

      return new Response(
        render(
          <Shell assets={assets}>
            <Render root={root}>
              <Suspense>
                {element}
              </Suspense>
            </Render>
          </Shell>,
        ),
        { headers: { "content-type": "text/html" } },
      );
    }}
    page={() => <dib>hemlo</dib>}
  />
));

export default App;
