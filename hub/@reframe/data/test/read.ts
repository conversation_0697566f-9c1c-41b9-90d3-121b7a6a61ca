import * as t from "../t.ts";
import { db } from "../schema.ts";

const token = await db
  .signUp({
    user: {
      id: 1,
      email: "",
      role: "admin",
    },
  });

const client = await db.signIn(token);

const org = await client
  .readMany(
    "org",
    {
      select: {
        id: true,
        slug: true,
      },
      include: {
        owner: {
          select: { id: true, email: true },
          include: {
            orgs: {
              limit: 2,
            },
          },
        },
        membership: {
          include: {
            org: {},
          },
        },
        memberships: {
          limit: 3,
          where: {
            role: "admin",
          },
          include: {
            org: {},
            user: {
              select: {
                id: true,
                email: true,
              },
            },
          },
        },
      },
      where: {
        id: 7,
        slug: { $neq: "foo" },
        owner: {
          id: { $lt: 1 },
          name: { $gt: "1" },
        },
        $or: [{
          owner: {
            id: 1,
            email: { $like: "%@gmail.com" },
          },
        }, {
          membership: {
            $or: [{
              role: "viewer",
            }, {
              org: {
                slug: "test",
              },
            }],
          },
        }],
      },
      limit: 10,
      offset: 2,
    },
  );

console.log(org[0].memberships[0].role); // fetch all

// fetch all orgs, whose owner reacted haha to a post, and org name is foo
