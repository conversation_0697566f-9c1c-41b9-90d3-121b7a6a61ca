import { db, from, scaled, t } from "../query.ts";

const a = from(
  t.table({
    ax: t.string(),
    ay: t.number(),
  })
    .primary("ax"),
  "a",
);

const b = from(
  t.table({
    bx: t.string(),
    by: t.number(),
  }).primary("bx"),
  "b",
);

const c = from(
  t.table({
    cx: t.string(),
    cy: t.number(),
  }).primary("cx"),
  "c",
);

const connection = db
  .connect({ url: ":memory:" });

const token = await connection
  .signUp({ user: { id: 1, email: "", role: "admin" } });

const { $client: client } = await connection.signIn(token);

await client.executeMultiple(
  `
    create table a ( ax text, ay integer );
    create table b ( bx text, by integer );
    create table c ( cx text, cy integer );

    -- add indexes
    create index a_ax on a ( ax );
    create index a_ay on a ( ay );
    create unique index a_axy on a ( ax, ay );

    create index b_bx on b ( bx );
    create index b_by on b ( by );
    create unique index b_bxy on b ( bx, by );
  `,
);

/**
 * test
 * - nested joinOne
 *
 * bugs
 * - json_group_array in joinMany
 *
 * fixed
 * - ON clause uses internal expression instead of identifier while select uses identifier correctly
 * - inside joinMany, outer table has wrong expression
 * - where shows expression instead of identifier
 */

const query = a
  .joinOne(
    "ab",
    b
      .joinOne(
        "bc",
        c
          .joinOne(
            "ca",
            a.select((r) => ({
              ax: r.ax,
              w1: t.and(true, false),
              w2: t.or(true, false),
            })),
            (a, c) => t.eq(a.ax, c.cx),
          )
          .joinMany(
            "cbs",
            b
              .select((b) => ({
                ...b,
                bb: scaled(b),
              }))
              .select((b) => ({
                ...b,
                bbb: scaled(b),
              }))
              .where((b) => t.and(t.eq(b.by, 11), t.isNull(b))),
            (b, c) =>
              t.and(
                t.eq(b.bx, c.cx),
                t.eq(b.by, 22),
                t.eq(c.cy, 33),
                t.isNull(c),
                t.isNull(b),
              ),
          ),
        (c, b) => t.eq(c.cy, b.by),
      ),
    (b, a) => t.eq(b.bx, a.ax),
  )
  .joinOne(
    "ac",
    c
      .joinOne(
        "cb",
        b
          .joinOne("ba", a, (a, b) => t.eq(a.ax, b.bx)),
        (b, c) => t.eq(b.by, c.cy),
      )
      .joinOne("ca", a, (a, c) => t.eq(a.ax, c.cx)),
    (c, a) => t.eq(c.cx, a.ax),
  )
  .select((r) => ({
    ...r,
    r1: scaled(r.ab),
  }))
  .select((r) => ({
    ...r,
    r2: scaled(r),
  }))
  .where((r) => t.and(t.isNull(r.r1), t.isNull(r.r2)));

const sql = t.compileSql(
  t.compile(
    query,
    {
      scope: t.createScope(["t"]),
      args: new Map(),
    },
  ),
);

console.log(sql);

console.log(await client.execute(`explain query plan ${sql}`));
