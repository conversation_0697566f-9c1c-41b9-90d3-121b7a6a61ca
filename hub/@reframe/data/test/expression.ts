import * as t from "@reframe/data/t.ts";

const auth = t.createVariable<{
  user: {
    id: t.Expression<number>;
    role: t.Expression<"admin" | "user">;
  };
}>("$$auth");

const row = t.createVariable<{
  ownerId: t.Expression<number>;
  membership: {
    role: t.Expression<"admin" | "user">;
  };
}>("$$row");

const update = t.createVariable<{
  id: t.Expression<number | null>;
  createdAt: t.Expression<Date | null>;
}>("$$update");

const expression = t.or(
  t.eq(auth.user.role, "admin"),
  t.eq(row.membership.role, "admin"),
  t.and(
    t.eq(row.ownerId, auth.user.id),
    t.isNull(update.id),
    t.isNull(update.createdAt),
  ),
);

let count = 0;
const { sql, args } = expression.toSQL({
  indent: 0,
  counter: () => count++,
});
console.log(sql);
console.log(args);
