import { db } from "../schema.ts";
import { createClient } from "npm:@libsql/client/node";

const token = await db
  .signUp({
    user: {
      id: 1,
      email: "",
      role: "admin",
    },
  });

const client1 = await db.signIn(token);

// const org = t
//   .table({
//     id: t.number(),
//     slug: t.string(),
//     ownerId: t.number(),
//     name: t.string(),
//     createdAt: t.date(),
//   })
//   .primary("id")
//   .unique("slug")
//   .index("ownerId");

// const membership = t
//   .table({
//     orgId: t.number(),
//     userId: t.number(),
//     role: t.union([
//       t.literal("admin"),
//       t.literal("editor"),
//       t.literal("viewer"),
//     ]),
//   })
//   .primary("orgId", "userId")
//   .index("orgId")
//   .index("userId")
//   .index("role");

// const channel = t
//   .table({
//     id: t.number(),
//     orgId: t.number(),
//     name: t.string(),
//     createdAt: t.date(),
//   })
//   .primary("id")
//   .unique("orgId", "name")
//   .index("orgId");

export const client = createClient({
  url: ":memory:",
});
const result1 = await client.executeMultiple(
  `
    create table user (
      id integer not null,
      name text not null,
      email text not null,
      active boolean,
      createdAt integer not null,
      metadata json not null,
      primary key (id)
    );

    create unique index user_email on user(email);
    create index user_createdAt on user(createdAt);
    create index user_active on user(active);

    create table org (
      id integer not null,
      slug text not null,
      ownerId integer not null,
      name text not null,
      createdAt integer not null,
      primary key (id)
    );
    
    create unique index org_slug on org(slug);
    create index org_ownerId on org(ownerId);

    create table membership (
      orgId integer not null,
      userId integer not null,
      role text not null,
      primary key (orgId, userId)
    );

    create index membership_orgId on membership(orgId);
    create index membership_userId on membership(userId);
    create index membership_role on membership(role);

    create table channel (
      id integer not null,
      orgId integer not null,
      name text not null,
      createdAt integer not null,
      primary key (id)
    );

    create unique index channel_orgId_name on channel(orgId, name);
    create index channel_orgId on channel(orgId);

    create table message (
      id integer not null,
      channelId integer not null,
      userId integer not null,
      text text not null,
      createdAt integer not null,
      primary key (id)
    );

    create index message_channelId on message(channelId);
    create index message_userId on message(userId);
    create index message_text on message(text);
    create index message_createdAt on message(createdAt);
  `,
);

const result2 = await client.batch([
  `
    insert into user (id, name, email, active, createdAt, metadata)
    values
      (1, 'Alice', '<EMAIL>', true, '2021-01-01', '{}'),
      (2, 'Bob', '<EMAIL>', false, '2021-01-02', '{"occupation": "builder"}')
  `,
  `
    insert into org (id, slug, ownerId, name, createdAt)
    values
      (1, 'foo', 1, 'foo', '2021-01-01'),
      (2, 'bar', 2, 'bar', '2021-01-02'),
      (3, 'phi', 1, 'phi', '2021-01-01')
  `,
  `
    insert into membership (orgId, userId, role)
    values
      (1, 1, 'admin'),
      (2, 1, 'editor'),
      (3, 2, 'viewer')
  `,
  `
    insert into channel (id, orgId, name, createdAt)
    values
      (1, 1, '#genfoo', '2021-01-01'),
      (2, 2, '#barrr', '2021-01-02'),
      (3, 1, '#foondom', '2021-01-03'),
      (4, 2, '#bored', '2021-01-04')
  `,
  `
    insert into message (id, channelId, userId, text, createdAt)
    values
      (1, 1, 1, 'hey bob', '2021-01-01'),
      (2, 1, 2, 'hey alice', '2021-01-02'),
      (3, 2, 1, 'oh hi bob', '2021-01-03'),
      (4, 2, 2, 'oh hi alice', '2021-01-04'),
      (5, 3, 1, 'hello bob', '2021-01-05'),
      (6, 3, 2, 'hello alice', '2021-01-06'),
      (7, 4, 1, 'goodbye bob', '2021-01-07'),
      (8, 4, 2, 'goodbye alice', '2021-01-08')
  `,
]);

// user.read({
//   $select: ['orgs'],
//   orgs: {
//       membership: {
//           role: "admin"
//       },
//       channel: {
//           message: {
//              $limit: 5,
//           },
//           $limit: 5,
//           $orderBy: { id: 'desc' }
//       },
//       $offset: 5
//   },
// })

const sql0 = `
SELECT * FROM (
  SELECT json_object('id', id)
  as row FROM user
)
WHERE row ->> '$.id' = '1'
`;

const sql = `
SELECT
  case when "$3" is null then null else json_object(
     'id', "$3_1"
    ,'slug', "$3_2"
    ,'ownerId', "$3_3"
    ,'owner', case when "$1" is null then null else json_object(
       'id', "$1_1"
      ,'email', "$1_2"
      ,'orgs', json("$1_3")
    ) end
    ,'membership', case when "$2" is null then null else json_object(
        'orgId', "$2_1"
        ,'userId', "$2_2"
        ,'role', "$2_3"
    ) end
  ) end as row

FROM (
    SELECT
        1 as "$3"
        ,id as "$3_1"
        ,slug as "$3_2"
        ,ownerId as "$3_3"
        ,"$1"
        ,"$1_1"
        ,"$1_2"
        ,"$1_3"
        ,"$2"
        ,"$2_1"
        ,"$2_2"
        ,"$2_3"
    FROM org
    LEFT JOIN (
        SELECT
            1 as "$1"
            ,id as "$1_1"
            ,email as "$1_2"
            ,(
              SELECT json_group_array(json(row))
              FROM (
                SELECT
                  case when "$3" is null then null else json_object(
                    'id', "$3_1"
                    ,'slug', "$3_2"
                    ,'ownerId', "$3_3"
                  ) end as row
                FROM (
                  SELECT
                    1 as "$3"
                    ,id as "$3_1"
                    ,slug as "$3_2"
                    ,ownerId as "$3_3"
                    ,"$1"
                    ,"$1_1"
                    ,"$1_2"
                    ,"$2"
                    ,"$2_1"
                    ,"$2_2"
                    ,"$2_3"
                  FROM org

                  LEFT JOIN (
                    SELECT
                      1 as "$1"
                      ,id as "$1_1"
                      ,email as "$1_2"
                    FROM user
                    WHERE "$1_1" = 1
                  ) owner on "$1_1" = "$3_3"

                  LEFT JOIN(
                      SELECT
                          1 as "$2"
                          ,orgId as "$2_1"
                          ,userId as "$2_2"
                          ,role as "$2_3"
                      FROM membership
                      WHERE "$2_2" = 1
                  ) membership on ("$2_1" = "$3_1" AND "$2_2" = 1)
                   
                  WHERE ("$1" is not null OR "$2" is not null)
                )
              )
            ) as "$1_3"
        FROM user
        WHERE "$1_1" = 1
    ) owner on "$1_1" = "$3_3"
    LEFT JOIN(
        SELECT
            1 as "$2"
            ,orgId as "$2_1"
            ,userId as "$2_2"
            ,role as "$2_3"
        FROM membership
        WHERE "$2_2" = 1
    ) membership on ("$2_1" = "$3_1" AND "$2_2" = 1)
    WHERE ("$1" is not null OR "$2" is not null)
)
`;

const sql3 = `
SELECT
    json_object(
        'id', id
        ,'slug', slug
        ,'ownerId', ownerId
        ,'owner', json_object(
            'id', "owner.id"
            ,'email', "owner.email"
        )
        ,'membership', case when "membership.$" is not null then json_object(
            'orgId', "membership.orgId"
            ,'userId', "membership.userId"
            ,'role', "membership.role"
            ,'org', json_object(
                'id', "membership.org.id"
                ,'slug', "membership.org.slug"
                ,'ownerId', "membership.org.ownerId"
                ,'owner', case when "membership.org.owner.$" is not null  then json_object(
                    'id', "membership.org.owner.id"
                    ,'email', "membership.org.owner.email"
                ) else null end
                ,'membership', json_object(
                    'orgId', "membership.org.membership.orgId"
                    ,'userId', "membership.org.membership.userId"
                    ,'role', "membership.org.membership.role"
                )
            )
        ) else null end
    ) as row
FROM (
    SELECT
        id as "id"
        ,slug as "slug"
        ,ownerId as "ownerId"
        ,owner.*
        ,membership.*
    FROM org

    LEFT JOIN (
        SELECT -- owner
            id as "owner.id"
            ,email as "owner.email"
        FROM user
        WHERE "owner.id" = 2
    ) owner on "owner.id" = ownerId

    LEFT JOIN (
        SELECT -- membership
            1 as "membership.$",
            orgId as "membership.orgId"
            ,userId as "membership.userId"
            ,role as "membership.role"
            ,org.*
        FROM membership

        LEFT JOIN (
            SELECT -- membership.org
                id as "membership.org.id"
                ,slug as "membership.org.slug"
                ,ownerId as "membership.org.ownerId"
                ,owner.*
                ,membership.*
            FROM org

            LEFT JOIN (
                SELECT -- membership.org.owner
                    1 as "membership.org.owner.$"
                    ,id as "membership.org.owner.id"
                    ,email as "membership.org.owner.email"
                FROM user
                WHERE id = 2
            ) owner on "membership.org.owner.id" = ownerId

            LEFT JOIN (
                SELECT -- membership.org.membership
                    orgId as "membership.org.membership.orgId"
                    ,userId as "membership.org.membership.userId"
                    ,role as "membership.org.membership.role"
                FROM membership
                WHERE userId = 2
            ) membership on "membership.org.membership.orgId" = id and "membership.org.membership.userId" = 2

            WHERE
              "membership.org.owner.id" is not null OR
              "membership.org.membership.role" is not null
        ) org on "membership.org.id" = orgId

        WHERE userId = 2
    ) membership on "membership.orgId" = id and "membership.userId" = 2

    WHERE (
        "owner.id" is not null OR "membership.role" is not null
    )
)
`;

const sql1 = `
  select
    json_group_array(
      json_object(
        'id',
        user1.id,
        'name',
        user1.name,
        'org',
        (
          select
            json_group_array(
              json_object(
                'id',
                org1.id,
                'slug',
                org1.slug,
                'ownerId',
                org1.ownerId,
                'name',
                org1.name,
                'membership',
                (
                  select
                    json_object(
                      'orgId',
                      membership.orgId,
                      'userId',
                      membership.userId,
                      'role',
                      membership.role
                    )
                  from membership membership
                  where membership.orgId = org1.id
                  and membership.role = 'admin'
                  limit 1
                ),
                'channel',
                (
                  select
                    json_group_array(
                      json_object(
                        'id',
                        channel1.id,
                        'orgId',
                        channel1.orgId,
                        'name',
                        channel1.name,
                        'message',
                        (
                          select
                            json_group_array(
                              json_object(
                                'id',
                                message1.id,
                                'channelId',
                                message1.channelId,
                                'userId',
                                message1.userId,
                                'text',
                                message1.text,
                                'createdAt',
                                message1.createdAt,
                                'channel',
                                (
                                  select
                                    json_group_array(
                                      json_object(
                                        'id',
                                        channel2.id,
                                        'orgId',
                                        channel2.orgId,
                                        'name',
                                        channel2.name
                                      )
                                    )
                                  from channel channel2
                                  where channel2.id = message1.channelId
                                )
                              )
                            )
                          from message message1
                          where message1.channelId = channel1.id
                        )
                      )
                    )
                  from channel channel1
                )
              )
            )
          from org org1
          where org1.ownerId = user1.id
        )
      )
    ) as "$"
  from user user1
`;

const sql4 = `
select 
  1 as t_ 
  , t_.id as t_id 
  , t_.slug as t_slug 
  , t_.ownerId as t_ownerId 
  , t_.name as t_name 
  , t_.createdAt as t_createdAt 
  , t_owner_
  , t_owner_id 
  , t_owner_name 
  , t_owner_email 
  , t_owner_active 
  , t_owner_createdAt 
  , t_owner_metadata 
  , t_owner_self_
  , t_owner_self_id 
  , t_owner_self_slug 
  , t_owner_self_ownerId 
  , t_owner_self_name 
  , t_owner_self_createdAt 
  , t_owner_self_owner_
  , t_owner_self_owner_id 
  , t_owner_self_owner_name 
  , t_owner_self_owner_email 
  , 1 as t_foo 
  , t_owner_self_owner_id 
  , t_owner_self_id 
  , t_owner_id 
  , t_.id as t_id 
  , case when 43 is null then null else json_object( 
      'a', t_owner_self_owner_id 
    , 'b', t_owner_self_id 
    , 'c', t_owner_id 
    , 'd', t_.id 
  ) end as t_bar 
from org as t_ 
left join ( 
  select 
    44 as t_owner_ 
    , t_owner_.id as t_owner_id 
    , t_owner_.name as t_owner_name 
    , t_owner_.email as t_owner_email 
    , t_owner_.active as t_owner_active 
    , t_owner_.createdAt as t_owner_createdAt 
    , t_owner_.metadata as t_owner_metadata 
    , t_owner_self_ 
    , t_owner_self_id 
    , t_owner_self_slug 
    , t_owner_self_ownerId 
    , t_owner_self_name 
    , t_owner_self_createdAt 
    , t_owner_self_owner_ 
    , t_owner_self_owner_id 
    , t_owner_self_owner_name 
    , t_owner_self_owner_email 
  from user as t_owner_ 
  left join ( 
    select 
      44 as t_owner_self_ 
      , t_owner_self_.id as t_owner_self_id 
      , t_owner_self_.slug as t_owner_self_slug 
      , t_owner_self_.ownerId as t_owner_self_ownerId 
      , t_owner_self_.name as t_owner_self_name 
      , t_owner_self_.createdAt as t_owner_self_createdAt 
      , t_owner_self_owner_ 
      , t_owner_self_owner_id 
      , t_owner_self_owner_name 
      , t_owner_self_owner_email 
    from org as t_owner_self_ 
    left join ( 
      select 
        44 as t_owner_self_owner_ 
        , t_owner_self_owner_.id as t_owner_self_owner_id 
        , t_owner_self_owner_.name as t_owner_self_owner_name 
        , t_owner_self_owner_.email as t_owner_self_owner_email 
      from user as t_owner_self_owner_ 
    ) as t_owner_self_owner_ on t_owner_self_owner_id = t_owner_self_.id 
  ) as t_owner_self_ on t_owner_self_ownerId = t_owner_id 
  WHERE t_owner_id = 1 
) as t_owner_ on t_owner_id = t_.ownerId 
WHERE ( 
  t_owner_self_owner_id IS NOT NULL 
  AND t_owner_self_id IS NOT NULL 
  AND t_owner_id IS NOT NULL 
  AND t_.id IS NOT NULL 
)
`;

// const result3 = await client1.$client.execute(sql4);
// for (const row of result3.rows) {
//   console.log(row);
//   // console.log(JSON.stringify(JSON.parse(row["row"]), null, 2));
// }

// const explain = await client1.$client.execute(`explain query plan ${sql4}`);
// console.log(explain.rows);

const result3 = await client.execute(`
  update org
  set slug = 'foo:1'
  from (
    select
      *
    from org
    join (
      select
        id as t_id
      from user
    )
    where org.ownerId = 2 and t_id = 2
  ) as t
  where 1
    and org.id = t.id
    and org.id = 2
  returning *
`);

console.log(result3.rows);
