// import { createClient } from "npm:@libsql/client/node";
// import { Buffer } from "node:buffer";

// try {
//   Deno.statSync("test.db");
// } catch {
//   Deno.writeFileSync("test.db", new Uint8Array());
// }

// // export const client = createClient({ url: ":memory:" });
// const client = createClient({ url: "file:test.db" });

// const result1 = await client.execute({
//   sql: `create table if not exists test (
//     i integer,
//     t text,
//     j json,
//     b blob
//   )`,
//   args: {},
// });
// console.log(result1);

// await client.execute({
//   sql: `insert into test (i, t, j, b) values (:i, :t, :j, :b)`,
//   args: {
//     i: 1,
//     t: "foo",
//     j: JSON.stringify({ foo: "bar" }),
//     b: Deno.readFileSync("test.db"),
//   },
// });

// const result = await client.execute({
//   sql: `select * from test`,
//   args: {},
// });

// console.log(result.rows);

import { relations, sql } from "npm:drizzle-orm";
import { integer, sqliteTable, text } from "npm:drizzle-orm/sqlite-core";
import { drizzle } from "npm:drizzle-orm/libsql";
import { createClient } from "npm:@libsql/client/node";

export const users = sqliteTable("users", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
});

const orgs = sqliteTable("orgs", {
  id: integer("id").primaryKey(),
  ownerId: integer("owner_id").notNull(),
  name: text("name").notNull(),
});

const apps = sqliteTable("apps", {
  orgId: integer("org_id").notNull(),
  name: text("name").notNull(),
});

const memberships = sqliteTable("memberships", {
  orgId: integer("org_id").notNull(),
  userId: integer("user_id").notNull(),
  role: text("role").notNull(),
});

export const usersRelations = relations(users, ({ one, many }) => ({
  orgs: many(orgs),
}));

export const orgsRelations = relations(orgs, ({ one, many }) => ({
  owner: one(users, { fields: [orgs.ownerId], references: [users.id] }),
  memberships: many(memberships, { relationName: "member" }),
  apps: many(apps, { relationName: "app" }),
}));

const membershipRelations = relations(memberships, ({ one, many }) => ({
  org: one(orgs, {
    relationName: "member",
    fields: [memberships.orgId],
    references: [orgs.id],
  }),
  user: one(users, { fields: [memberships.userId], references: [users.id] }),
}));

const appRelations = relations(apps, ({ one, many }) => ({
  org: one(orgs, {
    relationName: "app",
    fields: [apps.orgId],
    references: [orgs.id],
  }),
}));

const client = createClient({
  url: ":memory:",
});

const db = drizzle(client, {
  schema: {
    users,
    orgs,
    apps,
    memberships,
    usersRelations,
    orgsRelations,
    membershipRelations,
    appRelations,
  },
});

const result = db.query.orgs.findMany({
  with: {
    owner: {
      where: {},
    },
    memberships: true,
    apps: true,
  },
  where: (f, op) => op.eq(f.id, "1"),
}).toSQL();

console.log(result.sql);
