import Reframe from "@";
import { Asset } from "@reframe/react/shell.tsx";

const tailwindConfig = {
  corePlugins: {
    preflight: false,
  },
  theme: {
    screens: {
      toggle: "812px",
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1536px",
    },
    extend: {
      colors: {
        primary: {
          50: "#e9ecff",
          100: "#d7dcff",
          200: "#b6bdff",
          300: "#8b92ff",
          400: "#635dff",
          500: "#5038ff",
          600: "#4616ff",
          700: "#3f0cf6",
          800: "#330dc6",
          900: "#2e159a",
          950: "#170a48",
        },
      },
      keyframes: {
        "slide-in-20": {
          "0%": {
            transform: `translateX(-${20}px)`,
            opacity: "0",
          },
          "50%": {
            opacity: ".2",
          },
          "100%": {
            transform: "translateX(0px)",
            opacity: "1",
          },
        },
        "slide-in-200": {
          "0%": {
            transform: `translateX(-${200}px)`,
            opacity: "0",
          },
          "50%": {
            opacity: ".2",
          },
          "100%": {
            transform: "translateX(0px)",
            opacity: "1",
          },
        },
        "tilt-4": {
          "0%, 25%, 50%, 75% 100%": {
            transform: "rotate(0deg)",
          },
          "12.5%": {
            transform: `rotate(4deg)`,
          },
          "37.5%": {
            transform: `rotate(-4deg)`,
          },
          "62.5%": {
            transform: `rotate(4deg)`,
          },
          "87.5%": {
            transform: `rotate(-4deg)`,
          },
        },
        "blob-360": {
          "0%, 25%, 50%, 75% 100%": {
            transform: "rotate(0deg) scale(1)",
          },
          "12.5%": {
            transform: `rotate(360deg) scale(1.25)`,
          },
          "37.5%": {
            transform: `rotate(-360deg) scale(1)`,
          },
          "62.5%": {
            transform: `rotate(360deg) scale(1.25)`,
          },
          "87.5%": {
            transform: `rotate(-360deg) scale(1)`,
          },
        },
        "bounce-200": {
          "0%": {
            transform: "translateY(0px)",
          },
          "30%": {
            transform: `translateY(${-75}px)`,
          },
          "50%": {
            transform: `translateY(${0}px)`,
          },
          "70%": {
            transform: `translateY(${75}px)`,
          },
          "100%": {
            transform: `translateY(${0}px)`,
          },
        },
      },
      animation: {
        "slideIn-20": "slide-in-20 .5s ease-in-out forwards",
        "slideIn-200": "slide-in-200 .5s ease-in-out forwards",
        "tilt-4": "tilt-4 .5s ease-in-out forwards",
        "blob-360": "blob-360 10s ease-in-out infinite",
        "bounce-200": "bounce-200 5s linear infinite",
      },
    },
  },
};

const theme = (path: string, defaultValue: string) => {
  const parts = path.split(".");
  let value = tailwindConfig.theme as unknown;
  for (const part of parts) {
    if (!value || typeof value !== "object") {
      return defaultValue;
    }

    value = Reflect.get(value, part);
    if (!value) {
      return defaultValue;
    }
  }

  return value;
};

const preflight = (scope = "") => `
  ${scope} *,
  ${scope} ::before,
  ${scope} ::after {
    box-sizing: border-box; /* 1 */
    border-width: 0; /* 2 */
    border-style: solid; /* 2 */
    border-color: currentColor; /* 2 */
  }

  ${scope} ::before,
  ${scope} ::after {
    --tw-content: '';
  }

  ${scope === "" ? `html, :host` : scope} {
    line-height: 1.5; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
    -moz-tab-size: 4; /* 3 */
    tab-size: 4; /* 3 */
    font-family: ${theme(
      "fontFamily.sans",
      `ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"`
    )}; /* 4 */
    font-feature-settings: ${theme(
      "fontFamily.sans[1].fontFeatureSettings",
      "normal"
    )}; /* 5 */
    font-variation-settings: ${theme(
      "fontFamily.sans[1].fontVariationSettings",
      "normal"
    )}; /* 6 */
    -webkit-tap-highlight-color: transparent; /* 7 */
  }

  ${scope === "" ? `body` : scope} {
    margin: 0; /* 1 */
    line-height: inherit; /* 2 */
  }

  ${scope} hr {
    height: 0; /* 1 */
    color: inherit; /* 2 */
    border-top-width: 1px; /* 3 */
  }

  ${scope} abbr:where([title]) {
    text-decoration: underline dotted;
  }


  ${scope} h1,
  ${scope} h2,
  ${scope} h3,
  ${scope} h4,
  ${scope} h5,
  ${scope} h6 {
    font-size: inherit;
    font-weight: inherit;
  }


  ${scope} a {
    color: inherit;
    text-decoration: inherit;
  }


  ${scope} b,
  ${scope} strong {
    font-weight: bolder;
  }


  ${scope} code,
  ${scope} kbd,
  ${scope} samp,
  ${scope} pre {
    font-family: ${theme(
      "fontFamily.mono",
      `ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace`
    )}; /* 1 */
    font-feature-settings: ${theme(
      "fontFamily.mono[1].fontFeatureSettings",
      "normal"
    )}; /* 2 */
    font-variation-settings: ${theme(
      "fontFamily.mono[1].fontVariationSettings",
      "normal"
    )}; /* 3 */
    font-size: 1em; /* 4 */
  }

  ${scope} small {
    font-size: 80%;
  }

  ${scope} sub,
  ${scope} sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  ${scope} sub {
    bottom: -0.25em;
  }

  ${scope} sup {
    top: -0.5em;
  }

  ${scope} table {
    text-indent: 0; /* 1 */
    border-color: inherit; /* 2 */
    border-collapse: collapse; /* 3 */
  }


  ${scope} button,
  ${scope} input,
  ${scope} optgroup,
  ${scope} select,
  ${scope} textarea {
    font-family: inherit; /* 1 */
    font-feature-settings: inherit; /* 1 */
    font-variation-settings: inherit; /* 1 */
    font-size: 100%; /* 1 */
    font-weight: inherit; /* 1 */
    line-height: inherit; /* 1 */
    letter-spacing: inherit; /* 1 */
    color: inherit; /* 1 */
    margin: 0; /* 2 */
    padding: 0; /* 3 */
  }

  ${scope} button,
  ${scope} select {
    text-transform: none;
  }

  ${scope} button,
  ${scope} input:where([type='button']),
  ${scope} input:where([type='reset']),
  ${scope} input:where([type='submit']) {
    -webkit-appearance: button; /* 1 */
    background-color: transparent; /* 2 */
    background-image: none; /* 2 */
  }

  ${scope} :-moz-focusring {
    outline: auto;
  }

  ${scope} :-moz-ui-invalid {
    box-shadow: none;
  }

  ${scope} progress {
    vertical-align: baseline;
  }

  ${scope} ::-webkit-inner-spin-button,
  ${scope} ::-webkit-outer-spin-button {
    height: auto;
  }

  ${scope} [type='search'] {
    -webkit-appearance: textfield; /* 1 */
    outline-offset: -2px; /* 2 */
  }

  ${scope} ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ${scope} ::-webkit-file-upload-button {
    -webkit-appearance: button; /* 1 */
    font: inherit; /* 2 */
  }

  ${scope} summary {
    display: list-item;
  }


  ${scope} blockquote,
  ${scope} dl,
  ${scope} dd,
  ${scope} h1,
  ${scope} h2,
  ${scope} h3,
  ${scope} h4,
  ${scope} h5,
  ${scope} h6,
  ${scope} hr,
  ${scope} figure,
  ${scope} p,
  ${scope} pre {
    margin: 0;
  }

  ${scope} fieldset {
    margin: 0;
    padding: 0;
  }

  ${scope} legend {
    padding: 0;
  }

  ${scope} ol,
  ${scope} ul,
  ${scope} menu {
    list-style: none;
    margin: 0;
    padding: 0;
  }


  ${scope} dialog {
    padding: 0;
  }


  ${scope} textarea {
    resize: vertical;
  }


  ${scope} input::placeholder,
  ${scope} textarea::placeholder {
    opacity: 1; /* 1 */
    color: ${theme("colors.gray.400", "#9ca3af")}; /* 2 */
  }

  ${scope} button,
  ${scope} [role="button"] {
    cursor: pointer;
  }

  ${scope} :disabled {
    cursor: default;
  }


  ${scope} img,
  ${scope} svg,
  ${scope} video,
  ${scope} canvas,
  ${scope} audio,
  ${scope} iframe,
  ${scope} embed,
  ${scope} object {
    display: block; /* 1 */
    vertical-align: middle; /* 2 */
  }

  ${scope} img,
  ${scope} video {
    max-width: 100%;
    height: auto;
  }

  ${scope} [hidden] {
    display: none;
  }
`;

export const $hack_tailwind = () => {
  import("/:./script.js?directive=client");
};

const getTailwind = async () => {
  const tailwind = await Reframe.sourceGraph.read(
    "/@reframe/tailwind/script.js",
    {
      directive: "client",
    }
  );
  return tailwind.underlying;
};

export const assets = async (scope = ""): Promise<Asset[]> => [
  {
    kind: "style",
    content: preflight(scope),
  },
  {
    kind: "script",
    content: `
    ${await getTailwind()}

    tailwind.config = ${JSON.stringify(tailwindConfig, null, 2)}
    `,
  },
  {
    kind: "style",
    attributes: {
      type: "text/tailwindcss",
    },
    content: `@layer utilities {
      .hug {
        align-self: stretch;
      }

      .hug-0 {
        align-self: auto;
      }
    }`,
  },
];
