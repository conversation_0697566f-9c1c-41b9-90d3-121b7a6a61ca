import Reframe from "@";

import { Thenable } from "@reframe/zero/defs.ts";
import { Async } from "@reframe/react/async.tsx";
import { Auth as AuthCore, AuthConfig, AuthCtx, authStorage } from "./core.ts";
import { Provider } from "./client.tsx";
import { getAuth, signIn, signOut } from "./actions.ts";
import { cookies } from "./cookies.ts";

export { getAuth };

export const createAuth = (
  path: `/${string}`,
  base: Partial<AuthConfig> & Pick<AuthConfig, "secret" | "providers">,
) => {
  const basePath = `${path}/-`;

  const middleware = async (
    request: Request,
    next: (request: Request) => Response | Thenable<Response>,
  ) => {
    const url = new URL(request.url);

    const config: AuthConfig = {
      ...base,
      basePath,
      trustHost: true,
      session: { strategy: "jwt" },
      pages: {
        verifyRequest: "/auth/verify-request",
        newUser: "/auth/new-user",
        ...base.pages,
      },
      cookies: cookies(),
    };

    authStorage.set({ config, auth: null });

    return url.pathname.startsWith(basePath)
      ? AuthCore(request, config)
      : next(request);
  };

  const Auth = (
    { use }: {
      use: (ctx: AuthCtx) => React.ReactNode | Promise<React.ReactNode>;
    },
  ) => <Async value={getAuth} then={use} />;

  Auth.middleware = middleware;

  Auth.signIn = async (
    provider: Parameters<typeof signIn>[0],
    options?: Parameters<typeof signIn>[1],
  ) => {
    const { url, token } = await signIn(provider, options);

    return new Response(null, {
      status: 302,
      headers: {
        location: url,
        "x-session-token": token ?? "",
      },
    });
  };

  Auth.signOut = async (options?: { redirect?: `/${string}` }) => {
    await signOut();

    return new Response(null, {
      status: 302,
      headers: {
        location: options?.redirect ?? base.pages?.signIn ?? "/",
      },
    });
  };

  Auth.Provider = ({ children }: { children: React.ReactNode }) => (
    <Auth
      use={(auth) => (
        <Provider
          value={auth}
          children={children}
        />
      )}
    />
  );

  return Auth;
};
