import Reframe from "@";
import { <PERSON><PERSON> } from "https://deno.land/std@0.224.0/http/cookie.ts";
import { CookiesOptions } from "npm:@auth/core/types";
import { isSecure } from "./core.ts";

export const fixSameSite = (
  value: boolean | "lax" | "strict" | "none" | undefined,
): <PERSON><PERSON>["sameSite"] =>
  value === true || value === "strict"
    ? "Strict"
    : value === "lax"
    ? "Lax"
    : value === "none" || value === false
    ? "None"
    : undefined;

export const cookies = () => {
  const request = Reframe.http.requestStorage.request();
  const url = new URL(request.url);
  const secure = isSecure(request);

  return (({
    sessionToken: {
      name: `${secure ? "__Secure-" : ""}reframe.session-token`,
      options: {
        httpOnly: true,
        sameSite: "none",
        path: "/",
        secure: true,
        domain: url.hostname,
      },
    },
    callbackUrl: {
      name: `${secure ? "__Secure-" : ""}reframe.callback-url`,
      options: {
        httpOnly: true,
        sameSite: "none",
        path: "/",
        secure: true,
        domain: url.hostname,
      },
    },
    csrfToken: {
      name: `${secure ? "__Host-" : ""}reframe.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: "none",
        path: "/",
        secure: true,
        domain: url.hostname,
      },
    },
  }) as const) satisfies Partial<CookiesOptions>;
};
