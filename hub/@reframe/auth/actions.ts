"use server";

import Reframe from "@";
import {
  Auth,
  AuthCtx,
  authStorage,
  BuiltInProviderType,
  createActionURL,
  ProviderType,
  raw,
  skipCSRFCheck,
} from "./core.ts";
import { cookies, fixSameSite } from "./cookies.ts";
import { setCookie } from "https://deno.land/std@0.224.0/http/cookie.ts";

// https://github.com/nextauthjs/next-auth/blob/main/packages/next-auth/src/index.ts
type SignInParams = Parameters<
  <
    P extends BuiltInProviderType | (string & {}),
    R extends boolean = true,
  >(
    /** Provider to sign in to */
    provider?: P, // See: https://github.com/microsoft/TypeScript/issues/29729
    options?:
      | FormData
      | ({
        /** The relative path to redirect to after signing in. By default, the user is redirected to the current page. */
        redirectTo?: string;
        /** If set to `false`, the `signIn` method will return the URL to redirect to instead of redirecting automatically. */
        redirect?: R;
      } & Record<string, any>),
    authorizationParams?:
      | string[][]
      | Record<string, string>
      | string
      | URLSearchParams,
  ) => Promise<R extends false ? any : never>
>;

type SignOutParams = Parameters<
  <R extends boolean = true>(options?: {
    /** The relative path to redirect to after signing out. By default, the user is redirected to the current page. */
    redirectTo?: string;
    /** If set to `false`, the `signOut` method will return the URL to redirect to instead of redirecting automatically. */
    redirect?: R;
  }) => Promise<R extends false ? any : never>
>;

export async function signOut(options?: SignOutParams[0]) {
  const request = Reframe.http.requestStorage.request();
  const { config } = authStorage.get();

  const headers = new Headers(
    Object.fromEntries(request.headers.entries()),
  );

  headers.set("Content-Type", "application/x-www-form-urlencoded");

  const url = createActionURL("signout");

  const callbackUrl = options?.redirectTo ?? headers.get("Referer") ?? "/";
  const body = new URLSearchParams({ callbackUrl });

  const response = await Auth(
    new Request(url, { method: "POST", headers, body }),
    { ...config, raw, skipCSRFCheck },
  );

  for (const c of response?.cookies ?? []) {
    Reframe.http.requestStorage.cookies().set(c.name, c.value, {
      ...c.options,
      sameSite: fixSameSite(c.options.sameSite),
    });
  }

  return response.redirect!;
}

export async function signIn(
  provider: SignInParams[0],
  options: SignInParams[1] = {},
  authorizationParams: SignInParams[2] = {},
): Promise<{ url: string; token?: string }> {
  const request = Reframe.http.requestStorage.request();
  const { config } = authStorage.get();

  const headers = new Headers(
    Object.fromEntries(request.headers.entries()),
  );

  const { redirectTo, ...rest } = options instanceof FormData
    ? Object.fromEntries(options)
    : options;

  const callbackUrl = redirectTo?.toString() ?? headers.get("Referer") ?? "/";

  const signInURL = createActionURL("signin");

  if (!provider) {
    signInURL.searchParams.append("callbackUrl", callbackUrl);

    return { url: signInURL.toString() };
  }

  let url = `${signInURL}/${provider}?${new URLSearchParams(
    authorizationParams,
  )}`;

  let foundProvider: { id?: SignInParams[0]; type?: ProviderType } = {};

  for (const providerConfig of config.providers) {
    const { options, ...defaults } = typeof providerConfig === "function"
      ? providerConfig()
      : providerConfig;

    const id = (options?.id as string | undefined) ?? defaults.id;
    if (id === provider) {
      foundProvider = {
        id,
        type: (options?.type as ProviderType | undefined) ?? defaults.type,
      };
      break;
    }
  }

  if (!foundProvider.id) {
    const url = `${signInURL}?${new URLSearchParams({ callbackUrl })}`;

    return { url };
  }

  if (foundProvider.type === "credentials") {
    url = url.replace("signin", "callback");
  }

  headers.set("Content-Type", "application/x-www-form-urlencoded");

  // @ts-expect-error
  const body = new URLSearchParams({
    ...rest,
    callbackUrl,
  });
  const req = new Request(url, { method: "POST", headers, body });
  const res = await Auth(req, { ...config, raw, skipCSRFCheck });

  const redirectUrl =
    (res instanceof Response ? res.headers.get("Location") : res.redirect) ??
      url;

  if (foundProvider.type === "credentials") {
    return {
      url: redirectUrl,
      token: res?.cookies?.find((c) =>
        c.name === getSessionCookieOptions().name
      )
        ?.value,
    };
  }

  for (const c of res?.cookies ?? []) {
    Reframe.http.requestStorage.cookies().set(c.name, c.value, {
      expires: c.options.expires,
      maxAge: c.options.maxAge,
      domain: c.options.domain,
      path: c.options.path,
      secure: c.options.secure,
      httpOnly: c.options.httpOnly,
      sameSite: fixSameSite(c.options.sameSite),
    });
  }

  return {
    url: redirectUrl,
    token: res?.cookies?.find((c) => c.name === getSessionCookieOptions().name)
      ?.value,
  };
}

const getSessionCookieOptions = () => cookies().sessionToken;

function getToken() {
  const http = Reframe.http.requestStorage;
  const sessionKey = getSessionCookieOptions().name;

  if (http.headers().has("authorization")) {
    return http.headers().get("authorization")!;
  }

  if (http.cookies().get(sessionKey)) {
    return http.cookies().get(sessionKey)!;
  }

  const url = new URL(http.request().url);
  const token = url.searchParams.get("x-authorization");

  if (token) {
    return token;
  }
}

export function getAuth(_token?: string): Promise<AuthCtx> {
  const { config, auth } = authStorage.get();

  if (!_token && auth) {
    return auth;
  }

  const url = createActionURL("session");

  const token = _token ?? getToken();
  const headers = new Headers();

  if (token) {
    const options = getSessionCookieOptions();
    setCookie(headers, {
      ...options.options,
      sameSite: fixSameSite(options.options.sameSite),
      name: options.name,
      value: token,
    });
  }

  const cookie = headers.getSetCookie().join("; ");

  const promise = Auth(
    new Request(url, {
      headers: { cookie },
    }),
    config,
  ).then(async (response): Promise<AuthCtx> => {
    const data = await response.json();

    if (!data || !Object.keys(data).length) {
      return { authenticated: false };
    }

    if (response.status === 200) {
      return { authenticated: true, session: data.user };
    }

    throw new Error(data.message);
  });

  authStorage.set({ config, auth: promise });

  return promise;
}
