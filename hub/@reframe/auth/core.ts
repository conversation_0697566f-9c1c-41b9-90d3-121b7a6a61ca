import Reframe from "@";

import { createActionURL } from "npm:@auth/core";
import type { AuthConfig } from "npm:@auth/core/types";

export { Auth, raw, skipCSRFCheck } from "npm:@auth/core";
export type {
  BuiltInProviderType,
  ProviderType,
} from "npm:@auth/core/providers";

export { default as Google } from "npm:@auth/core/providers/google";
export { default as Credentials } from "npm:@auth/core/providers/credentials";

import type { AuthAction, Session } from "npm:@auth/core/types";

declare module "npm:@auth/core/types" {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string | null;
      image: string | null;
    };
  }
}

declare module "npm:@auth/core/jwt" {
  interface JWT {
    id: string;
    email: string;
    name: string | null;
    image: string | null;
  }
}

export { type AuthConfig };

export type AuthCtx = {
  authenticated: true;
  session: Session["user"];
} | {
  authenticated: false;
};

export const authStorage = Reframe.http.requestStorage.create<{
  auth: Promise<AuthCtx> | null;
  config: AuthConfig;
}>("auth");

export const isSecure = (request: Request) =>
  request.headers.get("x-forwarded-proto")?.startsWith("https") ||
  request.headers.get("x-forwarded-proto")?.startsWith("wss") ||
  request.headers.get("x-forwarded-ssl") === "on";

const _createActionURL = (action: AuthAction) => {
  const { config } = authStorage.get();
  const request = Reframe.http.requestStorage.request();

  return createActionURL(
    action,
    isSecure(request) ? "https" : "http",
    request.headers,
    {},
    config,
  );
};

export { _createActionURL as createActionURL };
