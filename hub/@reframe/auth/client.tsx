"use client";

import { createContext, useContext } from "npm:react@canary";
import { signIn, signOut } from "./actions.ts";
import type { AuthCtx } from "./core.ts";

export const Auth = ({ use }: {
  use: (ctx: AuthCtx) => React.ReactNode;
}) => {
  const context = useContext(AuthContext);

  if (!context) {
    console.error("Auth component must be used within an Auth.Provider");
    return use({ authenticated: false });
  }

  return use(context);
};

Auth.signIn = async (provider: Parameters<typeof signIn>[0]) => {
  const { url } = await signIn(provider);
  window.location.href = url;
};

Auth.signOut = async () => {
  const url = await signOut();
  console.log("signing out", url);
  window.location.href = url;
};

const AuthContext = createContext<AuthCtx | null>(null);

export const Provider = ({ children, value }: {
  children: React.ReactNode;
  value: AuthCtx;
}) => {
  return <AuthContext.Provider value={value} children={children} />;
};
