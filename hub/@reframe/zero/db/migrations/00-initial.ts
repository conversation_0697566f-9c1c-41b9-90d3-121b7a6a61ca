import { Kysely, sql } from "npm:kysely";

export async function up(db: Kysely<unknown>): Promise<void> {
  await db.schema
    .createTable("blob")
    .addColumn("hash", "text", (col) => col.primaryKey().notNull())
    .addColumn("content", "text", (col) => col.notNull())
    .addColumn("size", "integer", (col) => col.notNull())
    .addColumn(
      "created_at",
      "timestamp",
      (col) => col.notNull(),
    )
    .execute();

  await db.schema
    .createTable("file")
    .addColumn("path", "text", (col) => col.primaryKey().notNull())
    .addColumn("hash", "text", (col) => col.notNull())
    .addColumn("headers", "jsonb", (col) => col.notNull())
    .addColumn(
      "created_at",
      "timestamp",
      (col) => col.notNull(),
    )
    .addColumn(
      "updated_at",
      "timestamp",
      (col) => col.notNull(),
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("blob").execute();
  await db.schema.dropTable("file").execute();
}
