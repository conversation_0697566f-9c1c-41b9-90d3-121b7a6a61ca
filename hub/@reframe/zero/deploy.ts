import { walk } from "https://deno.land/std/fs/mod.ts";

import {
  load as loadEnv,
  parse as parseEnv,
} from "https://deno.land/std/dotenv/mod.ts";
import { parse } from "https://deno.land/std@0.200.0/flags/mod.ts";

const args = parse<{
  "add-domain"?: string;
  "provision"?: string;
  domain?: string;
}>(
  Deno.args,
);

const env = await loadEnv();

for (const [key, value] of Object.entries(env)) {
  Deno.env.set(key, value);
}

const accessToken = Deno.env.get("DENO_ACCESS_TOKEN");
const orgId = Deno.env.get("DENO_ORG_ID");
const projectId = Deno.env.get("DENO_PROJECT_ID");

if (!accessToken || !orgId || !projectId) {
  console.error("Missing required environment variables");
  Deno.exit(1);
}

// recursively read all the files from given path
const getFiles = async (base: string, limit: number) => {
  const paths: {
    path: string;
    name: string;
    content: string;
  }[] = [];
  for await (
    const dirEntry of walk(base, {
      skip: [/.git/],
    })
  ) {
    if (dirEntry.isFile) {
      paths.push({
        path: dirEntry.path.slice(base.length),
        name: dirEntry.name,
        content: await Deno.readTextFile(dirEntry.path),
      });
    }

    if (paths.length >= limit) {
      break;
    }
  }

  return paths;
};

const createAPI = () => {
  const _fetch = (url: string, init?: RequestInit) => {
    return fetch(new URL(url, "https://api.deno.com"), {
      ...init,
      headers: {
        authorization: `Bearer ${accessToken}`,
        ...init?.headers,
      },
    });
  };

  const get = (url: string) => _fetch(url);

  const post = (url: string, body: unknown) =>
    _fetch(url, {
      method: "POST",
      headers: {
        "content-type": "application/json",
      },
      body: JSON.stringify(body),
    });

  const patch = (url: string, body: unknown) =>
    _fetch(url, {
      method: "PATCH",
      headers: {
        "content-type": "application/json",
      },
      body: JSON.stringify(body),
    });

  return { get, post, patch };
};

const api = createAPI();

export const getProjects = async () => {
  return api.get(`/v1/organizations/${orgId}/projects`).then((res) =>
    res.json()
  );
};

export const createDeployment = async (body: {
  entryPointUrl: string;
  assets: Record<string, {
    kind: "file";
    content: string;
    encoding: "base64" | "utf-8";
  }>;
  envVars: Record<string, string>;
  description: string;
}) => {
  const result = await api.post(`/v1/projects/${projectId}/deployments`, body)
    .then((res) => res.json());

  const log = await api.get(
    `/v1/deployments/${result.id}/build_logs`,
  );

  if (log.body) {
    const reader = log.body.pipeThrough(new TextDecoderStream()).getReader();

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        break;
      }
      console.log(value);
    }
  }

  return result;
};

export const getLogs = async (deploymentId: string) => {
  const log = await api.get(
    `/v1/deployments/${deploymentId}/app_logs`,
  );

  if (log.body) {
    const reader = log.body.pipeThrough(new TextDecoderStream()).getReader();

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        break;
      }
      console.log(value);
    }
  }
};

export const associateDomain = async (
  deploymentId: string,
  domainId: string,
) => {
  const result = await api.patch(
    `/v1/domains/${domainId}`,
    {
      deploymentId,
    },
  ).then((res) => res.json());

  return result;
};

const getDomain = async (domain: string) => {
  const list = await listDomains() as unknown as {
    domain: string;
    id: string;
  }[];

  return list.find((d) => d.domain === domain);
};

export const deploy = async (app: string, domain?: string) => {
  if (!app.match(/@[a-z0-9-]+\/[a-z0-9-]+/i)) {
    console.error(`Invalid app ${app}, should be in the format @<org>/<name>`);
    Deno.exit(1);
  }

  console.log(`deploying ${app} to ${domain}`);

  const appEnv = parseEnv(
    await Deno.readTextFile(`./${app}/.env.prod`).catch(() => ""),
  );

  const files = await getFiles(`../.build/${app}/src`, Infinity);

  const deployment = await createDeployment({
    entryPointUrl: "entry.ts",
    assets: Object.fromEntries(
      files
        .map((file) => [
          file.path.slice(1),
          {
            kind: "file",
            content: file.content,
            encoding: "utf-8",
          },
        ]),
    ),
    envVars: appEnv,
    description: app,
  });

  const data = domain ? await getDomain(domain) : undefined;

  if (data) {
    const result = await associateDomain(deployment.id, data.id);
    console.log(result);
  }

  await getLogs(deployment.id);
};

export const listDomains = () => {
  return api.get(`/v1/organizations/${orgId}/domains`).then((res) =>
    res.json()
  );
};

export const addDomain = async (domain: string) => {
  // Add a domain to an organization
  // POST /organizations/{organizationId}/domains
  try {
    const response = await api.post(
      `/v1/organizations/${orgId}/domains`,
      { domain },
    );

    if (!response.ok) {
      throw new Error(await response.text());
    }

    return response.json();
  } catch (error) {
    const data = await getDomain(domain);

    if (data) {
      return data;
    }

    throw error;
  }
};

export const provisionDomain = async (domain: string) => {
  const data = await getDomain(domain);

  if (!data) {
    throw new Error(`domain ${domain} not found`);
  }

  const result = await api.post(
    `/v1/domains/${data.id}/certificates/provision`,
    {},
  ).then((res) => res.json());

  return result;
};

if (args["add-domain"]) {
  console.log(await addDomain(args["add-domain"]));
} else if (args["provision"]) {
  console.log(await provisionDomain(args["provision"]));
} else if (Deno.args.length > 0) {
  await deploy(Deno.args[0], args.domain);
}
