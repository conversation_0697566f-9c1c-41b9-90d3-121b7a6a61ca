import { createDbFs } from "./fs/db.ts";
import createRuntime, { db, dbUrl } from "./runtime.ts";

import { createLocalFs } from "./fs/local.ts";
import * as dev from "./dev/sync.ts";
import { createMutex } from "./utils/async/mutex.ts";
import { Store } from "./create/source-graph.ts";
import { Path } from "./defs.ts";
import { evaluate } from "./zero/evaluator/data-url.ts";
import { Runnable } from "./zero/runtime-factory.ts";
import { parse as parseEnv } from "https://deno.land/std/dotenv/mod.ts";
import { ensureDirSync } from "https://deno.land/std@0.188.0/fs/ensure_dir.ts";
import { createModuleReference } from "./create/runtime.ts";
import { createSourceServer } from "./create/source-server.tsx";
import { migrate } from "@reframe/db/migrate.ts";

const createStore = (entry: Path): Store => {
  const commitDir = `../.cache/commit`;
  const commitPath = `${commitDir}/${entry.replace(/\//g, "--")}.json`;

  ensureDirSync(commitDir);

  return {
    async read() {
      try {
        const content = Deno.readTextFileSync(commitPath);
        return JSON.parse(content);
      } catch {
        return {};
      }
    },

    async write(snapshot) {
      const content = JSON.stringify(snapshot, null, 2);

      Deno.writeTextFileSync(commitPath, content);
    },

    async clean() {
      console.log(`cleaning ${commitPath}`);
      try {
        await Deno.remove(commitPath);
      } catch {}
    },
  };
};

export const runtime = createRuntime()
  .withRequire(async (path, attributes) => {
    const directive = await runtime.sourceGraph.getDirective(
      path,
      attributes,
    );

    if (
      directive !== null && directive !== attributes.directive
    ) {
      return { default: createModuleReference(path, directive) };
    }

    const source = await runtime.sourceGraph.read(path, attributes);
    return evaluate<{ default: Runnable }>(source, { path });
  });

export const store = createStore(runtime.entry.fullPath);

export const push = (onWrite?: (path: Path) => void) =>
  dev.push(
    createLocalFs("/"),
    createDbFs(db(), "/hub"),
    onWrite,
  );

if (import.meta.main) {
  await migrate({
    url: dbUrl,
    location: import.meta.dirname!,
  });
  await runtime.sourceGraph.load(store);

  await push((path) => {
    runtime.sourceGraph.invalidate(path);
  });

  const { default: createHookRuntime } = await runtime
    .import<{ default: () => typeof runtime }>(
      "/~@/@/runtime.ts",
      { directive: "server" },
    ).catch(() => ({ default: () => runtime }));

  const prod = Deno.env.get("REFRAME_ENV") === "production";
  const envFile = await Deno.readTextFile(
    `./@${runtime.entry.org}/${runtime.entry.name}/${
      prod ? ".env.prod" : ".env"
    }`,
  ).catch(() => "");

  const hookRuntime = createHookRuntime()
    .extend(() => ({
      path: runtime.path,
      env: {
        ...Deno.env.toObject(),
        ...parseEnv(envFile),
      },
    }));

  try {
    await hookRuntime.sourceGraph.load(store);

    hookRuntime._internals.server.start();
    hookRuntime.serveInternal(await createSourceServer(hookRuntime));

    await hookRuntime.import(`/~@${runtime.entry.fullPath}`, {
      directive: "server",
    });
  } catch (error) {
    console.error(error);
  }

  const cancelSync = dev.sync(
    createLocalFs(`/`),
    {
      write: async (path, content, headers) => {
        const result = await hookRuntime.sourceGraph.write(
          path,
          content,
          headers,
        );

        return result;
      },
    },
  );

  const mutex = createMutex();

  hookRuntime.sourceGraph.onStale(async (event) => {
    if (mutex.isLocked()) {
      return;
    }

    await mutex.lock();

    try {
      for (const path of event.paths) {
        hookRuntime.moduleGraph.invalidate(path);
      }
      await hookRuntime.moduleGraph.cleanup();
      await hookRuntime.import(`/~@${hookRuntime.entry.fullPath}`, {
        directive: "server",
      });

      hookRuntime.serveInternal(await createSourceServer(hookRuntime));

      console.log("RELOADED", hookRuntime.entry.fullPath);
    } catch (error) {
      console.error(error);
    }
    await mutex.release();
  });
}