import { Listable, Path, Readable, Watchable, Writeable } from "../defs.ts";
import { createAsyncTaskQueue } from "../utils/async/queue.ts";

const skip = [
  "/.env",
  "/.env.prod",
];

const copy = async (path: Path, src: Readable, dest: Writeable) => {
  const match = skip.find((suffix) => path.endsWith(suffix));
  if (match) {
    console.log(`skipping ${path}: ends with ${match}`);
    return;
  }

  const body = await src.read(path, {});
  const content = await body.text();

  await dest.write(path, content, body.headers);
};

export const sync = (src: Readable & Watchable, dest: Writeable) => {
  return src.watch("/", async (event) => {
    try {
      await copy(event.path, src, dest);
    } catch (error) {
      console.error(error);
    }
  });
};

export const push = async (
  src: Readable & Listable,
  dest: Writeable & Listable,
  onWrite?: (path: Path) => void,
) => {
  const srcFiles = await src.list("/");
  const destFiles = await dest.list("/");

  const updatedAtDest = new Map<string, Date>(
    destFiles.map(({ path, updatedAt }) => [path, updatedAt]),
  );

  const stats = {
    enqueued: 0,
    skipped: 0,
    done: 0,
  };

  const queue = createAsyncTaskQueue(20, async (srcFile: {
    path: Path;
    updatedAt: Date;
  }) => {
    try {
      if (
        updatedAtDest.has(srcFile.path) &&
        srcFile.updatedAt <= updatedAtDest.get(srcFile.path)!
      ) {
        stats.skipped += 1;
        return;
      }

      await copy(srcFile.path, src, dest);
      onWrite?.(srcFile.path);
      stats.done += 1;
      if (stats.done % 10 === 0) {
        console.log("[push]", JSON.stringify(stats));
      }
    } catch (err) {
      console.log("[error]", srcFile.path, err);
    }
  });

  for (const srcFile of srcFiles) {
    stats.enqueued += 1;
    await queue.enqueue(srcFile);
  }

  await queue.drain(0);
};
