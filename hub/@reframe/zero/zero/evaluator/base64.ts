import type { Body } from "../../body.ts";

import { Module } from "../../defs.ts";

export const evaluate = async <M extends Record<string, unknown> = {}>(
  content: Body,
  type: "text/javascript" | "text/typescript" = "text/javascript",
) => {
  return evaluateText<M>(await content.text(), type);
};

export const evaluateText = async <M extends Record<string, unknown> = {}>(
  content: string,
  type: "text/javascript" | "text/typescript" = "text/javascript",
) => {
  const dataUrl = `data:text/javascript;base64,${btoa(content)}`;

  const module = await import(dataUrl);

  return module as Module<M>;
};
