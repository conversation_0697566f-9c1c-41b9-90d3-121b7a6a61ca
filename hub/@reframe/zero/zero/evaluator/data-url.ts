import type { Body } from "../../body.ts";
import type { Module, Path } from "../../defs.ts";

export const evaluate = async <M extends Record<string, unknown> = {}>(
  content: Body,
  opts: { type?: "text/javascript" | "text/typescript"; path?: Path },
) => {
  return evaluateText<M>(await content.text(), opts);
};

const storeForDebugging = () => {
  const read = () => {
    try {
      return JSON.parse(Deno.readTextFileSync("/tmp/data-url.json"));
    } catch {
      return {};
    }
  };

  const snapshot = {
    current: read(),
  };

  const write = (path: string, url: Path) => {
    snapshot.current[path] = url;
    Deno.writeTextFileSync(
      "/tmp/data-url.json",
      JSON.stringify(snapshot.current, null, 2),
    );
  };

  return { read, write };
};

export function formatStack(stack: string) {
  const parsedData = JSON.parse(Deno.readTextFileSync("/tmp/data-url.json"));
  const regex =
    /blob:null\/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/g;

  return stack.replace(
    regex,
    (match, hash) => (parsedData[match] ?? match).replace("/~@", ""),
  );
}

const debug = storeForDebugging();

export const evaluateText = async <M extends Record<string, unknown> = {}>(
  content: string,
  opts: { type?: "text/javascript" | "text/typescript"; path?: Path },
) => {
  const url = URL.createObjectURL(
    new Blob([content], { type: opts.type ?? "text/javascript" }),
  );

  if (opts.path) debug.write(url, opts.path);

  const module = await import(url);
  // URL.revokeObjectURL(url);

  return module as Module<M>;
};
