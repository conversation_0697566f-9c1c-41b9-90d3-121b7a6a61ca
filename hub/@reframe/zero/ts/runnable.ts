import { createTsSystem, ts } from "./system.ts";
import * as transformers from "./transformers.ts";

const system = createTsSystem();

export const createTranspiler = (
  transformers?: ts.CustomTransformers,
  compilerOptions?: ts.CompilerOptions,
) =>
(path: string, content: string) => {
  // system.set(path, content);

  const result = system.transpile(
    path,
    content,
    {
      transformers,
      compilerOptions,
    },
  );

  return result.outputText;
};

const gensym = (prefix: string) =>
  `${prefix}_${Math.random().toString(36).slice(2)}`;

export const runnable = (
  path: string,
  content: string,
  compilerOptions?: ts.CompilerOptions,
) => {
  const metadata = {
    imports: [] as string[],
    dynamicImports: [] as string[],
    exports: {
      names: [] as string[],
      namespaces: [] as string[],
    },
    directive: "",
  };

  const transpiler = createTranspiler({
    after: [transformers.runnable((sourceFile) => ({
      path: sourceFile.fileName,
      metadata,
      symbols: {
        reframe: gensym("reframe"),
        imports: gensym("imports"),
        exports: gensym("exports"),
        generate: (prefix) => gensym(prefix),
      },
    }))],
  }, compilerOptions);

  const transpiled = transpiler(path, content);

  return {
    transpiled,
    metadata,
  };
};
