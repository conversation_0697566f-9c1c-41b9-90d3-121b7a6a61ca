import Reframe from "@";
import { createSourceServer } from "../create/source-server.tsx";
import { render } from "@reframe/react/server.tsx";
import React, { Suspense } from "npm:react@canary";

import {
  Button,
  For,
  If,
  Link,
  Page,
  Router,
  t,
  Text,
  tw,
  X,
  Y,
} from "@reframe/ui/main.tsx";

import { Scroll as ScrollArea } from "@reframe/ui/components/scrollarea.tsx";
import { Toggle } from "@reframe/ui/components/toggle.tsx";

import { Render } from "@reframe/react/server.tsx";

export const Shell = ({ children }: React.PropsWithChildren<{}>) => {
  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp,container-queries" />
        <script>
          {`tailwind.config = {
                theme: {
                  extend: {
                    colors: {
                      primary: {
                        50: "#e9ecff",
                        100: "#d7dcff",
                        200: "#b6bdff",
                        300: "#8b92ff",
                        400: "#635dff",
                        500: "#5038ff",
                        600: "#4616ff",
                        700: "#3f0cf6",
                        800: "#330dc6",
                        900: "#2e159a",
                        950: "#170a48",
                      },
                      secondary:{
                        darkBlue: "#161C24",
                        coolGray: "#9B9BA6",
                        blue: "#526DFC",
                        lightGray: "#C4CDD5",
                        offWhite: "#F4F6F8",
                        purple: "#C871FD",
                        darkBlueGray: "#111727",
                        lightBlue: "#E9ECFF",
                        lightBlueWhite: "#F6F6FF",
                        lightBlueGray: "#D3D6E6",
                        gray: "#F3F3F3",
                      }
                    }
                  }
                }
              }`}
        </script>
      </head>
      <body>
        <Suspense>{children}</Suspense>
      </body>
    </html>
  );
};

type Node = {
  hash?: string;
  children: Record<string, Node>;
};

const Tree = ({ path, node }: { path: `/${string}`; node: Node }) => {
  return (
    <Y css="gap-1.5 items-start">
      <For
        each={Object.entries(node.children)}
        render={([name, child]) => (
          <If
            condition={!!child.hash}
            then={
              <Link
                css="hover:bg-primary-400/90 hover:text-primary-50 px-2 rounded-lg"
                to={`${path}/${name}`}
              >
                {name}
              </Link>
            }
            otherwise={
              <Toggle
                value={true}
                on={
                  <Y css="gap-1.5">
                    <Text css="cursor-pointer">⏷ {name}</Text>
                    <Y css="pl-4">
                      <Tree node={child} path={`${path}/${name}`} />
                    </Y>
                  </Y>
                }
                off={<Text css="cursor-pointer">⏵ {name}</Text>}
              />
            }
          />
        )}
      />
    </Y>
  );
};

const Code = async ({ hash }: { hash?: string }) => {
  if (!hash) {
    return null;
  }

  const source = await Reframe.sourceGraph.blob.read(
    `/${hash}`,
    {},
  );
  const content = await source.text();

  return (
    <ScrollArea css="h-auto min-h-0">
      <X
        css={[
          "min-h-[100vh] p-8 text-sm ",
          "[&>pre]:!bg-transparent [&>pre]:whitespace-pre-wrap [&>pre]:max-w-full",
          "[&>pre>code]:!bg-transparent",
        ]}
      >
        <pre>{content}</pre>
      </X>
    </ScrollArea>
  );
};

const Iframe = tw("iframe", t`w-full h-full border-0`);

export const Editor = ({ request }: {
  request: Request;
}) => {
  return (
    <Router
      root="/editor"
      request={request}
      route={async (Router) => {
        const x = await Reframe.sourceGraph.serialize();

        const paths = Object.entries(x)
          .map(([path, node]) =>
            [
              path.slice(1).split(
                "?",
              )[0],
              node.hash,
            ] as const
          )
          .filter(([path, hash]) =>
            hash !== null &&
            path.startsWith(`@${Reframe.entry.org}/${Reframe.entry.name}`)
          ).sort((a, b) => a[0].localeCompare(b[0]));

        type Node = {
          hash?: string;
          children: Record<string, Node>;
        };

        const tree: Node = {
          children: {},
        };

        for (const [path, hash] of paths) {
          const segments = path.split("/");
          let node = tree;
          for (const segment of segments) {
            if (segment in node.children) {
              node = node.children[segment];
            } else {
              node.children[segment] = {
                children: {},
              };
              node = node.children[segment];
            }
          }
          node.hash = hash;
        }

        return (
          <Router.Route
            layout={({ children }) => (
              <Shell>
                <Render root="root">
                  <Page>
                    <X css="gap-4 max-h-[100vh] grow bg-secondary-lightBlueWhite flex-1 rounded-tr-xl bg-[url('https://ik.imagekit.io/3cog0usdm/retune%20community/Dots%208px.png?updatedAt=1715102529850')] p-8">
                      <Y css="min-w-96 border bg-white px-8 py-6 rounded-tl-xl rounded-xl shadow-xl text-lg font-normal text-neutral-600 justify-between">
                        <Tree node={tree} path="/editor" />
                        <Link to="/editor/_chat">
                          <Button size="lg" css="text-lg h-14 w-full">
                            I want this 🙋‍♀️
                          </Button>
                        </Link>
                      </Y>
                      {children}
                    </X>
                  </Page>
                </Render>
              </Shell>
            )}
            page={() => <Code hash={paths[0]?.[1]} />}
            route:_chat={(Router) => (
              <Iframe
                src="https://retune.so/share/chat/11ef219d-b206-d8f0-8f45-79ce9175e711/widget"
                css="h-auto rounded-xl"
              />
            )}
            route={(...segments) => () => {
              const path = segments.join("/");
              const file = paths.find(
                ([p]) => p === path,
              ) ?? paths.find(([p]) => p.startsWith(path)) ??
                paths[0];

              return <Code hash={file?.[1]} />;
            }}
          />
        );
      }}
    />
  );
};

const sourceServer = await createSourceServer(Reframe);
export const server = (request: Request) => {
  const url = new URL(request.url);

  if (url.pathname.startsWith("/~editor")) {
    return new Response(
      render(<Editor request={request} />),
      {
        headers: { "content-type": "text/html" },
      },
    );
  }

  return sourceServer(request);
};
