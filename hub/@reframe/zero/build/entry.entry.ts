const cache = new Map<string, unknown>();
const commit = JSON.parse(Deno.readTextFileSync("./commit.json"));

const { $require } = await import("./require.ts") as {
  $require: <T>(path: string) => Promise<T>;
};

const importFromHere = (specifier: string, runtime: unknown) => {
  const hash = commit[specifier].hash;
  if (!hash) {
    throw new Error(`Module not found: ${specifier}`);
  }

  if (cache.has(specifier)) {
    return cache.get(specifier);
  }

  cache.set(
    specifier,
    $require<
      {
        default: (runtime: unknown) => Promise<{
          __esModule: boolean;
        }>;
      }
    >(hash)
      .then(async (unmodule) => {
        const module = await unmodule.default(runtime);

        module.__esModule = true;
        return module;
      }),
  );

  return cache.get(specifier);
};

const createMinimalRuntime = async () => {
  const { resolvePath } = await importFromHere(
    "/~@/@reframe/zero/utils/path.ts?directive=server",
    null,
  ) as {
    resolvePath: (specifier: string, referrer: string) => string;
  };

  const resolve = (specifier: string, referrer: string) => {
    return resolvePath(specifier, referrer);
  };

  const Runtime = (entry: string) => ({
    import: (specifier: string) => {
      if (specifier.startsWith("node:")) {
        return import(specifier);
      }

      if (specifier === "@") {
        return Promise.resolve({ default: Runtime(entry) });
      }

      return importFromHere(
        resolve(specifier, entry),
        Runtime(resolve(specifier, entry)),
      );
    },

    importMany: async (...specifiers: string[]) => {
      const imports = Array.from(new Set(specifiers));

      const modules = await Promise.all(
        imports.map(async (
          specifier,
        ) => [specifier, await Runtime(entry).import(specifier)]),
      );

      return Object.fromEntries(modules);
    },
  });

  return Runtime;
};

const minimalRuntime = await createMinimalRuntime();

const { createRuntime } = await minimalRuntime("/")
  .import("/~@/@reframe/zero/build/entry.ts?directive=server") as {
    createRuntime: (
      require: <T>(path: string) => Promise<T>,
    ) => Promise<unknown>;
  };

await createRuntime($require);
