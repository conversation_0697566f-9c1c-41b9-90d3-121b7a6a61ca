import { Store } from "../create/source-graph.ts";
import {
  createModuleReference,
  createRuntime as _createRuntime,
} from "../create/runtime.ts";
import { createLocalFs, createLocalFsWithHeaders } from "../fs/local.ts";
import { joinAttributes } from "../utils/path.ts";

export const __dynamic = async () => {
  await import("./source-server.tsx");
};

const meta = JSON.parse(Deno.readTextFileSync("./meta.json")) as {
  entry: { org: string; name: string; path: string };
};

const createStore = (): Store => {
  const snapshot = {
    current: JSON.parse(Deno.readTextFileSync("./commit.json")),
  };

  return {
    async read() {
      return snapshot.current;
    },

    async write(data) {
      snapshot.current = data;
    },
  };
};

export const store = createStore();
const snapshot = await store.read();

export const createRuntime = async (
  $require: <T>(path: string) => Promise<T>,
) => {
  const runtime = _createRuntime(
    [`@${meta.entry.org}/${meta.entry.name}${meta.entry.path}`],
    () => createLocalFs("/src"),
    () =>
      createLocalFsWithHeaders("/blob", () => ({
        content: ".content.mjs",
        headers: ".headers.json",
      })),
  )
    .extend(() => ({
      env: {
        ...Deno.env.toObject(),
      },
    }))
    .withRequire(async (path, attributes) => {
      const directive = await runtime.sourceGraph
        .getDirective(path, attributes);

      if (
        directive !== null && directive !== attributes.directive
      ) {
        return { default: createModuleReference(path, directive) };
      }

      const hash = snapshot[joinAttributes(path, attributes)].hash;
      return $require(hash);
    });

  await runtime.sourceGraph.load(store);

  const { server } = await runtime.import<{
    server: (request: Request) => Promise<Response>;
  }>(
    `/~@/@reframe/zero/build/source-server.tsx`,
    {
      directive: "server",
    },
  );
  await runtime._internals.server.start(server);

  await runtime.import(`/~@${runtime.entry.fullPath}`, {
    directive: "server",
  });
};

export {};
