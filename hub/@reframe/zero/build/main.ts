import { createLocalFsWithHeaders } from "@reframe/zero/fs/local.ts";

import { push, runtime, store } from "../run.ts";
import { parse } from "https://deno.land/std@0.200.0/flags/mod.ts";

const args = parse<{
  clean?: boolean;
}>(
  Deno.args,
);

const mergeMaps = <K, V>(...maps: Map<K, V>[]) =>
  maps.reduce(
    (acc, map) => {
      for (const [key, value] of map.entries()) {
        acc.set(key, value);
      }
      return acc;
    },
    new Map<K, V>(),
  );

const runnableEntry = `/~@${runtime.entry.fullPath}?directive=server` as const;

if (args.clean) {
  await store.clean();
}
await runtime.sourceGraph.load(store);
await push((path) => {
  console.log("INVALIDATE", path);
  runtime.sourceGraph.invalidate(path);
});

const hashes = mergeMaps(
  await runtime.sourceGraph.getAllHashes(runnableEntry, { dynamic: true }),
  await runtime.sourceGraph.getAllHashes(
    `/~@/@reframe/zero/build/entry.ts?directive=server`,
    { dynamic: true },
  ),
);

const snapshot = await runtime.sourceGraph.commit();

const buildPath =
  `/../.build/@${runtime.entry.org}/${runtime.entry.name}` as const;

try {
  await Deno.remove(Deno.cwd() + buildPath + "/src", { recursive: true });
} catch (e) {}

const fs = createLocalFsWithHeaders(
  buildPath,
  (path) =>
    [
        ".env",
        "/src/commit.json",
        "/src/deno.json",
        "/src/meta.json",
        "/src/entry.ts",
        "/src/require.ts",
        "/src/fly.toml",
        "/src/Dockerfile",
      ].includes(path)
      ? ({
        content: "",
        headers: ".deployignore",
      })
      : ({
        content: ".content.mjs",
        headers: ".headers.json",
      }),
);

const writeAll = async () => {
  await fs.write(
    "/src/commit.json",
    JSON.stringify(
      Object.fromEntries(
        Object.entries(snapshot),
      ),
      null,
      2,
    ),
    {},
  );

  await fs.write(
    "/src/fly.toml",
    `
app = '${runtime.entry.org}-${runtime.entry.name}'
primary_region = 'sjc'

[build]

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1

[processes]
  app = "task start"
`,
    {},
  );

  await fs.write(
    "/src/Dockerfile",
    `
ARG DENO_VERSION=2.0.0
ARG BIN_IMAGE=denoland/deno:bin-\${DENO_VERSION}
FROM \${BIN_IMAGE} AS bin

FROM frolvlad/alpine-glibc:alpine-3.13

RUN apk --no-cache add ca-certificates

RUN addgroup --gid 1000 deno \
  && adduser --uid 1000 --disabled-password deno --ingroup deno \
  && mkdir /deno-dir/ \
  && chown deno:deno /deno-dir/

ENV DENO_DIR /deno-dir/
ENV DENO_INSTALL_ROOT /usr/local

ARG DENO_VERSION
ENV DENO_VERSION=\${DENO_VERSION}
COPY --from=bin /deno /bin/deno

WORKDIR /deno-dir
COPY . .

ENTRYPOINT ["/bin/deno"]
CMD ["task", "start"]
`,
    {},
  );

  await fs.write(
    "/src/deno.json",
    JSON.stringify(
      {
        "imports": {
          "/": "./",
          "./": "./",
        },
        "unstable": ["cron", "kv", "broadcast-channel"],
        "tasks": {
          "start": "deno run --allow-all entry.ts",
        },
      },
      null,
      2,
    ),
    {},
  );

  await fs.write(
    "/src/meta.json",
    JSON.stringify(
      { entry: runtime.entry },
      null,
      2,
    ),
    {},
  );

  await fs.write(
    "/src/require.ts",
    `
    export const $require = (path) => {
      switch (path) {
        ${
      Array.from(hashes.values())
        .map((hash) =>
          `case "${hash}": return import("./blob/${hash + ".content.mjs"}");`
        )
        .join("\n\t\t")
    }
        default:
          throw new Error("module not found: " + path);
      }
    }
    `,
    {},
  );

  const entryTs = await runtime.sourceGraph.read(
    "/@reframe/zero/build/entry.entry.ts",
    {
      directive: "server",
    },
  )
    .then((body) => body.text());

  await fs.write(
    "/src/entry.ts",
    entryTs,
    {},
  );

  for (const [path, hash] of hashes.entries()) {
    const body = await runtime.sourceGraph.blob.read(`/${hash}`, {});

    const content = await body.text();

    const header = body.headers;

    await fs.write(`/src/blob/${hash}`, content, header);
  }

  for (const [path, node] of Object.entries(snapshot)) {
    const prefix = `/@${runtime.entry.org}/${runtime.entry.name}` as const;
    if (
      node.hash !== null &&
      path.startsWith(prefix + "/")
    ) {
      const body = await runtime.sourceGraph.blob.read(`/${node.hash}`, {});
      await fs.write(`/src/blob/${node.hash}`, await body.text(), body.headers);
    }
  }
};

await writeAll();
