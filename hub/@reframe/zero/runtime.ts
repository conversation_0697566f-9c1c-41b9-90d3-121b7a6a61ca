import { createHttpFs } from "./fs/http.ts";
import { createNpmFs } from "./fs/npm.ts";
import { createRouterFs } from "./fs/router.ts";
import { createCacheFs } from "./fs/cache.ts";
import { createRunnableFs } from "./fs/runnable.ts";
import { createDbFs } from "./fs/db.ts";

import { Path, Readable, Writeable } from "./defs.ts";
import { createRuntime as _createRuntime } from "./create/runtime.ts";
import { createClient } from "@reframe/db/client.ts";
import { createLocalFsWithHeaders } from "./fs/local.ts";
import { DB } from "./db/types.ts";

export const dbUrl = Deno.env.get("DATABASE_URL") ?? "file:../.cache/zero-0.db";
export const db = () => createClient<DB>(dbUrl);

const cache = (path: Path, fs: Readable) =>
  createCacheFs(
    fs,
    createLocalFsWithHeaders(`/../.cache${path}`, () => ({
      content: ".content",
      headers: ".headers",
    })),
  );

const createCompute = (self: () => Readable & Writeable) =>
  createRouterFs()
    .mount("/", () => createDbFs(db(), "/hub"))
    .mount("/~@", () =>
      createRunnableFs(self(), {
        minify: Deno.env.get("REFRAME_ENV") === "production",
      }))
    .mount("/~npm", () =>
      cache("/npm", createNpmFs({ external: "react,react-dom" })))
    .mount("/~http", () =>
      cache("/http", createHttpFs({ ssl: false })))
    .mount("/~https", () => cache("/https", createHttpFs({ ssl: true })));

const createBlob = () =>
  createLocalFsWithHeaders("/../.cache/blob", () => ({
    content: ".content.mjs",
    headers: ".headers.json",
  }));

export default () =>
  _createRuntime(
    Deno.args,
    createCompute,
    createBlob,
  );
