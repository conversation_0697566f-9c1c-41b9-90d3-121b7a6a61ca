export const createAsyncTaskQueue = <Key, M>(
  maxConcurrency: number,
  task: (task: Key) => Promise<M>,
) => {
  const promises = new Map<Key, Promise<M>>();
  const done = new Set<Key>();

  const drain = async (concurrency: number): Promise<void> => {
    if (promises.size <= concurrency) {
      return;
    }

    const entries = Array.from(promises.entries());

    for (const key of entries.map(([key]) => key)) {
      done.add(key);
      promises.delete(key);
    }

    await Promise.all(entries.map(([_, promise]) => promise));

    return drain(concurrency);
  };

  const enqueue = async (key: Key) => {
    if (done.has(key) || promises.has(key)) {
      return;
    }

    await drain(maxConcurrency);
    promises.set(key, task(key));
  };

  return { enqueue, drain, done };
};
