import {
  <PERSON><PERSON>,
  deleteC<PERSON>ie,
  getCookies,
  setCookie,
} from "https://deno.land/std@0.224.0/http/cookie.ts";

import { AsyncLocalStorage } from "@reframe/zero/utils/async/local-storage.ts";

const cloneResponse = (response: Response) =>
  new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: new Headers(response.headers),
  });

export class AsyncRequestStorage extends AsyncLocalStorage<
  {
    request: Request;
    cookies: Map<string, Omit<Cookie, "name">> | null;
    store: Map<symbol, unknown>;
  }
> {
  constructor() {
    super();

    const getStore = this.getStore.bind(this);
    const run = this.run.bind(this);

    const dangerouslyPatch = (fn: "queueMicrotask" | "setTimeout") => {
      const original = globalThis[fn];

      Reflect.set(self, fn, <Args extends unknown[]>(
        callback: (...args: Args) => void,
        ...args: Args
      ) => {
        const store = getStore();

        if (store) {
          return original(
            (...args: Args) => run(store, callback, ...args),
            // @ts-expect-error A spread argument must either have a tuple type or be passed to a rest parameter.deno-ts(2556)
            ...args,
          );
        }

        return original(
          callback,
          // @ts-expect-error A spread argument must either have a tuple type or be passed to a rest parameter.deno-ts(2556)
          ...args,
        );
      });
    };

    dangerouslyPatch("queueMicrotask");
    dangerouslyPatch("setTimeout");
  }

  static symbol<T>(key: string) {
    return Symbol(key) as symbol & {
      __type: T;
    };
  }

  #getStore() {
    const store = this.getStore();

    if (!store) {
      throw new Error("request context not found");
    }

    return store;
  }

  create<T>(key: string) {
    const symbol = AsyncRequestStorage.symbol<T>(key);

    return {
      symbol,
      get: () => this.get(symbol),
      set: (value: T) => this.set(symbol, value),
    };
  }

  get<T>(key: symbol & { __type: T }) {
    if (!this.#getStore().store.has(key)) {
      throw new Error(`key not found: ${String(key)}`);
    }

    return this.#getStore().store.get(key)! as T;
  }

  set<T>(key: symbol & { __type: T }, value: T) {
    this.#getStore().store.set(key, value);
  }

  request() {
    return this.#getStore().request;
  }

  headers() {
    return this.#getStore().request.headers;
  }

  cookies() {
    const cookies = new Map<string, string>(
      Object.entries(getCookies(this.headers())),
    );

    return {
      get: (name: string) => cookies.get(name),
      getAll: () => Object.fromEntries(cookies.entries()),
      set: (
        name: string,
        value: string,
        options: Omit<Cookie, "name" | "value"> = {},
      ) => {
        const cookies = this.#getStore().cookies;

        if (!cookies) {
          throw new Error("response has already been sent");
        }

        console.log("[cookie] set", name, value);

        cookies.set(name, {
          value,
          path: "/",
          httpOnly: true,
          ...options,
        });
      },
      clear: (options: Omit<Cookie, "name" | "value"> = {}) => {
        for (const [name] of cookies.entries()) {
          this.#getStore().cookies?.set(name, {
            value: "",
            path: "/",
            ...options,
          });
        }
      },
    };
  }

  serve(
    serve: (request: Request) => Response | Promise<Response>,
    request: Request,
  ) {
    const cookies = new Map<string, Omit<Cookie, "name">>();
    const store = {
      store: new Map<symbol, unknown>(),
      request,
      cookies: cookies as Map<string, Omit<Cookie, "name">> | null,
    };

    return new Promise<Response>(
      (resolve, reject) =>
        this.run(store, async () => {
          try {
            const originalResponse = await serve(request);

            // return immediately if the request is a websocket upgrade
            if (request.headers.get("upgrade") === "websocket") {
              return resolve(originalResponse);
            }

            const response = cloneResponse(originalResponse);

            // set extra headers
            for (const [name, { value, ...options }] of cookies.entries()) {
              if (value === "") {
                console.log("[cookie] delete", name);
                deleteCookie(response.headers, name, {
                  ...options,
                });
              } else {
                console.log("[cookie] set", name, value);
                setCookie(response.headers, {
                  name,
                  value,
                  ...options,
                });
              }
            }

            store.cookies = null;

            if (!response.body) {
              return resolve(response);
            }

            const [r1, r2] = response.body?.tee();

            resolve(new Response(r1, response));

            // wait until r2 is fully completed
            const reader = r2.getReader();
            while (true) {
              const { done } = await reader.read();

              if (done) {
                break;
              }
            }
          } catch (error) {
            reject(error);
          }
        }),
    );
  }
}
