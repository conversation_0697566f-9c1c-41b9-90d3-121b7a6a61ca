const _AsyncLocalStorage: {
  new <T>(): {
    getStore: () => T | undefined;
    run: <I extends unknown[], O>(
      store: T,
      fn: (...args: I) => O,
      ...args: I
    ) => O;
  };
} = typeof Deno === "undefined"
  ? class AsyncLocalStorage<T> {
    #store: T | undefined;
    constructor() {}

    getStore() {
      return this.#store;
    }

    run<I extends unknown[], O>(
      store: T,
      fn: (...args: I) => O,
      ...args: I
    ) {
      const prevStore = this.#store;
      this.#store = store;
      try {
        return fn(...args);
      } finally {
        this.#store = prevStore;
      }
    }
  }
  : (await import("node:async_hooks")).AsyncLocalStorage;

Reflect.set(
  self,
  "AsyncLocalStorage",
  Reflect.get(self, "AsyncLocalStorage") ??
    _AsyncLocalStorage,
);

export const AsyncLocalStorage = Reflect.get(
  self,
  "AsyncLocalStorage",
) as typeof _AsyncLocalStorage;
