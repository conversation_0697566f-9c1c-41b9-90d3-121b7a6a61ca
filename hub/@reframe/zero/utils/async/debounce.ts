export const asyncDebounce = <
  I extends unknown[],
  O extends unknown,
>(
  ms: number,
  fn: (...args: I) => Promise<O>,
): (...args: I) => Promise<O> => {
  let promise: Promise<O> | null = null;

  return (...args) => {
    if (promise) {
      return promise;
    }

    promise = new Promise((resolve) => {
      setTimeout(() => {
        promise = null;
        resolve(fn(...args));
      }, ms);
    });

    return promise;
  };
};
