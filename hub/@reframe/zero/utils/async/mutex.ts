export const createMutex = () => {
  let state = {} as {
    promise?: Promise<void>;
    resolve?: () => void;
  };

  return {
    isLocked: () => !!state.promise,
    lock: async () => {
      while (state.promise) {
        await state.promise;
      }

      state.promise = new Promise((resolve) => {
        state.resolve = resolve;
      });
    },
    release: () => {
      state.resolve?.();
      state = {};
    },
  };
};
