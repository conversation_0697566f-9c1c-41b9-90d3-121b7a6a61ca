interface WalkEntry extends Deno.FileInfo {
  path: string;
  name: string;
}

import {
  basename,
  join,
  normalize,
  relative,
} from "https://deno.land/std@0.188.0/path/mod.ts";

export interface WalkOptions {
  base?: string;
  /** @default {Infinity} */
  maxDepth?: number;
  /** @default {true} */
  includeFiles?: boolean;
  /** @default {true} */
  includeDirs?: boolean;
  /** @default {false} */
  followSymlinks?: boolean;
  exts?: string[];
  match?: RegExp[];
  skip?: RegExp[];
}

function include(
  path: string,
  exts?: string[],
  match?: RegExp[],
  skip?: RegExp[],
): boolean {
  if (exts && !exts.some((ext): boolean => path.endsWith(ext))) {
    return false;
  }
  if (match && !match.some((pattern): boolean => !!path.match(pattern))) {
    return false;
  }
  if (skip && skip.some((pattern): boolean => !!path.match(pattern))) {
    return false;
  }
  return true;
}

function wrapErrorWithRootPath(err: unknown, root: string) {
  if (err instanceof Error && "root" in err) return err;
  const e = new Error() as Error & { root: string };
  e.root = root;
  e.message = err instanceof Error
    ? `${err.message} for path "${root}"`
    : `[non-error thrown] for path "${root}"`;
  e.stack = err instanceof Error ? err.stack : undefined;
  e.cause = err instanceof Error ? err.cause : undefined;
  return e;
}

async function createWalkEntry(path: string, base: string): Promise<WalkEntry> {
  path = normalize(path);

  const name = basename(path);
  const info = await Deno.stat(path);
  return {
    path: "/" + relative(base, path),
    name,
    ...info,
  };
}

export async function* walk(
  root: string,
  {
    base = root,
    maxDepth = Infinity,
    includeFiles = true,
    includeDirs = true,
    followSymlinks = false,
    exts = undefined,
    match = undefined,
    skip = undefined,
  }: WalkOptions = {},
): AsyncIterableIterator<WalkEntry> {
  if (maxDepth < 0) {
    return;
  }

  if (includeDirs && include(root, exts, match, skip)) {
    yield await createWalkEntry(root, base);
  }

  if (maxDepth < 1 || !include(root, undefined, undefined, skip)) {
    return;
  }

  try {
    for await (const entry of Deno.readDir(root)) {
      if (!entry.name) {
        throw new Error("entry.name is nullish");
      }

      const path = join(root, entry.name);

      const { isDirectory } = entry;

      if (isDirectory) {
        yield* walk(path, {
          base,
          maxDepth: maxDepth - 1,
          includeFiles,
          includeDirs,
          followSymlinks,
          exts,
          match,
          skip,
        });
      } else if (includeFiles && include(path, exts, match, skip)) {
        yield await createWalkEntry(path, base);
      }
    }
  } catch (err) {
    throw wrapErrorWithRootPath(err, normalize(root));
  }
}
