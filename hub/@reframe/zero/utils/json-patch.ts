type JsonValue = string | number | boolean | null | JsonValue[] | Json;

type Json = {
  [key: string]: JsonValue;
};

type MergeValue<T, U> = T extends Json ? U extends Json ? Merge<T, U>
  : U
  : U;

type Merge<T extends Json, U extends JsonValue> = {
  [K in keyof T | keyof U]: K extends keyof T
    ? K extends keyof U ? MergeValue<T[K], U[K]>
    : T[K]
    : K extends keyof U ? U[K]
    : never;
};

const isJson = (value: JsonValue): value is Json =>
  value !== null && typeof value === "object" && !Array.isArray(value);

export const apply = <T extends Json, U extends JsonValue>(
  target: T,
  patch: U,
): Merge<T, U> => {
  if (!isJson(patch)) {
    return patch as Merge<T, U>;
  }

  if (!isJson(target)) {
    target = {} as T;
  }

  for (const key in patch) {
    if (patch[key] === null) {
      if (Reflect.has(target, key)) {
        Reflect.deleteProperty(target, key);
      }
    } else {
      Reflect.set(
        target,
        key,
        apply(
          Reflect.get(target, key) as Json,
          Reflect.get(patch, key) as JsonValue,
        ),
      );
    }
  }

  return target as Merge<T, U>;
};

export const merge = <T extends JsonValue, U extends JsonValue>(
  patch1: T,
  patch2: U,
) => {
  if (!isJson(patch1) || !isJson(patch2)) {
    return patch2;
  }

  const patch = structuredClone(patch1) as Json;

  for (const key in patch2) {
    if (patch1[key] !== undefined) {
      Reflect.set(
        patch,
        key,
        merge(
          Reflect.get(patch, key),
          Reflect.get(patch2 as Json, key),
        ),
      );
    } else {
      patch[key] = patch2[key];
    }
  }

  return patch as MergeValue<T, U>;
};
