import { createFs } from "./create.ts";

const stats = {
  done: 0,
  total: 0,
};

export const createNpmFs = ({
  cdn = "https://esm.sh",
  ...params
}: {
  cdn?: string;
  target?: string;
  external?: string;
  conditions?: string;
}) =>
  createFs((ctx) =>
    ctx
      .read(async (path, headers) => {
        const url = new URL(path, cdn);

        if (headers["directive"] === "server") {
          url.searchParams.set("conditions", "react-server");
          url.searchParams.set("target", "denonext");
        } else {
          url.searchParams.set("target", "esnext");
        }

        if (
          !url.pathname.startsWith("/v135/") &&
          !url.pathname.startsWith("/stable/") &&
          !url.pathname.includes("/inngest")
        ) {
          url.searchParams.set("pin", "v135");
        }

        for (const [key, value] of Object.entries(params)) {
          url.searchParams.set(key, value);
        }

        url.pathname = url.pathname.replace(/@19.2.0-canary-[^/]+/g, '@19.0.0-rc-de68d2f4-20241204');

        console.log(
          "[npm]",
          `[${stats.done}/${++stats.total}]`,
          url.pathname,
          url.searchParams.toString(),
        );

        try {
          const response = await fetch(url);

          if (!response.ok) {
            console.error("ERROR", url.href, await response.text());
            //   throw ctx.notFound(`module not found: ${path}`);
            throw new Error(`module not found: ${path}`);
          }

          if (!response.body) {
            //   throw ctx.notFound(`module does not have any body: ${path}`);
            throw new Error(`module does not have any body: ${path}`);
          }

          return ctx.response(
            response.body,
            Object.fromEntries(response.headers.entries()),
          );
        } finally {
          console.log(
            "[npm]",
            `[${++stats.done}/${stats.total}]`,
            url.pathname,
            url.searchParams.toString(),
          );
        }
      })
  );
