import { getContentType } from "../utils/content-type.ts";
import { createFs } from "./create.ts";
import { Path } from "../defs.ts";
import { Client } from "../db/client.ts";
import { sha256 } from "../utils/hash.ts";
import { relative } from "https://deno.land/std@0.188.0/path/mod.ts";

export const createDbFs = (db: Client, prefix: Path) => {
  return createFs((ctx) =>
    ctx
      .read(async (path) => {
        const fullPath = `${prefix}${path}` satisfies Path;

        const file = await db
          .selectFrom("file")
          .innerJoin("blob", "file.hash", "blob.hash")
          .select(
            ["blob.content", "file.headers"],
          )
          .where("file.path", "=", fullPath)
          .executeTakeFirst();

        if (!file) {
          throw new Error(`file not found: ${fullPath}`);
        }

        return ctx.text(file.content, JSON.parse(file.headers));
      })
      .write(async (path, content, _headers) => {
        const fullPath = `${prefix}${path}` satisfies Path;

        const headers = {
          ..._headers,
          "x-fs-abs-path": fullPath,
          "x-fs-local-stat-size": String(content.length),
          "content-type": getContentType(fullPath),
        };

        const hash = await sha256(content);
        const result = await Promise.all([
          db
            .replaceInto("blob")
            .values({
              hash,
              content,
              size: content.length,
              createdAt: new Date().toISOString(),
            })
            .execute(),
          db
            .replaceInto("file")
            .values({
              path: fullPath,
              hash,
              headers: JSON.stringify(headers),
              updatedAt: new Date().toISOString(),
              createdAt: new Date().toISOString(),
            })
            .execute(),
        ]);

        return ctx.text(content, headers);
      })
      .list(async (path) => {
        const fullPath = `${prefix}${path}` satisfies Path;

        const result = await db
          .selectFrom("file")
          .selectAll()
          .where("file.path", "like", `${fullPath}%`)
          .execute();

        return result.map((row) => ({
          path: `/${relative(fullPath, row.path)}`,
          updatedAt: new Date(row.updatedAt),
        }));
      })
  );
};
