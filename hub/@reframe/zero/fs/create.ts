import * as Body from "../body.ts";

import { Listable, Readable, Watchable, Writeable } from "../defs.ts";

type Ctx<
  Read extends Readable | {} = {},
  Write extends Writeable | {} = {},
  List extends Listable | {} = {},
  Watch extends Watchable | {} = {},
> = {
  handlers:
    & Read
    & Write
    & List
    & Watch;

  text: typeof Body.text;
  json: typeof Body.json;
  response: typeof Body.response;

  read: (
    _: Readable["read"],
  ) => Ctx<Readable, Write, List, Watch>;
  write: (
    _: Writeable["write"],
  ) => Ctx<Read, Writeable, List, Watch>;
  list: (_: Listable["list"]) => Ctx<Read, Write, Listable, Watch>;
  watch: (
    _: Watchable["watch"],
  ) => Ctx<Read, Write, List, Watchable>;
};

export const ensureStartsWithSlash = (path: string) =>
  `/${path.startsWith("/") ? path.slice(1) : path}` as const;

export const createFs = <C extends Ctx = Ctx>(
  factory: (ctx: Ctx) => C,
): C["handlers"] => {
  const helper = <
    Read extends Readable | {} = {},
    Write extends Writeable | {} = {},
    List extends Listable | {} = {},
    Watch extends Watchable | {} = {},
  >(
    handlers: Read & Write & List & Watch,
  ): Ctx<Read, Write, List, Watch> => ({
    handlers,

    text: Body.text,
    json: Body.json,
    response: Body.response,

    read: (read) =>
      helper<{ read: typeof read }, Write, List, Watch>(
        {
          ...handlers,
          read: (path, headers) => read(ensureStartsWithSlash(path), headers),
        },
      ),

    write: (write) =>
      helper<Read, { write: typeof write }, List, Watch>(
        {
          ...handlers,
          write: (path, body, headers) =>
            write(ensureStartsWithSlash(path), body, headers),
        },
      ),

    list: (list) =>
      helper<Read, Write, { list: typeof list }, Watch>(
        { ...handlers, list },
      ),

    watch: (watch) =>
      helper<Read, Write, List, { watch: typeof watch }>(
        { ...handlers, watch },
      ),
  });

  const { handlers: fs } = factory(helper({}));
  return fs;
};
