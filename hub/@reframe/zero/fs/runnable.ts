import { Readable } from "../defs.ts";
import { createFs } from "./create.ts";
import { runnable } from "../ts/runnable.ts";
import { minify as terser } from "npm:terser";

const minify = async (transpiled: string, path: string) => {
  try {
    const { code } = await terser(transpiled);
    if (!code) {
      throw Error("Can't minify the code.");
    }
    return code;
  } catch (error) {
    console.error(`Error minifying ${path}: ${error}`);
    return transpiled;
  }
};

export const createRunnableFs = <C extends Readable>(
  base: C,
  opts: { minify: boolean },
) =>
  createFs((ctx) =>
    ctx
      .read(async (path, headers) => {
        const response = await base.read(path, headers);

        const code = await response.text();

        const {
          transpiled,
          metadata: { imports, dynamicImports, exports, directive },
        } = runnable(path, code, {
          removeComments: false,
          inlineSourceMap: !opts.minify,
        });

        const body = ctx.text(
          opts.minify ? await minify(transpiled, path) : transpiled,
          {
            ...response.headers,
            "x-reframe-imports": imports
              .filter((s) => s !== "@" && !s.startsWith("data:"))
              .join(","),
            "x-reframe-dynamic-imports": dynamicImports
              .filter((s) => s !== "@" && !s.startsWith("data:"))
              .join(","),
            "x-reframe-directive": directive,
            "x-fs-runnable-exported-names": exports.names.join(","),
            "x-fs-runnable-exported-namespaces": exports.namespaces.join(","),
            "content-type": "application/javascript",
          },
        );

        return body;
      })
  );
