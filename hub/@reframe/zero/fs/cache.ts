import { Path, Readable, Writeable } from "../defs.ts";
import { createFs } from "./create.ts";

const join = (path: Path, headers: Record<string, string>) => {
  const url = new URL(path, "cache://");
  for (
    const [key, value] of Object.entries(headers).sort(([a], [b]) =>
      a.localeCompare(b)
    )
  ) {
    url.searchParams.set(key, value);
  }

  return `/${url.pathname.slice(1)}${url.search}` as const;
};

export const createCacheFs = <
  S extends Readable,
  C extends Readable & Writeable,
>(
  source: S,
  cache: C,
  opts: {
    shouldRevalidate?: (path: Path) => boolean;
  } = {},
) => {
  return createFs((ctx) =>
    ctx
      .read(async (path, headers) => {
        try {
          if (opts.shouldRevalidate?.(path) === false) {
            throw "skip";
          }

          const body = await cache.read(join(path, headers), {});

          //   ctx.log("HIT", path);
          return body;
        } catch (_error) {
          //   ctx.log("MISS", path);
          console.log("[cache] MISS", path);
          //   const body = await ctx.forward(source);
          const body = await source.read(path, headers);

          await cache.write(
            join(path, headers),
            await body.clone().text(),
            body.headers,
          );

          return body;
        }
      })
  );
};
