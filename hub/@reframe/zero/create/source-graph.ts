import { Body, Path, Readable, Writeable } from "../defs.ts";
import { createFs } from "../fs/create.ts";
import { asyncDebounce } from "../utils/async/debounce.ts";
import { sha256 } from "../utils/hash.ts";
import { joinAttributes, resolvePath, splitAttributes } from "../utils/path.ts";

type Node = {
  hash: string;
  directive: "server" | "client" | null;
  dependants: Path[];
  imports: string[];
  dynamicImports: string[];
};

const createGraph = () => {
  const map = new Map<Path, {
    hash: string | null;
    directive: "server" | "client" | null;
    dependants: Set<Path>;
    imports: Set<string>;
    dynamicImports: Set<string>;
  }>();

  const safeGet = (path: Path) => {
    if (!map.has(path)) {
      map.set(path, {
        hash: null,
        directive: null,
        dependants: new Set(),
        imports: new Set(),
        dynamicImports: new Set(),
      });
    }
    return map.get(path)!;
  };

  const addDependency = (parent: Path, child: Path) => {
    safeGet(child).dependants.add(parent);
  };

  const addImport = (path: Path, specifier: string, dynamic = false) => {
    safeGet(path)[dynamic ? "dynamicImports" : "imports"].add(specifier);
    // make sure the import is in the graph
    safeGet(resolvePath(specifier, path));
  };

  const dirty = (path: Path) => {
    const node = map.get(path);

    if (!node) {
      return;
    }

    node.hash = null;
    for (const parent of node.dependants) {
      dirty(parent);
    }
  };

  return {
    map,
    get: safeGet,
    set: (path: Path, hash: string, directive: "server" | "client" | null) => {
      safeGet(path).hash = hash;
      safeGet(path).directive = directive;
    },
    delete: (path: Path) => {
      map.delete(path);
    },
    addDependency,
    addImport,
    dirty,
    hash: (path: Path) => {
      return safeGet(path).hash;
    },
  };
};

export type Store = {
  read: () => Promise<Record<Path, Node>>;
  write: (snapshot: Record<Path, Node>) => Promise<void>;
  clean: () => Promise<void>;
};

export const createSourceGraph = (
  blob: Readable & Writeable,
  createCompute: (self: () => Readable & Writeable) => Readable & Writeable,
) => {
  const cache = new Map<Path, Promise<Body>>();

  const graph = createGraph();

  const subscriptions = new Set<
    (source: {
      paths: Path[];
    }) => void
  >();

  const commitHandler = {
    current: null as null | (() => Promise<Record<Path, Node>>),
  };

  const serialize = async () => {
    const snapshot: Record<Path, Node> = {};
    const queue = Array.from(graph.map.keys());
    const skip = new Set<Path>();

    while (queue.length > 0) {
      const path = queue.pop()!;
      const node = graph.get(path);

      if (snapshot[path] || skip.has(path)) {
        continue;
      }

      while (node.hash === null) {
        // this will set the hash
        try {
          const { specifier, attributes } = splitAttributes(path);

          await currentFs.read(specifier as Path, attributes);

          for (const i of node.imports) {
            // console.log("[enqueue]", path, "->", i, "->", resolvePath(i, path));
            queue.push(resolvePath(i, path));
          }

          for (const i of node.dynamicImports) {
            // console.log("[enqueue]", path, "->", i, "->", resolvePath(i, path));
            queue.push(resolvePath(i, path));
          }
        } catch (e) {
          // console.error(e);
          console.warn("[commit] [delete]", path);
          // remove the node from the graph
          graph.delete(path);
          break;
        }
      }

      if (node.hash === null) {
        // errored out while reading, skip
        skip.add(path);
        continue;
      }

      snapshot[path] = {
        hash: node.hash,
        directive: node.directive,
        dependants: Array.from(node.dependants),
        imports: Array.from(node.imports),
        dynamicImports: Array.from(node.dynamicImports),
      };
    }

    return snapshot;
  };

  const commit = asyncDebounce(100, async () => {
    if (!commitHandler.current) {
      console.warn("no store to commit to");
      return;
    }

    console.log("[COMMIT]");

    try {
      return await commitHandler.current();
    } catch (error) {
      console.error("[commit]", error);
    }
  });

  const load = async (store: Store) => {
    const snapshot = await store.read();

    for (
      const [path, { hash, dependants, imports, dynamicImports, directive }]
        of Object
          .entries(snapshot)
    ) {
      graph.set(path as Path, hash, directive);

      for (const dependant of dependants) {
        graph.addDependency(dependant, path as Path);
      }

      for (const i of imports) {
        graph.addImport(path as Path, i);
      }

      for (const i of dynamicImports) {
        graph.addImport(path as Path, i, true);
      }
    }

    commitHandler.current = async () => {
      const snapshot = await serialize();
      await store.write(snapshot);
      return snapshot;
    };
  };

  const onStale = (
    fn: (source: { paths: Path[] }) => void,
  ) => {
    subscriptions.add(fn);

    return () => {
      subscriptions.delete(fn);
    };
  };

  const currentFs = createFs((ctx) =>
    ctx.read(async (_path, headers) => {
      const path = joinAttributes(_path, headers);
      const hash = graph.hash(path);

      if (hash) {
        return blob.read(`/${hash}`, {});
      }

      const compute = createCompute(() =>
        createFs((ctx) =>
          ctx
            .read(
              (path2, headers) => {
                graph.addDependency(path, joinAttributes(path2, headers));
                return currentFs.read(path2, headers);
              },
            )
            .write(currentFs.write)
        )
      );

      if (!cache.has(path)) {
        cache.set(
          path,
          compute.read(_path, headers)
            .then(async (body) => {
              const content = await body.clone().text();
              const newHash = await sha256(content);
              await blob.write(`/${newHash}`, content, body.headers);

              commit().catch((error) => {
                console.error("[commit]", error);
              });

              const directive = body.header("x-reframe-directive") as
                | "server"
                | "client"
                | ""
                | undefined;

              graph.set(path, newHash, !directive ? null : directive);
              cache.delete(path);
              return body;
            }),
        );
      }

      const body = await cache.get(path)!;

      const imports = body.header("x-reframe-imports")?.split(",").filter((x) =>
        x.length > 0 && !x.startsWith("node:")
      ) ?? [];

      const dynamicImports =
        body.header("x-reframe-dynamic-imports")?.split(",").filter((x) =>
          x.length > 0 && !x.startsWith("node:")
        ) ?? [];

      for (const i of imports) {
        graph.addImport(path, i);
      }

      for (const i of dynamicImports) {
        graph.addImport(path, i, true);
      }

      return body;
    })
      .write(async (path, content, headers) => {
        const compute = createCompute(() => currentFs);
        const response = await compute.write(path, content, headers);

        // todo: move path/headers to Request / Response
        const queue = [
          joinAttributes(path, { directive: "server" }),
          joinAttributes(path, { directive: "client" }),
        ];

        for (const path of queue) {
          graph.dirty(path);
        }

        const stale = new Set<Path>();

        while (queue.length > 0) {
          const current = queue.pop()!;

          if (stale.has(current)) {
            continue;
          }

          const node = graph.get(current);

          stale.add(current);
          queue.push(...node.dependants);
        }

        for (const fn of subscriptions) {
          fn({ paths: Array.from(stale) });
        }

        return response;
      })
  );

  const getAllHashes = async (
    path: Path,
    opts?: {
      dynamic: boolean;
    },
  ) => {
    const hashes = new Map<Path, string | null>();
    const queue = [path];

    while (queue.length > 0) {
      const current = queue.pop()!;

      if (hashes.has(current)) {
        continue;
      }

      const node = graph.get(current);
      const { specifier, attributes } = splitAttributes(current);

      while (!node.hash) {
        // this will set the hash
        await currentFs.read(specifier as Path, attributes);
      }

      if (
        node.directive !== null &&
        node.directive !== attributes.directive
      ) {
        if (opts?.dynamic) {
          queue.push(joinAttributes(specifier as Path, {
            ...attributes,
            directive: node.directive,
          }));
        } else {
          hashes.set(current, null);
        }

        continue;
      }

      hashes.set(current, node.hash);

      for (const specifier of node.imports) {
        queue.push(resolvePath(specifier, current));
      }

      if (opts?.dynamic) {
        for (const specifier of node.dynamicImports) {
          queue.push(resolvePath(specifier, current));
        }
      }
    }

    return hashes;
  };

  return {
    read: currentFs.read,
    write: currentFs.write,

    invalidate: (path: Path) => {
      graph.dirty(joinAttributes(path, { directive: "server" }));
      graph.dirty(joinAttributes(path, { directive: "client" }));
    },

    getDirective: async (path: Path, headers: Record<string, string>) => {
      const node = graph.get(joinAttributes(path, headers));
      while (!node.hash) {
        // this will set the hash
        await currentFs.read(path, headers);
      }

      return node.directive;
    },

    blob,

    getAllHashes,

    serialize,
    load,
    commit,

    onStale,
  };
};

export type SourceGraph = ReturnType<typeof createSourceGraph>;
