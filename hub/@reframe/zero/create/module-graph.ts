import type { Module, Path } from "@reframe/zero/defs.ts";

type ModuleNode = {
  module: Promise<unknown>;
  dependants: Set<Path>;
  cleanups: Set<() => Promise<void>>;
};

export const createModuleGraph = () => {
  const cache = new Map<
    string,
    ModuleNode
  >();

  const cleanupPaths = new Map<Path, Set<() => Promise<void>>>();

  const stale = new Set<Path>();

  const invalidate = (path: Path) => {
    const node = cache.get(path);
    if (!node) {
      return;
    }

    console.log("INVALIDATE", path);

    stale.add(path);
    cache.delete(path);

    for (const dependant of node.dependants) {
      invalidate(dependant);
    }
  };

  const print = () => {
    for (const [path, node] of cache) {
      console.log(path, node.dependants);
    }
  };

  const addCleanup = (path: Path, stop: () => Promise<void>) => {
    if (!cleanupPaths.has(path)) {
      cleanupPaths.set(path, new Set());
    }

    // add stop function to cleanupPaths[path] set
    cleanupPaths.get(path)!.add(stop);
  };

  const cleanup = async () => {
    for (const path of stale) {
      if (cleanupPaths.has(path)) {
        for (const stop of cleanupPaths.get(path)!) {
          await stop();
        }

        cleanupPaths.delete(path);
      }
    }

    stale.clear();
  };

  const load = <T extends Record<string, unknown> = {}>(
    path: Path,
    require: () => Promise<Module<T>>,
    importer: Path,
  ): Promise<T> => {
    if (!cache.has(path)) {
      cache.set(path, {
        module: require(),
        dependants: new Set(),
        cleanups: new Set(),
      });
    }

    const node = cache.get(path)!;

    if (importer) {
      node.dependants.add(importer);
    }

    return node.module as Promise<T>;
  };

  return {
    load,
    invalidate,
    print,
    addCleanup,
    cleanup,
  };
};

export type ModuleGraph = ReturnType<typeof createModuleGraph>;
