import {
  Module,
  Path,
  Readable,
  ThenableModule,
  Writeable,
} from "@reframe/zero/defs.ts";
import { createSourceGraph } from "./source-graph.ts";
import { createModuleGraph } from "./module-graph.ts";
import { createZeroRuntime, Runnable } from "../zero/runtime-factory.ts";
import { joinAttributes, resolvePath, splitAttributes } from "../utils/path.ts";
import { parse } from "https://deno.land/std@0.200.0/flags/mod.ts";
import { AsyncLocalStorage } from "../utils/async/local-storage.ts";
import { AsyncRequestStorage } from "@reframe/zero/utils/async/request-storage.ts";
import { formatStack } from "@reframe/zero/zero/evaluator/data-url.ts";

export const parseEntry = (_args: string[]) => {
  const args = parse(_args, {});

  const hook = String(args._[0]);

  // create regex for @(<org)/(<name>)/(<path/to/entry>)
  const match = /^@(?<org>[^/]+)\/(?<name>[^/]+?)(?<path>\/.*)$/.exec(hook);

  if (!match) {
    throw new Error(
      `hook must be in the form of @<org>/<name>/<path/to/path>, found ${hook}`,
    );
  }
  const { org, name, path } = match.groups!;

  return {
    org,
    name,
    path: path as Path,
    fullPath: `/@${org}/${name}${path}` as const,
  };
};

const prepareModule = <T extends {}>(
  module: T | Promise<T> | ThenableModule<T>,
): ThenableModule<T> => {
  if (!(module instanceof Promise)) {
    return prepareModule(Promise.resolve(module));
  }

  if ("status" in module) {
    return module;
  }

  return module
    .then((value) => {
      return Object.assign(module, {
        status: "fulfilled",
        value: Object.isExtensible(value)
          ? Object.assign(value, {
            __esModule: true,
          })
          : value,
      });
    }, (error: unknown) =>
      Object.assign(module, {
        status: "rejected",
        value: error,
      })) as ThenableModule<T>;
};

const requestStorage = new AsyncRequestStorage();
const actionStorage = new AsyncLocalStorage<{
  endpoint: string;
  headers: Record<string, string | undefined>;
}>();

export const createModuleReference = (
  path: Path,
  directive: "server" | "client",
): Runnable => {
  if (directive === "client") {
    const createReference = (path: Path, name: string): unknown =>
      new Proxy<(...args: unknown[]) => unknown>(
        () => {
          throw new Error(
            `Cannot render client component ${name} from server.`,
          );
        },
        {
          get: (_, property) => {
            if (typeof property === "symbol") {
              return undefined;
            }

            switch (property) {
              case "$$typeof":
                return Symbol.for("react.client.reference");
              case "$$id":
                return path + "#" + name;
              case "$$async":
                return true;
              case "then":
                return undefined;
              case "name":
                return name;
              case "toJSON":
                return;
            }

            return undefined;
          },
        },
      );

    return async () => (new Proxy<Module<Record<string, unknown>>>({
      __esModule: true,
    }, {
      get: (_, name) => {
        if (typeof name === "symbol") {
          return undefined;
        }

        switch (name) {
          case "then":
            return undefined;
          case "__esModule":
            return true;
        }

        return createReference(path, name as string);
      },
    }));
  }

  if (directive === "server") {
    return async (runtime) => {
      const createActionReference =
        (name: string) => async (...args: unknown[]) => {
          const { createFromReadableStream, encodeReply } = await runtime
            .import<{
              createFromReadableStream: (
                body: ReadableStream<Uint8Array>,
                _: unknown,
              ) => unknown;
              encodeReply: (
                args: unknown,
              ) => Promise<ReadableStream<Uint8Array>>;
            }>("npm:react-server-dom-webpack@canary/client.edge", {});

          const storage = actionStorage.getStore();

          const endpoint = storage?.endpoint ?? self.__reframe?.origin ?? "/";
          const response = await fetch(endpoint, {
            method: "POST",
            headers: {
              ...storage?.headers,
              Accept: "text/x-component",
              "x-react-server-action": `${path}#${name}`,
            },
            body: await encodeReply(args),
          });

          if (!response.body) {
            throw new Error(`No body in response`);
          }

          if (!response.ok) {
            throw new Error(
              `Error ${response.status} - ${path}#${name}\n${await response
                .text()}`,
            );
          }

          return await createFromReadableStream(response.body, {
            serverConsumerManifest: {},
          });
        };

      const createReference = <T extends object>(
        base: "" | `${string}.`,
        target: T,
      ): T =>
        new Proxy<T>(target, {
          get: (_, name) => {
            if (typeof name === "symbol") {
              return undefined;
            }

            switch (name) {
              case "then":
                return undefined;
              case "__esModule":
                return true;
            }

            return createReference(
              `${base}${name}.`,
              createActionReference(base + name),
            );
          },
        });

      return createReference("", {} as Module<Record<string, unknown>>);
    };
  }

  throw new Error(`Unknown directive ${directive}`);
};

// TODO: make it thinner, move server, etc to a separate file
// so we can have a thin runtime for the client
export function createRuntime(
  args: string[],
  createCompute: (self: () => Readable & Writeable) => Readable & Writeable,
  createBlob: () => Readable & Writeable,
) {
  if (typeof Deno !== "undefined") {
    Reflect.deleteProperty(self, "window");
  }

  const entry = parseEntry(args);

  const blob = createBlob();
  const sourceGraph = createSourceGraph(blob, createCompute);
  const moduleGraph = createModuleGraph();

  const http = {
    requestStorage,
    action: {
      storage: actionStorage,
      createContext: (
        ctx: () => {
          endpoint: string;
          headers: Record<string, string | undefined>;
        },
      ) => {
        const run = <I extends unknown[], O extends unknown>(
          fn: (...args: I) => O,
          ...args: I
        ) => actionStorage.run(ctx(), fn, ...args);

        const createClient = <T extends {}>(module: T) =>
          new Proxy(module, {
            get: (_, name) => {
              if (typeof name === "symbol") {
                return undefined;
              }

              const fn = Reflect.get(module, name);

              if (typeof fn !== "function") {
                return fn;
              }

              return (...args: unknown[]) =>
                run(fn as (...args: unknown[]) => unknown, ...args);
            },
          });

        return { run, createClient };
      },
    },
  };

  const server = {
    our: null as null | ((req: Request) => Response | Promise<Response>),
    their: null as null | ((req: Request) => Response | Promise<Response>),
    start: () => {
      const serve = (request: Request) => {
        const url = new URL(request.url);

        if (url.pathname.startsWith("/~/")) {
          if (!server.our) {
            throw new Error("server.our is not implemented");
          }

          return server.our(request);
        }

        if (server.their) {
          return server.their(request);
        }

        throw new Error("server unimplemented");
      };

      return Deno.serve(
        {
          onError: (error) => {
            if (error instanceof Error) {
              return new Response(formatStack(error.stack ?? error.message), {
                status: 500,
              });
            }

            console.error(error);

            if (
              typeof error === "object" &&
              (error === null || error.constructor === Object)
            ) {
              return new Response(JSON.stringify(error), { status: 500 });
            }

            return new Response(String(error), { status: 500 });
          },
          port: Deno.env.get("PORT") ? Number(Deno.env.get("PORT")) : 8000,
        },
        (originalRequest) => {
          if (
            originalRequest.headers.get("upgrade") === "websocket"
          ) {
            return requestStorage.serve(serve, originalRequest);
          }

          const newHeaders = new Headers(
            Object.fromEntries(originalRequest.headers.entries()),
          );

          if (newHeaders.get("x-forwarded-proto") === null) {
            newHeaders.set(
              "x-forwarded-proto",
              new URL(originalRequest.url).protocol.replace(":", ""),
            );
          }

          const url = new URL(originalRequest.url);
          url.protocol = newHeaders.get("x-forwarded-proto")!;

          const request = new Request(url, {
            ...originalRequest,
            method: originalRequest.method,
            headers: newHeaders,
            body: originalRequest.body,
          });

          return requestStorage.serve(serve, request);
        },
      );
    },
  };

  const effect = async (fn: () => Promise<() => Promise<void>>) => {
    const stop = await fn();

    moduleGraph.addCleanup(entry.fullPath, stop);
  };

  const serve = async (
    serve: (request: Request) => Response | Promise<Response>,
  ) => {
    return effect(async () => {
      server.their = serve;
      console.log(`serving ${entry.fullPath} at ${performance.now()}`);

      return async () => {
        server.their = null;
      };
    });
  };

  const serveInternal = async (
    serve: (request: Request) => Response | Promise<Response>,
  ) => {
    return effect(async () => {
      server.our = serve;
      console.log(`serving ${entry.fullPath} at ${performance.now()}`);

      return async () => {
        server.our = null;
      };
    });
  };

  const crons = new Map<string, {
    controller: AbortController;
    pending: null | Promise<void>;
    job: () => Promise<void> | void;
  }>();

  const cron = async (
    name: string,
    schedule: string | Deno.CronSchedule,
    handler: (signal: AbortSignal) => Promise<void> | void,
    options: { backoffSchedule?: number[]; signal?: AbortSignal } = {},
  ) => {
    try {
      const prod = Deno.env.get("REFRAME_ENV") === "production";
      const controller = new AbortController();

      // if the signal is aborted, cancel the cron job
      options.signal?.addEventListener("abort", () => {
        controller.abort();
      });

      const job = async () => {
        try {
          const cron = crons.get(name)!;
          if (cron.controller.signal.aborted) {
            return;
          }

          if (cron.pending) {
            console.log(
              `cron ${name} is already with handler ${handler.toString()}, hence skipping`,
            );
            return;
          }

          const promise = cron.job();

          if (promise) {
            cron.pending = promise;

            await promise;

            cron.pending = null;
          }
        } catch (error) {
          console.error(`error while running cron ${name}`, error);
        }
      };

      if (!crons.has(name)) {
        crons.set(name, {
          controller,
          job: () => handler(controller.signal),
          pending: null,
        });

        // run immediately and then schedule

        const promise = Deno.cron(name, schedule, {
          backoffSchedule: options.backoffSchedule,
        }, () => {
          console.log(
            `triggering cron ${name} with handler ${handler.toString()}`,
          );
          return job();
        });

        return await promise;
      }

      const prev = crons.get(name)!;
      if (!prev.controller.signal.aborted) {
        console.log(`aborting cron ${name}`, prev.controller.signal);
        prev.controller.abort();
        console.log(`aborted cron ${name}`, prev.controller.signal);
      }

      crons.set(name, {
        controller,
        job: () => handler(controller.signal),
        pending: null,
      });

      if (!prod) {
        await job();
      }
    } catch (error) {
      console.error(`error while running cron ${name}`, error);
    }
  };

  return createZeroRuntime()
    .extend(() => {
      return {
        entry,
        args,
        env: typeof Deno === "undefined" ? {} : Deno.env.toObject(),
        path: entry.fullPath,
        directive: "server" as "server" | "client",

        resolve: (specifier: string, referrer: Path) =>
          resolvePath(specifier, referrer),

        sourceGraph,
        moduleGraph,

        _internals: { server },

        effect,
        serve,
        serveInternal,
        cron,
        http,
      };
    })
    .extend((factory) => {
      return {
        withRequire: (
          require: (
            path: Path,
            attributes: Record<string, string>,
          ) => Promise<{ default: Runnable }>,
        ) =>
          factory().extend((factory) => {
            const _import = (
              specifier: string,
              attributes: Record<string, string> = {},
            ) => {
              if (
                specifier.startsWith("node:") ||
                // support sqlite native module
                ["npm:@libsql/client/node"].includes(specifier)
              ) {
                if (
                  typeof window !== "undefined" &&
                  typeof Deno === "undefined"
                ) {
                  return {};
                }

                return import(specifier);
              }

              if (specifier.startsWith("data:")) {
                // todo: nested imports inside data urls won't work
                // they won't be resolved with our runtime
                // we need to convert them to runnables before
                // they can be imported
                console.log("[[data]]", specifier);
                return import(specifier);
              }

              const runtime = factory();

              if (specifier === "@") {
                return { default: runtime };
              }

              attributes.directive ??= runtime.directive;
              const path = runtime.resolve(specifier, runtime.path);

              if (!["server", "client"].includes(attributes.directive)) {
                throw new Error(
                  `Invalid directive ${attributes.directive} for ${path} (from ${runtime.path})`,
                );
              }

              return runtime.moduleGraph.load(
                joinAttributes(path, attributes),
                async () => {
                  const runnable = await require(path, attributes);
                  const module = await runnable.default(
                    runtime.extend(() => ({
                      path,
                      directive: attributes.directive,
                    })),
                  );

                  return module;
                },
                joinAttributes(runtime.path, {
                  directive: runtime.directive,
                }),
              );
            };

            const __import = <M extends {}>(
              specifier: string,
              attributes: Record<string, string>,
            ): ThenableModule<M> =>
              prepareModule(
                _import(specifier, attributes) as
                  | Module<M>
                  | Promise<Module<M>>,
              );

            return {
              import: __import,

              importMany: async (...paths: string[]) => {
                return Object.fromEntries(
                  await Promise.all(
                    paths.map(async (path) => {
                      const { specifier, attributes } = splitAttributes(path);
                      return [path, await __import(specifier, attributes)];
                    }),
                  ),
                );
              },
            };
          }),
      };
    });
}

export type Runtime = ReturnType<
  ReturnType<typeof createRuntime>["withRequire"]
>;

export default new Proxy({}, {
  get: (_, key: string) => {
    throw new Error(`Runtime.${key} is not defined`);
  },
}) as Runtime;
