import { Runtime } from "./runtime.ts";

export const createSourceServer = async (runtime: Runtime) => {
  const module = await runtime.import<{
    App: { createServer: () => (request: Request) => Response };
  }>("/~@/@reframe/app/lib/editor.tsx", {
    directive: "server",
  });

  const server = module.App.createServer();

  return (original: Request) => {
    const url = new URL(original.url);
    url.pathname = url.pathname.slice(2);

    const request = new Request(url, {
      ...original,
      method: original.method,
      headers: original.headers,
      body: original.body,
    });

    return server(request);
  };
};
