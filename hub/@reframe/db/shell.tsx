import React, { Suspense } from "npm:react@canary";

export const Shell = ({
  children,
}: {
  children?: React.ReactNode;
}) => {
  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp,container-queries" />
        <script>
          {`tailwind.config = {
              theme: {
                extend: {
                  colors: {
                    primary: {
                      50: "#e9ecff",
                      100: "#d7dcff",
                      200: "#b6bdff",
                      300: "#8b92ff",
                      400: "#635dff",
                      500: "#5038ff",
                      600: "#4616ff",
                      700: "#3f0cf6",
                      800: "#330dc6",
                      900: "#2e159a",
                      950: "#170a48",
                    }
                  }
                }
              }
            }`}
        </script>
        <style type="text/tailwindcss">
          {`@layer utilities {
            .hug {
              align-self: stretch;
            }

            .hug-0 {
              align-self: auto;
            }

            .wrap {
              flex-wrap: wrap;
            }
          }`}
        </style>
      </head>
      <body>
        <Suspense>{children}</Suspense>
      </body>
    </html>
  );
};
