import * as path from "node:path";
import * as fs from "node:fs/promises";
import { LibsqlDialect } from "./libsql-dialect.ts";
import { Cli } from "npm:kysely-codegen/dist/cli/index.js";
import { parse } from "https://deno.land/std@0.200.0/flags/mod.ts";
import { parse as parseEnv } from "https://deno.land/std/dotenv/mod.ts";
import { ensureDirSync } from "https://deno.land/std@0.188.0/fs/ensure_dir.ts";

import { FileMigrationProvider, Kysely, Migrator } from "npm:kysely";

export async function migrate(
  { url, authToken, location, down }: {
    url: string;
    authToken?: string;
    location: string;
    down?: boolean;
  },
) {
  const db = new Kysely<unknown>({
    dialect: new LibsqlDialect({ url, authToken }),
  });

  console.log(`migrating to latest in ${location}: ${url}`);

  const migrator = new Migrator({
    db,
    provider: new FileMigrationProvider({
      fs,
      path,
      // This needs to be an absolute path.
      migrationFolder: path.resolve(location, "db/migrations"),
    }),
  });

  const { error, results } = await (
    !down ? migrator.migrateToLatest() : migrator.migrateDown()
  );

  results?.forEach((it) => {
    if (it.status === "Success") {
      console.log(`migration "${it.migrationName}" was executed successfully`);
    } else if (it.status === "Error") {
      console.error(`failed to execute migration "${it.migrationName}"`);
    }
  });

  if (error) {
    await db.destroy();
    throw error;
  }

  await db.destroy();
}

if (import.meta.main) {
  ensureDirSync(path.join(import.meta.dirname!, "../../../.cache"));

  const args = parse<{ path?: string; prod?: boolean; down?: boolean }>(
    Deno.args,
  );

  const APP = String(args._[0]);

  if (
    !APP || !APP.match(/@[a-z0-9-]+\/[a-z0-9-]+/i)
  ) {
    console.error("db/migrate.ts @<org>/<app> [--prod]");
    Deno.exit(1);
  }

  const appEnv = parseEnv(
    await Deno.readTextFile(`./${APP}/${args.prod ? ".env.prod" : ".env"}`)
      .catch(
        () => "",
      ),
  );

  if (!appEnv.DATABASE_URL) {
    console.error(
      `missing DATABASE_URL in ./${APP}/${args.prod ? ".env.prod" : ".env"}`,
    );
    Deno.exit(1);
  }

  const appLocation = path.join(import.meta.dirname!, "../..", APP);

  await migrate({
    url: appEnv.DATABASE_URL,
    authToken: appEnv.DATABASE_AUTH_TOKEN,
    down: args.down,
    location: appLocation,
  });

  console.log(`generating types in ${appLocation}`);

  await new Cli()
    .generate({
      camelCase: true,
      dialectName: "libsql",
      url: `${appEnv.DATABASE_URL}${
        appEnv.DATABASE_AUTH_TOKEN
          ? `?authToken=${appEnv.DATABASE_AUTH_TOKEN}`
          : ""
      }`,
      print: false,
      outFile: path.join(appLocation, "db/types.ts"),
      schemas: [],
    });

  // read the generated file
  const generated = Deno.readTextFileSync(
    path.join(appLocation, "db/types.ts"),
  );

  // replace "kysely" with "npm:kysely"
  const replaced = generated.replace(/"kysely"/g, '"npm:kysely"');

  // write the generated file
  Deno.writeTextFileSync(path.join(appLocation, "db/types.ts"), replaced);
  Deno.writeTextFileSync(
    path.join(appLocation, "db/client.ts"),
    `import Reframe from "@";
import * as db from "@reframe/db/client.ts";
import { DB } from "./types.ts";

export const createClient = () =>
  db.createClient<DB>(
    Reframe.env.DATABASE_URL,
    Reframe.env.DATABASE_AUTH_TOKEN,
  );

export type Client = ReturnType<typeof createClient>;
`,
  );
}
