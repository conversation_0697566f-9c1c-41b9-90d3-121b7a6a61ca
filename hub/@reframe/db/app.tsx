import { Link, Text, X, Y } from "@reframe/ui/main.tsx";
import { createRoute } from "@reframe/react/router.tsx";
import { Suspense } from "npm:react@canary";
import { Sidebar } from "./screens/sidebar.tsx";
import { SingleTable } from "./screens/single-table.tsx";
import { CreateTableForm } from "./screens/create-table.tsx";
import { EditTable } from "./screens/edit-table.tsx";
import { Console } from "./screens/console.tsx";
import { Shell } from "./shell.tsx";
import { Render } from "@reframe/react/server.tsx";
import { getDatabases, getIndices, getTabledata, getTables } from "./action.ts";
import { render, reply } from "@reframe/react/server.tsx";
import { ShowAllDB } from "./screens/show-all-db.tsx";
import { Async } from "@reframe/react/async.tsx";

export const App = createRoute<{ request: Request }>((Router) => (
  <Router.Route
    context={async (prev) => {
      const url = new URL(prev.request.url);
      const query = Object.fromEntries(url.searchParams.entries());

      return { query };
    }}
    render={(element) =>
      new Response(
        render(
          <Shell>
            <Render onError={console.error}>
              <Suspense>
                {element}
              </Suspense>
            </Render>
          </Shell>,
        ),
        { headers: { "content-type": "text/html" } },
      )}
    middleware={async (request, next) => {
      const url = new URL(request.url);

      if (url.pathname.endsWith(".ico")) {
        return new Response(null, { status: 404 });
      }

      if (request.headers.get("x-react-server-action")) {
        return reply(request);
      }

      return next(request);
    }}
    page={() => (
      <Y min-x="200px">
        <Async
          value={() => getDatabases()}
          then={(databases) => (
            <ShowAllDB
              databases={databases}
            />
          )}
        />
      </Y>
    )}
    route:db={(Router) => (
      <Router.Route
        layout={(Router) => (
          <Y css="gap-4 grow">
            <Router.Outlet />
          </Y>
        )}
        route={(dbName) => (Router) => (
          <Router.Route
            context={async (prev) => {
              const url = new URL(prev.request.url);
              const dbName = url.pathname.split("/")[2];
              return { dbName };
            }}
            layout={(Router) => (
              <Y css="gap-4 bg-neutral-950">
                <X css="gap-4 items-center ">
                  <X
                    css="gap-4 p-4 text-neutral-300 bg-neutral-900 rounded-lg"
                    max-x="20%"
                    min-x="20%"
                  >
                    <Link to="/">← Back</Link>
                  </X>

                  <X css="gap-2 p-4 grow items-center bg-neutral-900 rounded-lg">
                    <Text css="text-lg text-neutral-100">
                      Database Manager &gt;
                    </Text>

                    <Text css="text-lg text-neutral-100">
                      {dbName}
                    </Text>
                  </X>
                </X>
                <X css="grow gap-4">
                  <Async
                    value={() => getTables()}
                    then={(tables) => (
                      <Sidebar
                        path={Router.path}
                        tables={tables}
                      />
                    )}
                  />
                  <Router.Outlet />
                </X>
              </Y>
            )}
            page={() => <Console />}
            route:create={(Router) => (
              <Router.Route
                page={() => (
                  <CreateTableForm
                    path={Router.path}
                  />
                )}
              />
            )}
            route:table={(Router) => (
              <Router.Route
                layout={(Router) => (
                  <Y css="gap-2  grow">
                    <Router.Outlet />
                  </Y>
                )}
                route={(tableName) => (Router) => (
                  <Router.Route
                    context={async (prev) => {
                      const tables = await getTables();
                      const table = tables.find((t) => {
                        return t.name === tableName;
                      });
                      const page = Number(prev.query.page ?? 1);
                      const data = await getTabledata(tableName, {
                        offset: 25 * (Number(page) - 1),
                        limit: 25,
                      });
                      const indices = await getIndices(tableName);

                      return { table, data, indices, page };
                    }}
                    page={(Router) => (
                      <Router.Context
                        use={({ table, data, indices, page }) => (
                          <SingleTable
                            page={page}
                            path={Router.path}
                            name={tableName}
                            data={data}
                            columns={table?.columns || []}
                            indices={indices || []}
                          />
                        )}
                      />
                    )}
                    route:edit={(Router) => (
                      <Router.Route
                        page={(Router) => (
                          <Router.Context
                            use={({ table, data, indices }) => (
                              <EditTable
                                name={tableName}
                                columns={table?.columns || []}
                                indices={indices || []}
                              />
                            )}
                          />
                        )}
                      />
                    )}
                  />
                )}
              />
            )}
          />
        )}
      />
    )}
  />
));
