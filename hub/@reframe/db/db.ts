import { CamelCasePlugin, Kysely } from "npm:kysely";
import { LibsqlDialect } from "npm:@libsql/kysely-libsql";

export const createClient = <
  DB extends {} = {
    [key: string]: unknown;
  },
>(url: string, authToken?: string) => {
  const db = new Kysely<DB>({
    dialect: new LibsqlDialect({
      url,
      authToken,
    }),
    plugins: [new CamelCasePlugin()],
  });

  return db;
};

export type Client<
  DB extends {} = {
    [key: string]: unknown;
  },
> = ReturnType<typeof createClient<DB>>;

export { sql } from "npm:kysely";
