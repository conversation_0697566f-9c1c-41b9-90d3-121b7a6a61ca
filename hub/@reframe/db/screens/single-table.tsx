"use client";

import { Button, Input, Text, X, Y } from "@reframe/ui/main.tsx";
import { Link } from "@reframe/ui/components/router.tsx";
import { useState } from "npm:react@canary";

import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "./table.tsx";

import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "./pagination.tsx";
import { RowDrawer } from "./edit-row.tsx";

const IndicesTable = ({ indices }: {
  indices: Array<{
    name: string;
    columns: string[];
    unique: boolean;
  }>;
}) => {
  const headerTitles = ["Index", "Columns", "Unique"];
  return (
    <Table>
      <TableHeader>
        <TableRow>
          {headerTitles.map((title) => {
            return <TableHead css="text-neutral-100">{title}</TableHead>;
          })}
        </TableRow>
      </TableHeader>
      <TableBody>
        {indices.map((index) => (
          <TableRow>
            <TableCell>{index.name}</TableCell>
            <TableCell>{index.columns.join(",b")}</TableCell>
            <TableCell>{index.unique ? "true" : "false"}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

const Paginations = ({ path, n, totalPages }: {
  path: string;
  n: number;
  totalPages: number;
}) => {
  const page = Number(n);

  return (
    <Pagination>
      <PaginationContent>
        <PaginationItem>
          <Link to={`${path}/${page - 1}`}>
            <PaginationPrevious disabled={page === 1} />
          </Link>
        </PaginationItem>

        {page !== 1 && (
          <PaginationItem>
            <Link to={`${path}/${page - 1}`}>
              <PaginationLink>
                {page - 1}
              </PaginationLink>
            </Link>
          </PaginationItem>
        )}

        <PaginationItem css="bg-neutral-100 text-neutral-300">
          <PaginationLink isActive>
            {page}
          </PaginationLink>
        </PaginationItem>

        {page + 1 < totalPages && (
          <PaginationItem>
            <Link to={`${path}/${page + 1}`}>
              <PaginationLink>
                {page + 1}
              </PaginationLink>
            </Link>
          </PaginationItem>
        )}

        <PaginationItem>
          <Link to={`${path}/${page + 1}`}>
            <PaginationNext disabled={page === totalPages} />
          </Link>
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};

export const SingleTable = ({
  page,
  data,
  path,
  name,
  columns,
  indices,
}: {
  page: number;
  data: {
    rows: Record<string, unknown>[];
    count: number;
    primaryKeys: string[];
  };
  path: string;
  name: string;
  columns: { name: string }[];
  indices: Array<{
    name: string;
    columns: string[];
    unique: boolean;
  }>;
}) => {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [editRow, setEditRow] = useState<Record<string, unknown>>();

  const { rows, count, primaryKeys } = data;

  const snakeToCamel = (str: string) => {
    return str.replace(/([_][a-z])/g, (group) =>
      group.toUpperCase()
        .replace("_", ""));
  };

  const totalPages = Math.ceil(Number(count) / 25);

  return (
    <X css="min-h-screen max-h-fit max-w-screen gap-4">
      <Y css="gap-2 grow p-4 min-w-56 bg-neutral-900 rounded-lg">
        <X css="gap-12 justify-between items-center">
          <Text css="text-lg text-neutral-100">
            Table &gt; {name}
          </Text>
          <X css="gap-4">
            <Input
              css="p-4 border-2 border-neutral-600 bg-neutral-800 text-neutral-300"
              placeholder="Search for a table"
            />
            <Button
              css="bg-neutral-800 hover:bg-neutral-600 text-neutral-300"
              onClick={() => {
                console.log("Filter");
              }}
            >
              Filter
            </Button>
            <Link to={`${path}/edit`}>
              <Button css="bg-neutral-800 hover:bg-neutral-600 text-neutral-300">
                Edit Table
              </Button>
            </Link>
          </X>
        </X>

        <Y css="gap-8 p-4 justify-center">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead css="text-neutral-200">{column.name}</TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody css="text-neutral-300">
              {rows.map((row, index) => (
                <TableRow key={index}>
                  {columns.map((column) => {
                    const columnName = snakeToCamel(column.name);

                    return (
                      <TableCell
                        key={column.name}
                        css="max-w-lg overflow-hidden"
                      >
                        <Text css="line-clamp-2 text-elipsis">
                          {String(row[columnName])}
                        </Text>
                      </TableCell>
                    );
                  })}
                  <Button
                    variant="ghost"
                    css="mt-2"
                    onClick={() => {
                      setEditRow(row);
                      setDrawerOpen(true);
                    }}
                  >
                    ✎
                  </Button>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <X>
            <Paginations
              css="justify-end"
              n={page}
              totalPages={totalPages}
              path={path}
            />
          </X>
          <Y css="border border-neutral-800 rounded-lg">
            <IndicesTable indices={indices} />
          </Y>
        </Y>
      </Y>

      {editRow && drawerOpen && (
        <RowDrawer
          open={drawerOpen}
          setOpen={setDrawerOpen}
          tableName={name}
          row={editRow}
          primaryKeys={primaryKeys}
        />
      )}
    </X>
  );
};
