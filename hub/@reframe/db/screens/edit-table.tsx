"use client";

import { But<PERSON>, Text, X, Y } from "@reframe/ui/main.tsx";
import { useState } from "npm:react@canary";
import { ColumnForm } from "./create-table.tsx";
import { addRow, createColumn, createIndex } from "../action.ts";

import { ColumnMetadata } from "npm:kysely";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@reframe/db/screens/table.tsx";

type ColumnType = {
  name: string;
  type: "text" | "integer" | "real" | "jsonb";
  nullable: boolean;
};

type IndexType = {
  name: string;
  columns: string[];
  unique: boolean;
};

const AddIndex = (
  {
    columns,
    tableName,
  }: {
    columns: ColumnMetadata[];
    tableName: string;
  },
) => {
  const [newIndex, setNewIndex] = useState<IndexType>({
    name: "",
    columns: [],
    unique: false,
  });
  const [loading, setLoading] = useState(false);

  const saveIndex = async () => {
    setLoading(true);
    await createIndex({
      tableName,
      index: newIndex,
    });
    setNewIndex({
      name: "",
      columns: [],
      unique: false,
    });
    setLoading(false);
  };

  return (
    <Y css="gap-4">
      <Text css="text-md">Create New Index</Text>
      <X css="gap-8">
        <Y css="gap-2">
          <Text css="text-md">Index Name</Text>
          <input
            type="text"
            value={newIndex.name}
            onChange={(e) => setNewIndex({ ...newIndex, name: e.target.value })}
          />
        </Y>
        <Y css="gap-2">
          <Text css="text-md">Columns</Text>
          <input
            type="text"
            value={newIndex.columns.join(",")}
            onChange={(e) =>
              setNewIndex({ ...newIndex, columns: e.target.value.split(",") })}
          />
        </Y>
        <Y css="gap-2">
          <Text css="text-md">Unique</Text>
          <select
            value={newIndex.unique ? "true" : "false"}
            onChange={(e) =>
              setNewIndex({ ...newIndex, unique: e.target.value === "true" })}
          >
            <option value="true">true</option>
            <option value="false">false</option>
          </select>
        </Y>
      </X>

      <X css="justify-end">
        <Button
          css="text-neutral-100  bg-neutral-800 hover:bg-neutral-600"
          disabled={!newIndex.name || !newIndex.columns.length || loading}
          onClick={() => saveIndex()}
        >
          Add
        </Button>
      </X>
    </Y>
  );
};

export const EditTable = (
  { name, columns, indices }: {
    name: string;
    columns: ColumnMetadata[];
    indices: IndexType[];
  },
) => {
  const [newColumns, setNewColumns] = useState<ColumnType[]>([
    { name: "", type: "text", nullable: false },
  ]);
  const [newRow, setNewRow] = useState<Record<string, unknown>>({});

  const [loading, setLoading] = useState(false);

  const addColumn = () => {
    setNewColumns([...newColumns, { name: "", type: "text", nullable: false }]);
  };

  const saveColumn = async () => {
    setLoading(true);
    for (const column of newColumns) {
      await createColumn({
        tableName: name,
        column: column,
      });
    }

    setNewColumns([]);
    setLoading(false);
  };

  const insertRow = async () => {
    await addRow(name, newRow);
  };

  const headerTitles = ["Column Name", "Data Type", "Nullable"];

  return (
    <Y css="gap-4 p-2 bg-neutral-900 rounded-lg">
      <X css="gap-4 items-center">
        <Text css="text-lg text-neutral-100">Edit Table &gt;</Text>
        <Text css="text-lg text-neutral-100">{name}</Text>
      </X>

      <Y css="gap-4 p-4 bg-neutral-900">
        <Text css="text-md text-neutral-100">Columns</Text>

        <Y css="gap-4 p-2">
          <Table>
            <TableHeader>
              <TableRow>
                {headerTitles.map((title) => {
                  return <TableHead css="text-neutral-100">{title}</TableHead>;
                })}
              </TableRow>
            </TableHeader>
            <TableBody>
              {columns.map((column) => (
                <TableRow css="text-neutral-300">
                  <TableCell>{column.name}</TableCell>
                  <TableCell>{column.dataType}</TableCell>
                  <TableCell>{column.isNullable ? "true" : "false"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Y>

        <Y css="gap-4">
          <X css="justify-between items-center">
            <Text css="text-md text-neutral-100">
              Create New Columns
            </Text>
            <Button
              css="bg-neutral-800 hover:bg-neutral-600"
              onClick={addColumn}
            >
              +
            </Button>
          </X>
          <ColumnForm
            columns={newColumns}
            setColumns={setNewColumns}
          />

          <X css="justify-end">
            <Button
              css="bg-neutral-800 hover:bg-neutral-600"
              disabled={loading || !newColumns.length}
              onClick={() => saveColumn()}
            >
              Add
            </Button>
          </X>
        </Y>
      </Y>
      <Y css="gap-8 p-4 bg-neutral-900">
        <X css="justify-between items-center">
          <Text css="text-md text-neutral-100">
            Add a Row
          </Text>
        </X>
        <Y css="gap-4">
          <X css="gap-4">
            {columns.map((column) => (
              <Y css="gap-2">
                <Text css="text-md text-neutral-100">{column.name}</Text>
                <input
                  type="text"
                  onChange={(e) =>
                    setNewRow({
                      ...newRow,
                      [column.name]: e.target.value,
                    })}
                />
              </Y>
            ))}
          </X>
        </Y>

        <X css="justify-end">
          <Button
            css="bg-neutral-800 hover:bg-neutral-600"
            onClick={() => insertRow()}
          >
            Add
          </Button>
        </X>
      </Y>

      <Y css="gap-8 p-4 bg-neutral-900">
        <Y css="gap-2">
          <X css="justify-between items-center">
            <Text css="text-md text-neutral-100">Indices</Text>

            <Button css="bg-neutral-800 hover:bg-neutral-600">
              +
            </Button>
          </X>
          <Y>
            {indices.map((index) => (
              <Text css="text-neutral-100">{index.name}</Text>
            ))}
          </Y>
        </Y>

        <Y css="text-neutral-300">
          <AddIndex
            css="text-neutral-300"
            columns={columns}
            tableName={name}
          />
        </Y>
      </Y>
    </Y>
  );
};
