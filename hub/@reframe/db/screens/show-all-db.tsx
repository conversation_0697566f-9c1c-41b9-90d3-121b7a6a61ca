"use client";

import { Button, For, Input, Text, X, Y } from "@reframe/ui/main.tsx";
import { Link } from "@reframe/ui/components/router.tsx";
import { useState } from "npm:react@canary";
import { Select } from "@reframe/ui/components/select.tsx";
import {
  createConnection,
  createDBFromFork,
  createEmptyDB,
  syncTurso,
} from "../action.ts";

type Database = {
  name: string;
  url?: string;
};

const ConnectModal = ({ setShowConnectModal }: {
  setShowConnectModal: (show: boolean) => void;
}) => {
  const [name, setName] = useState("");
  const [token, setToken] = useState("");
  const [host, setHost] = useState("");
  const [loading, setLoading] = useState(false);

  const connectDB = async () => {
    setLoading(true);
    const result = await createConnection({
      name,
      hostname: host,
    });

    setLoading(false);
  };

  return (
    <Y css="p-4">
      <Y css="gap-4 p-4">
        <Text css="text-lg text-neutral-200">
          Connect to a Database
        </Text>

        <Y css="gap-4">
          <Input
            placeholder="Database Name"
            css="p-4 border-2"
            onChange={(e) => setName(e.target.value)}
          />
          <Input
            placeholder="Auth Token"
            css="p-4 border-2"
            onChange={(e) => setToken(e.target.value)}
          />

          <Input
            placeholder="Host"
            css="p-4 border-2"
            onChange={(e) => setHost(e.target.value)}
          />
        </Y>

        <X css="gap-4 p-4 justify-end">
          <Button
            css="bg-neutral-800  hover:bg-neutral-300 hover:text-neutral-800"
            onClick={() => setShowConnectModal(false)}
          >
            Cancel
          </Button>
          <Button
            css="bg-neutral-200  hover:bg-neutral-800 hover:text-neutral-300 text-neutral-950"
            disabled={!name || !host || loading}
            onClick={() => connectDB()}
          >
            Connect
          </Button>
        </X>
      </Y>
    </Y>
  );
};

const CreateModal = ({ setShowCreateModal }: {
  setShowCreateModal: (show: boolean) => void;
}) => {
  const [selected, setSelected] = useState<
    "empty" | "schema" | "fork"
  >(
    "empty",
  );

  const [newDBName, setNewDBName] = useState("");
  const [loading, setLoading] = useState(false);
  const [seedDB, setSeedDB] = useState("");

  const createDB = async () => {
    setLoading(true);

    if (selected === "empty") {
      await createEmptyDB(
        {
          org: "ashfaque",

          newDBName: newDBName,
        },
      );
      setLoading(false);
    }

    if (selected === "schema") {
      console.log("Creating DB from schema");
    }

    if (selected === "fork") {
      await createDBFromFork({
        org: "ashfaque",
        newDBName: newDBName,
        seedDB: seedDB,
      });
      setLoading(false);
    }
  };

  return (
    <Y css="gap-4 p-4">
      <Y css="gap-4 min-w-96">
        <Text css="text-lg text-neutral-200">
          Create a Turso Database
        </Text>

        <Y>
          <Select
            placeholder="Select"
            onChange={(e) => {
              setSelected(e);
            }}
          >
            <Select.Option value="empty">From Scratch</Select.Option>
            <Select.Option value="schema">Copy Schema</Select.Option>
            <Select.Option value="fork">Fork Existing</Select.Option>
          </Select>
        </Y>

        {selected === "empty" && (
          <Y css="gap-4">
            <Input
              placeholder="Database Name"
              css="p-4 border-2"
              value={newDBName}
              onChange={(e) => setNewDBName(e.target.value)}
            />
          </Y>
        )}

        {selected === "fork" && (
          <Y css="gap-4">
            <Input
              placeholder="Database Name"
              css="p-4 border-2 text-neutral-300"
              value={newDBName}
              onChange={(e) => setNewDBName(e.target.value)}
            />

            <Input
              placeholder="Seed DB"
              css="p-4 border-2"
              value={seedDB}
              onChange={(e) => setSeedDB(e.target.value)}
            />
          </Y>
        )}

        <X css="gap-4 p-2 justify-end">
          <Button
            css="bg-neutral-800  hover:bg-neutral-300 hover:text-neutral-800"
            onClick={() => setShowCreateModal(false)}
          >
            Cancel
          </Button>
          <Button
            css="bg-neutral-200  hover:bg-neutral-800 hover:text-neutral-300 text-neutral-950"
            disabled={loading || !newDBName}
            onClick={() => createDB()}
          >
            Create
          </Button>
        </X>
      </Y>
    </Y>
  );
};

export const ShowAllDB = ({ databases }: {
  databases: Database[];
}) => {
  const [showConnectModal, setShowConnectModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const syncWithTurso = async () => {
    setLoading(true);

    await syncTurso({
      org: "ashfaque",
    });

    setLoading(false);
  };

  const filteredDatabases = databases.filter((db) =>
    db.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Y
      justify="center"
      align="center"
      css="gap-4 p-16 min-h-screen   bg-neutral-900"
    >
      <Y
        fit-x
        css="px-4 py-4 max-w-screen-lg border-2 border-neutral-800 rounded-lg"
      >
        <Y css="gap-4 px-8 py-2">
          <Text css="text-xl text-neutral-200 text-center">
            Open a Database
          </Text>
        </Y>

        <Y css="gap-4 px-8 py-2">
          <Input
            placeholder="Search for a database"
            css="p-4 border-2 border-neutral-600 bg-neutral-800 text-neutral-300"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </Y>

        <X css="gap-2 px-8 justify-end">
          <Button
            css="bg-neutral-900 border-2 border-neutral-600 rounded-lg hover:bg-neutral-300 hover:text-neutral-800"
            disabled={loading}
            onClick={() => syncWithTurso()}
          >
            Sync
          </Button>
          <Button
            css="bg-neutral-200 text-neutral-900 hover:bg-neutral-800 hover:text-neutral-300"
            onClick={() => {
              setShowCreateModal(false);
              setShowConnectModal(!showConnectModal);
            }}
          >
            Connect
          </Button>
          <Button
            css="bg-neutral-200 text-neutral-900 hover:bg-neutral-800 hover:text-neutral-300"
            onClick={() => {
              setShowConnectModal(false);
              setShowCreateModal(!showCreateModal);
            }}
          >
            Create
          </Button>
        </X>

        {showConnectModal && (
          <ConnectModal
            setShowConnectModal={setShowConnectModal}
          />
        )}

        {showCreateModal && (
          <CreateModal setShowCreateModal={setShowCreateModal} />
        )}

        <Y css="max-h-0 border-t-2 border-black border-dashed" />

        <Y css="gap-4 p-4">
          <Y css="gap-4 p-4 min-w-96">
            <Text css="text-lg text-neutral-200">
              All Databases
            </Text>

            <For
              each={filteredDatabases}
              render={(db) => (
                <X css="gap-2 p-4 min-w-36 items-center justify-between bg-neutral-800 border-1 border-neutral-600 rounded-lg">
                  <Text css="text-neutral-200 text-center">
                    {db.name}
                  </Text>

                  <Link to={`/db/${db.name}`}>
                    <Button css="bg-neutral-800 hover:bg-neutral-300 hover:text-neutral-800">
                      View
                    </Button>
                  </Link>
                </X>
              )}
            />
          </Y>
        </Y>
      </Y>
    </Y>
  );
};
