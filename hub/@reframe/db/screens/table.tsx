import { t, tw } from "@reframe/ui/tw.tsx";

const Table = tw("table", t`w-full caption-bottom text-sm`);

const TableHeader = tw("thead", t`[&_tr]:border-b`);

const TableBody = tw("tbody", t`[&_tr:last-child]:border-0`);

const TableFooter = tw(
  "tfoot",
  t`border-t bg-primary-200/50 font-medium [&>tr]:last:border-b-0`,
);

const TableRow = tw(
  "tr",
  t`border-b border-neutral-700 transition-colors hover:bg-neutral-600 text-neutral-300 data-[state=selected]:bg-primary-200`,
);

const TableHead = tw(
  "th",
  t`h-12 px-4 text-left align-middle font-medium text-primary-800 [&:has([role=checkbox])]:pr-0`,
);

const TableCell = tw(
  "td",
  t`p-4 align-middle [&:has([role=checkbox])]:pr-0`,
);

const TableCaption = tw("caption", t`mt-4 text-sm text-primary-800`);

export {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
};
