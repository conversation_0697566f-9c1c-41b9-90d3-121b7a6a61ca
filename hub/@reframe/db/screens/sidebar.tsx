"use client";

import { But<PERSON>, <PERSON>, Text, X, Y } from "@reframe/ui/main.tsx";

export const Sidebar = ({
  path,
  tables,
}: {
  path: string;
  tables: Array<{ name: string }>;
}) => {
  return (
    <Y css="bg-neutral-900 rounded-lg gap-4 p-4" max-x="20%" min-x="20%">
      <Text css=" pb-4 text-lg text-neutral-100">
        All Tables
      </Text>

      {tables.map((table) => {
        return (
          <>
            <Link to={`${path}/table/${table.name}`}>
              <X css="gap-2 p-2 border-2 hover:bg-neutral-600 border-neutral-400 rounded-lg min-w-[100px] max-w-[200px]">
                <Text css="truncate text-neutral-300">{table.name}</Text>
              </X>
            </Link>
          </>
        );
      })}

      <Y css="gap-4">
        <Link to={`${path}/create`}>
          <Button css="bg-neutral-800  hover:bg-neutral-600">
            Create Table
          </Button>
        </Link>
      </Y>
    </Y>
  );
};
