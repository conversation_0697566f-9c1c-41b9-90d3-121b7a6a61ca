import { Button, Input, Text, X, Y } from "@reframe/ui/main.tsx";
import { useState } from "npm:react@canary";
import { deleteRow, editRow } from "../action.ts";
import {
  Drawer,
  DrawerContent,
  Drawer<PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@reframe/ui/components/drawer.tsx";

export const RowDrawer = ({
  open,
  setOpen,

  tableName,
  row,
  primaryKeys,
}: {
  open: boolean;
  setOpen: (b: boolean) => void;

  tableName: string;
  row: Record<string, unknown>;
  primaryKeys: string[];
}) => {
  const [newValue, setNewValue] = useState(row);

  const updateRow = async ({
    row,
    primaryKeys,
    newValue,
  }: {
    row: Record<string, unknown>;
    primaryKeys: string[];
    newValue: Record<string, unknown>;
  }) => {
    const clause = Object.fromEntries(
      primaryKeys.map((key) => [key, row[key]]),
    );

    const newRow = await editRow(tableName, clause, newValue);
  };

  const removeRow = async ({
    tableName,
    row,
    primaryKeys,
  }: {
    tableName: string;
    row: Record<string, unknown>;
    primaryKeys: string[];
  }) => {
    if (primaryKeys.length === 0) {
      console.error("No primary keys found");
      return;
    }

    const clause = Object.fromEntries(
      primaryKeys.map((key) => [key, row[key]]),
    );

    await deleteRow(tableName, clause);
  };

  return (
    <Drawer
      open={open}
      onOpenChange={(open) => {
        setOpen(!open);
      }}
    >
      <DrawerContent
        css="bg-neutral-950"
        side="right"
      >
        <DrawerHeader>
          <X css="py-4 justify-between items-center">
            <DrawerTitle css="text-neutral-100">
              Edit Row
            </DrawerTitle>
            <Button
              css="bg-neutral-400 hover:bg-neutral-600"
              variant="outline"
              onClick={() => {
                setOpen(false);
              }}
            >
              x
            </Button>
          </X>
        </DrawerHeader>

        <Y css="gap-4 p-2 bg-neutral-950">
          {Object.entries(row).map(([key, value]) => (
            <Y key={key} css="gap-4">
              <Text css="text-neutral-300">{key}</Text>
              <input
                value={newValue[key]}
                onChange={(e) => {
                  setNewValue({
                    ...newValue,
                    [key]: e.target.value,
                  });
                }}
              />
            </Y>
          ))}
        </Y>
        <DrawerFooter>
          <X css="gap-4 py-4">
            <Button
              css="bg-neutral-400 hover:bg-neutral-600"
              onClick={() => {
                updateRow({ row, primaryKeys, newValue });
                setOpen(false);
              }}
            >
              Save
            </Button>
            <Button
              css="bg-neutral-800 hover:bg-neutral-600"
              variant="destructive"
              onClick={() => {
                removeRow({ tableName, row, primaryKeys });
                setOpen(false);
              }}
            >
              Delete
            </Button>
          </X>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
};
