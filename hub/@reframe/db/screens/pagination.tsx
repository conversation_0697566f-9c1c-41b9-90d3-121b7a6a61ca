import { Button, t, tw } from "@reframe/ui/main.tsx";

const Pagination = tw("nav", t`mx-auto flex w-full justify-center`).override(
  () => ({
    role: "navigation",
    "aria-label": "pagination",
  }),
);

const PaginationContent = tw("ul", t`flex flex-row items-center gap-1`);

const PaginationItem = tw("li", t``);

const PaginationLink = tw(Button, t``)
  .override(({ isActive }: { isActive?: boolean }) => ({
    variant: isActive ? "link" : "ghost",
  }));

const PaginationPrevious = tw(PaginationLink, t`gap-1 pl-2.5`)
  .override(() => ({
    "aria-label": "Go to previous page",
    size: "default",
    children: (
      <>
        <span>{"<"}</span>
        <span>Previous</span>
      </>
    ),
  }));

const PaginationNext = tw(PaginationLink, t`gap-1 pr-2.5`)
  .override(() => ({
    "aria-label": "Go to next page",
    size: "default",
    children: (
      <>
        <span>Next</span>
        <span>{">"}</span>
      </>
    ),
  }));

const PaginationEllipsis = tw(
  "span",
  t`flex h-9 w-9 items-center justify-center`,
)
  .override(() => ({
    "aria-hidden": true,
    children: (
      <>
        <span>{"..."}</span>
        <span className="sr-only">More pages</span>
      </>
    ),
  }));

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
};
