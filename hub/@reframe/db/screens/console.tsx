"use client";

import { Button, For, Input, Text, X, Y } from "@reframe/ui/main.tsx";
import { useState } from "npm:react@canary";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@reframe/ui/components/table.tsx";
import { rawQuery } from "../action.ts";

const shell = async (
  query: string,
) => {
  const response = await rawQuery(query);
  if (response.rows.length === 0) {
    return <Text>No results</Text>;
  }

  const columns = Object.keys(response.rows[0]!);

  return (
    <Y css="gap-4 p-2">
      <Table>
        <TableHeader>
          <Text>{query}</Text>
          <TableRow>
            <For
              each={columns}
              render={(column) => <TableHead>{column}</TableHead>}
            />
          </TableRow>
        </TableHeader>
        <TableBody>
          <For
            each={response.rows}
            render={(row) => (
              <TableRow>
                <For
                  each={columns}
                  render={(column) => <TableCell>{row[column]}</TableCell>}
                />
              </TableRow>
            )}
          />
        </TableBody>
      </Table>
    </Y>
  );
};

export const Console = () => {
  const [showConsole, setShowConsole] = useState(false);
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<React.ReactElement[]>([]);

  const onRun = async () => {
    const response = await shell(query);

    setResults((results) => [...results, response]);
  };

  return (
    <Y css=" min-h-screen w-full gap-4 p-8 grow bg-neutral-950">
      {!showConsole && (
        <Y css=" items-center gap-2">
          <Text css="text-lg text-neutral-100">Console</Text>

          <Button
            css=" text-neutral-100 bg-neutral-600 hover:bg-neutral-400"
            onClick={() => {
              setShowConsole(!showConsole);
            }}
          >
            Connect
          </Button>
        </Y>
      )}

      {showConsole && (
        <Y fit-y css=" gap-4">
          <Text css="text-lg text-neutral-100">Console</Text>
          <Y css="gap-4">
            {results}
          </Y>
          <X css="gap-2">
            <Text css="text-neutral-100">&gt;</Text>
            <Input
              css="p-4 w-full bg-neutral-300"
              placeholder="Enter your query"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
            />
          </X>
          <X css="gap-4">
            <Button
              css=" text-neutral-100 bg-neutral-600 hover:bg-neutral-400"
              onClick={() => {
                onRun();
              }}
            >
              Run
            </Button>
            <Button
              css=" text-neutral-100 bg-neutral-400 hover:bg-neutral-600"
              variant="outline"
              onClick={() => {
                setShowConsole(!showConsole);
              }}
            >
              Close
            </Button>
          </X>
        </Y>
      )}
    </Y>
  );
};
