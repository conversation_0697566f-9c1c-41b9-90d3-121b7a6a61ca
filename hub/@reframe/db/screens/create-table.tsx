"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Y } from "@reframe/ui/main.tsx";
import { useState } from "npm:react@canary";
import { createTable } from "../action.ts";

type ColumnType = {
  name: string;
  type: "text" | "integer" | "real" | "jsonb";
  nullable: boolean;
};

export const ColumnForm = (
  { columns, setColumns }: {
    columns: ColumnType[];
    setColumns: (columns: ColumnType[]) => void;
  },
) => {
  const updateColumn = (
    index: number,
    key: string,
    value: string | boolean,
  ) => {
    setColumns(columns.map((col, i) => {
      if (i === index) {
        return { ...col, [key]: value };
      }

      return col;
    }));
  };

  const removeColumn = (index: number) => {
    setColumns(columns.filter((_, i) => i !== index));
  };

  return (
    <Y css="gap-4">
      {columns.map((column, i) => (
        <X css="gap-4">
          <Y css="gap-2">
            <Text css="text-md text-neutral-100">Column Name</Text>
            <input
              type="text"
              value={column.name}
              onChange={(e) => updateColumn(i, "name", e.target.value)}
            />
          </Y>

          <Y css="gap-2 ">
            <Text css="text-md text-neutral-100">Type</Text>
            <select
              value={column.type}
              onChange={(e) => updateColumn(i, "type", e.target.value)}
            >
              <option value="text">text</option>
              <option value="integer">integer</option>
              <option value="real">real</option>
              <option value="jsonb">jsonb</option>
            </select>
          </Y>
          <Y css="gap-2">
            <Text css="text-md  text-neutral-100">Nullable</Text>
            <select
              value={column.nullable ? "true" : "false"}
              onChange={(e) =>
                updateColumn(i, "nullable", e.target.value === "true")}
            >
              <option value="true">true</option>
              <option value="false">false</option>
            </select>
          </Y>
          <Y css="gap-4 px-2 justify-end">
            <Button
              css="text-neutral-100  bg-neutral-800 hover:bg-neutral-600"
              onClick={() => removeColumn(i)}
            >
              x
            </Button>
          </Y>
        </X>
      ))}
    </Y>
  );
};

export const CreateTableForm = (
  { path }: {
    path: WithRouter["path"];
  },
) => {
  const [tableName, setTableName] = useState("");

  const newPath = path.replace(/\/create$/, `/${tableName}`);
  const [columns, setColumns] = useState<ColumnType[]>([
    { name: "", type: "text", nullable: true },
  ]);
  const [primaryKeys, setPrimaryKeys] = useState<string[]>([""]);

  const removePrimaryKey = (key: string) => {
    setPrimaryKeys(primaryKeys.filter((k) => key !== k));
  };

  const addColumn = () => {
    setColumns([...columns, { name: "", type: "text", nullable: false }]);
  };

  return (
    <Y css="gap-4 px-4 py-6 min-h-screen w-full bg-neutral-950">
      <Text css="text-lg text-neutral-100">Create Table</Text>

      <Y css="gap-4">
        <Text css="text-md text-neutral-200">Table Name</Text>
        <input
          type="text"
          value={tableName}
          onChange={(e) => setTableName(e.target.value)}
        />
      </Y>

      <Y css="gap-4 p-2 bg-neutral-950 ">
        <X css="justify-between items-center">
          <Text css="text-md text-neutral-100">Columns</Text>
          <Button
            css="bg-neutral-400 hover:bg-neutral-600"
            onClick={addColumn}
          >
            +
          </Button>
        </X>
        <Y css="gap-4 bg-neutral-950">
          <ColumnForm
            css="text-neutral-300"
            columns={columns}
            setColumns={setColumns}
          />
        </Y>
      </Y>

      <Y css="gap-4 p-2 bg-neutral-950">
        <X css="justify-between items-center">
          <Text css="text-md text-neutral-100">Primary Keys</Text>

          <Button
            css="bg-neutral-400 hover:bg-neutral-600"
            onClick={() => setPrimaryKeys([...primaryKeys, ""])}
          >
            +
          </Button>
        </X>
        <Y css="gap-4">
          {primaryKeys.map((key, index) => (
            <X css="gap-4 items-center">
              <select
                value={key}
                onChange={(e) => {
                  setPrimaryKeys((keys) =>
                    keys.map((k, i) => i === index ? e.target.value : k)
                  );
                }}
              >
                <option value="">Select Column</option>
                {columns.map((col) => (
                  <option value={col.name}>{col.name}</option>
                ))}
              </select>

              <Button
                css=" text-neutral-300 bg-neutral-600 hover:bg-neutral-400"
                variant="ghost"
                onClick={() => removePrimaryKey(key)}
              >
                x
              </Button>
            </X>
          ))}
        </Y>
      </Y>

      <Y>
        <Button
          css=" text-neutral-100 bg-neutral-600 hover:bg-neutral-400"
          disabled={!tableName ||
            columns.some((c) => c.name.length === 0) ||
            primaryKeys.filter((k) => k.length > 0).length === 0}
          onClick={async () => {
            await createTable(
              {
                name: tableName,
                primaryKeys: primaryKeys.filter((k) => k.length > 0),
                columns,
              },
            );
            window.location.href = newPath;
          }}
        >
          Create Table
        </Button>
      </Y>
    </Y>
  );
};
