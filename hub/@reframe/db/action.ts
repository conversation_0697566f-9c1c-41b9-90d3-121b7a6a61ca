"use server";

import Reframe from "@";
import { createClient as createTursoClient } from "npm:@tursodatabase/api";
import { createClient } from "../db/db/client.ts";

import { v4 as uuid } from "npm:uuid";

import { sql } from "../db/client.ts";

type column = {
  name: string;
  type: "text" | "integer" | "real" | "jsonb";
  nullable: boolean;
};

const userId = "1";

const token = Reframe.env.TURSO_TOKEN;

const groupToken = Reframe.env.TURSO_GROUP_TOKEN;

const db = createClient();

export const getDatabases = async () => {
  const result = await db
    .selectFrom("database")
    .selectAll()
    .execute();

  return result;
};

export const syncTurso = async (
  { org }: { org: string },
) => {
  const turso = createTursoClient({ org, token });

  const databases = await turso.databases.list();

  for (const database of databases) {
    const host = `https://${database.hostname}`;

    await db
      .insertInto("database")
      .values({
        id: database.id,
        authToken: token,
        hostname: host,
        name: database.name,
        userId,
        createdAt: new Date().toISOString(),
      })
      .onConflict((database) =>
        database
          .column("id")
          .doNothing()
      )
      .returningAll()
      .execute();
  }

  return databases;
};

export const createConnection = async (
  {
    name,
    hostname,
  }: { name: string; hostname: string },
) => {
  // TODO: get from cookie later

  const result = await db
    .insertInto("database")
    .values({
      id: uuid(),
      authToken: groupToken,
      hostname,
      name,
      userId,
      createdAt: new Date().toISOString(),
    })
    .returningAll()
    .execute();

  return result;
};

export const createEmptyDB = async (
  { org, newDBName }: { org: string; newDBName: string },
) => {
  const turso = createTursoClient({
    org,
    token,
  });

  const newDatabase = await turso.databases.create(
    `${newDBName}`,
    {
      group: "default",
    },
  );

  const { jwt } = await turso.databases.createToken(newDatabase.name, {
    expiration: "2w",
    authorization: "full-access",
  });

  const host = `https://${newDatabase.hostname}`;

  const result = await db
    .insertInto("database")
    .values({
      id: newDatabase.id,
      authToken: jwt,
      hostname: host,
      name: newDatabase.name,
      userId,
      createdAt: new Date().toISOString(),
    })
    .onConflict((database) =>
      database
        .column("id")
        .doNothing()
    )
    .returningAll()
    .execute();

  return result;
};

export const createDBFromFork = async (
  { org, newDBName, seedDB }: {
    org: string;

    newDBName: string;
    seedDB: string;
  },
) => {
  const turso = createTursoClient({
    org,
    token,
  });

  const currentTimestamp = new Date().toISOString();

  const newDatabase = await turso.databases.create(
    `${newDBName}`,
    {
      group: "default",
      seed: {
        type: "database",
        name: seedDB,
        timestamp: currentTimestamp,
      },
    },
  );

  const { jwt } = await turso.databases.createToken(newDatabase.name, {
    expiration: "2w",
    authorization: "full-access",
  });

  const host = `https://${newDatabase.hostname}`;

  const result = await db
    .insertInto("database")
    .values({
      id: newDatabase.id,
      authToken: jwt,
      hostname: host,
      name: newDatabase.name,
      userId,
      parentId: seedDB,
      createdAt: new Date().toISOString(),
    })
    .onConflict((database) =>
      database
        .column("id")
        .doNothing()
    )
    .returningAll()
    .execute();

  return result;
};

export const getTables = async () => await db.introspection.getTables();

export const getTabledata = async (
  name: string,
  { limit = 25, offset = 0 }: {
    limit?: number;
    offset?: number;
  },
) => {
  const rowLength = await db
    .selectFrom(name)
    .select([
      sql<{}>`count(*)`.as("count"),
    ])
    .execute();

  const count = rowLength[0].count as number;

  const keys = await db
    .selectFrom(sql<{}>`pragma_table_info(${name})`.as("i"))
    .select([
      sql<string>`i.name`.as("name"),
      sql<number>`i.pk`.as("pk"),
    ])
    .execute();

  const primaryKeys = keys.filter((k) => k.pk === 1).map((k) => k.name);

  const result = await db
    .selectFrom(name)
    .selectAll()
    .limit(limit)
    .offset(offset)
    .execute();

  return {
    rows: result,
    count: count,
    primaryKeys: primaryKeys,
  };
};

export const getIndices = async (
  name: string,
) => {
  const result = await db
    .selectFrom(sql<{}>`pragma_index_list(${name})`.as("i"))
    .leftJoin(
      sql<{}>`pragma_index_info(i.name)`.as("c"),
      (join) => join.onTrue(),
    )
    .select([
      sql<string>`i.name`.as("index"),
      sql<number>`i.\`unique\``.as("unique"),
      sql<string>`c.name`.as("column"),
      sql<number>`c.seqno`.as("order"),
    ])
    .execute();

  const indices: Array<{
    name: string;
    columns: string[];
    unique: boolean;
  }> = [];

  for (const row of result) {
    const index = indices.find((i) => i.name === row.index);
    if (!index) {
      indices.push({
        name: row.index,
        columns: [row.column],
        unique: row.unique === 1,
      });
    } else {
      index.columns.push(row.column);
    }
  }

  return indices;
};

export const rawQuery = async (query: string) =>
  await sql.raw(query).execute(db);

export const createTable = async ({
  name,
  columns,
  primaryKeys,
}: {
  name: string;
  primaryKeys: string[];
  columns: column[];
}) => {
  const tables = await db.introspection.getTables();

  if (tables.find((t) => t.name === name)) {
    throw new Error(`Table ${name} already exists`);
  }

  let q = db.schema.createTable(name);

  for (const column of columns) {
    q = q.addColumn(column.name, column.type, (col) => {
      let c = col;

      if (!column.nullable) {
        c = c.notNull();
      }

      return c;
    });
  }

  q = q.addPrimaryKeyConstraint(`${name}_pk`, primaryKeys as []);

  await q.execute();
};

export const singleTableData = async (
  name: string,
  { limit = 25, offset = 0 }: {
    limit?: number;
    offset?: number;
  },
) => {
  const data = await getTabledata(
    name,
    {
      offset: 0,
      limit: 25,
    },
  );

  const indices = await getIndices(
    name,
  );

  return { data, indices };
};

export const addRow = async (
  tableName: string,
  row: Record<string, unknown>,
) => {
  const insertRow = await db
    .insertInto(tableName)
    .values(row)
    .execute();

  console.log("insertRow", insertRow);

  return;
};

export const editRow = async (
  tableName: string,
  clause: Record<string, unknown>,
  newValue: Record<string, unknown>,
) => {
  const updateRow = await db
    .updateTable(tableName)
    .set(newValue)
    .where((q) =>
      q.and(
        clause,
      )
    )
    .execute();

  console.log("updateRow", updateRow);

  return;
};

export const deleteRow = async (
  tableName: string,
  clause: Record<string, unknown>,
) => {
  await db
    .deleteFrom(tableName)
    .where((q) =>
      q.and(
        clause,
      )
    )
    .execute();

  return;
};

export const createIndex = async ({
  tableName,
  index,
}: {
  tableName: string;
  index: {
    name: string;
    columns: string[];
    unique: boolean;
  };
}) => {
  const tables = await getTables();

  const table = tables.find((t) => t.name === tableName);

  if (!table) {
    throw new Error(`Table ${table} not found`);
  }

  const indices = await getIndices(tableName);

  if (indices.find((i) => i.name === index.name)) {
    throw new Error(`Index ${index.name} already exists`);
  }

  const { name, columns, unique } = index;

  let q = db.schema.createIndex(name);

  if (unique) {
    q = q.unique();
  }

  for (const column of columns) {
    q = q.on(table.name).column(column);
  }

  await q.execute();
};

export const createColumn = async ({
  tableName,
  column,
}: {
  tableName: string;
  column: {
    name: string;
    type: "text" | "integer" | "real" | "jsonb";
    nullable: boolean;
  };
}) => {
  const tables = await getTables();

  const table = tables.find((t) => t.name === tableName);

  if (!table) {
    throw new Error(`Table ${table} not found`);
  }

  if (table.columns.find((c) => c.name === column.name)) {
    throw new Error(`Column ${column.name} already exists`);
  }

  const { name, type, nullable } = column;

  let q = db.schema.alterTable(table.name).addColumn(name, type, (col) => {
    let c = col;

    if (!nullable) {
      c = c.notNull();
    }

    return c;
  });
  await q.execute();
};
