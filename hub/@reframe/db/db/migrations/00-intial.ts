import { Kysely } from "npm:kysely";

const createIndex = async (
  db: <PERSON>ysely<unknown>,
  table: string,
  indices: string[][],
) => {
  for (const index of indices) {
    await db.schema
      .createIndex(`idx_${table}_${index.join("_")}`)
      .on(table)
      .columns(index)
      .execute();
  }
};

export async function up(db: Kysely<unknown>): Promise<void> {
  /** user */

  await db.schema
    .createTable("user")
    .addColumn(
      "id",
      "uuid",
      (col) => col.primaryKey().notNull(),
    )
    .addColumn("created_at", "timestamp", (col) => col.notNull())
    .execute();

  await createIndex(db, "user", [
    ["id"],
    ["created_at"],
  ]);

  await db.schema
    .createTable("database")
    .addColumn(
      "id",
      "uuid",
      (col) => col.primaryKey().notNull(),
    )
    .addColumn("user_id", "uuid", (col) => col.notNull())
    .addColumn("name", "text", (col) => col.notNull())
    .addColumn("hostname", "text", (col) => col.notNull())
    .addColumn("auth_token", "text")
    .addColumn("parent_id", "uuid")
    .addColumn("created_at", "timestamp", (col) => col.notNull())
    .execute();

  await createIndex(db, "database", [
    ["id"],
    ["user_id"],
    ["name"],
    ["hostname"],
    ["parent_id"],
    ["created_at"],
  ]);

  await db.schema
    .createTable("turso")
    .addColumn("org", "text", (col) => col.notNull())
    .addColumn("token", "text", (col) => col.notNull())
    .addColumn("created_at", "timestamp", (col) => col.notNull())
    .execute();

  await createIndex(db, "turso", [
    ["org"],
    ["created_at"],
  ]);
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.dropTable("database").execute();
  await db.schema.dropTable("turso").execute();
}
