import { Channel, Message, parseMessages, WithId } from "./core.ts";

export const createChannel = <M extends Message<unknown>>(
  name: string[],
): Channel<M> => {
  const bc = new BroadcastChannel(name.join("/"));

  const channel: Channel<M> = {
    name,
    publish: (message: M | M[]) => {
      console.log("[publish]", name, message);
      const messages = Array.isArray(message) ? message : [message];
      bc.postMessage(JSON.stringify(messages));
    },
    subscribe: (handler) => {
      const listener = (event: MessageEvent) => {
        console.log("[broadcast]", name, event.data);
        const messages = parseMessages<M>(event.data);

        for (const message of messages) {
          handler(message);
        }
      };

      console.log("[subscribe]", name);
      bc.addEventListener("message", listener);
      return () => {
        console.log("[unsubscribe]", name);
        bc.removeEventListener("message", listener);
      };
    },
    stream: () => {
      let cleanup: (() => void) | undefined;

      return new ReadableStream<WithId<M>>({
        start(controller) {
          cleanup = channel.subscribe((message) => controller.enqueue(message));
        },
        cancel() {
          if (cleanup) {
            cleanup();
          }
        },
      });
    },
  };

  return channel;
};
