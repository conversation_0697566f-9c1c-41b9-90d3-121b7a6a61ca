"use client";

import { Button, For, Input, Show, Text, X, Y } from "@reframe/ui/main.tsx";
import { PubSub } from "./client.tsx";
import { useState } from "npm:react@canary";
import { parseJson } from "./core.ts";

export const Playground = () => {
  const [messages, setMessages] = useState<
    ["in" | "out" | "debug" | "error", unknown][]
  >(
    [],
  );
  const [type, setType] = useState<string>("");
  const [message, setMessage] = useState<string>("");

  return (
    <PubSub
      path="/pubsub"
      channel={["chat", "test"]}
      auth="1234"
      onConnect={(socket) => {
        setMessages((prev) => [...prev, ["debug", "connected"]]);
      }}
      onMessage={(message, socket) => {
        setMessages((prev) => [...prev, ["in", message]]);
      }}
      onError={(error, socket) => {
        setMessages((prev) => [...prev, [
          "error",
          error instanceof Error ? error.message : error,
        ]]);
      }}
      onClose={(code, reason, socket) => {
        console.log("socket closed", code, reason);
        setMessages((prev) => [...prev, [
          "debug",
          "closed",
        ]]);
      }}
      use={(socket) => (
        socket.status === "open"
          ? (
            <Y gap={4} css="p-8">
              <X gap={2}>
                <Input
                  placeholder="type"
                  css="w-40"
                  onChange={(e) => setType(e.target.value)}
                />
                <Input
                  placeholder="message"
                  onChange={(e) => setMessage(e.target.value)}
                />
                <Button
                  onClick={() => {
                    try {
                      const data = parseJson(message);
                      const m = socket.send(type, data);
                      setMessages((prev) => [...prev, ["out", m]]);
                    } catch (error) {
                      setMessages((prev) => [...prev, [
                        "error",
                        error instanceof Error ? error.message : error,
                      ]]);
                    }
                  }}
                />
              </X>
              <Y gap={2} css="flex-col-reverse">
                <For
                  each={messages}
                  render={([type, message]) => (
                    <Text
                      css={[
                        "font-mono whitespace-pre-wrap p-2 bg-slate-400",
                        type === "in"
                          ? "bg-blue-300"
                          : type === "out"
                          ? "bg-green-300"
                          : type === "error"
                          ? "bg-red-300"
                          : type === "debug"
                          ? "bg-gray-300"
                          : "transparent",
                      ]}
                    >
                      {JSON.stringify(message, null, 2)}
                    </Text>
                  )}
                />
              </Y>
            </Y>
          )
          : <Text css="p-8">status: {socket.status}</Text>
      )}
    />
  );
};
