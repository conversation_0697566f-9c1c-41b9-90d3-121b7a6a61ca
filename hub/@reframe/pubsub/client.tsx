"use client";

import { useEffect, useRef, useState } from "npm:react@canary";
import { createSocket, Handlers, Message, Socket } from "./core.ts";

export const PubSub = <M extends Message<unknown>>(
  props: {
    path: `/${string}`;
    auth?: string;
    channel: string | string[];
    onConnect: (socket: Socket<M, {}>) => void;
    use: (
      socket:
        | (Pick<Socket<M, {}>, "send" | "sendMany"> & { status: "open" })
        | {
          status: "pending" | "closed";
        },
    ) => React.ReactNode;
  } & Omit<Handlers<M, {}>, "onConnect">,
) => {
  const [socket, setSocket] = useState<Socket<M, {}> | null>(null);
  const [status, setStatus] = useState<"pending" | "open" | "closed">(
    "pending",
  );

  const handlersRef = useRef({
    onConnect: props.onConnect,
    onMessage: props.onMessage,
    onError: props.onError,
    onClose: props.onClose,
  });

  handlersRef.current.onConnect = props.onConnect;
  handlersRef.current.onMessage = props.onMessage;
  handlersRef.current.onError = props.onError;
  handlersRef.current.onClose = props.onClose;

  const channel = Array.isArray(props.channel)
    ? props.channel.join("/")
    : props.channel;

  useEffect(() => {
    const url = new URL(
      `${self.__reframe.origin + props.path}/${channel}${
        props.auth ? `?x-authorization=${props.auth}` : ""
      }`,
      self.location.href,
    );
    url.protocol = "wss:";
    const ws = new WebSocket(url);

    const socket = createSocket({
      ws,
      context: () => {},
      handlers: {
        ...handlersRef.current,
        onConnect: (socket) => {
          setStatus("open");
          return handlersRef.current.onConnect(socket);
        },
        onClose: (code, reason, socket) => {
          setStatus("closed");
          return handlersRef.current.onClose(code, reason, socket);
        },
      },
    });

    setSocket(socket);

    return () => {
      setSocket(null);
      ws.close();
    };
  }, [
    channel,
    props.path,
    props.auth,
  ]);

  if (!socket) {
    return props.use({ status: "pending" });
  }

  return props.use({
    status,
    send: socket.send,
    sendMany: socket.sendMany,
  });
};
