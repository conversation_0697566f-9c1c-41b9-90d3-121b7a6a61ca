import { v4 as uuid } from "npm:uuid";

export type Message<T> = {
  type: string;
  data: T;
};

export type WithId<M extends Message<unknown>> = M & { id: string };

export type Context<Ctx> = (ctx?: Ctx | ((prev: Ctx) => Ctx)) => Ctx;

export type Channel<
  M extends Message<unknown>,
> = {
  name: string[];
  publish: (
    message: WithId<M> | WithId<M>[],
  ) => void;
  subscribe: (
    handler: (message: WithId<M>) => void,
  ) => () => void;
  stream: () => ReadableStream<WithId<M>>;
};

export type Socket<
  M extends Message<unknown>,
  Ctx extends Record<string, unknown>,
> = {
  channel?: Channel<M>;
  context: Context<Ctx>;
  send: <T extends M["type"]>(
    type: T,
    data: Extract<M, { type: T }>["data"],
  ) => {
    message: WithId<M>;
    broadcast: () => void;
  };
  sendMany: (messages: M[]) => {
    messages: WithId<M>[];
    broadcast: () => void;
  };
  broadcast: (message: WithId<M> | WithId<M>[]) => void;
  error: (error: unknown) => void;
  close: (code: number, reason: string) => void;
};

export type Handlers<
  M extends Message<unknown>,
  Ctx extends Record<string, unknown>,
> = {
  onConnect: (
    socket: Socket<M, Ctx>,
  ) => void | Promise<void>;
  onMessage: (
    message: M & { id: string },
    socket: Socket<M, Ctx>,
  ) => void | Promise<void>;
  onError: (error: unknown, socket: Socket<M, Ctx>) => void | Promise<void>;
  onClose: (
    code: number,
    reason: string,
    socket: Socket<M, Ctx>,
  ) => void | Promise<void>;
};

export const parseJson = (json: string): unknown => {
  try {
    return JSON.parse(json);
  } catch (_error) {
    throw new Error("message is not a valid json");
  }
};

const validateMessage = <M extends Message<unknown>>(
  data: unknown,
): M & { id: string } => {
  if (typeof data !== "object" || data === null) {
    throw new Error("message is not an object");
  }

  if (!("id" in data) || typeof data.id !== "string") {
    throw new Error("message.id is not a string");
  }

  if (!("type" in data) || typeof data.type !== "string") {
    throw new Error("message.type is not a string");
  }

  return data as M & { id: string };
};

export const parseMessage = <M extends Message<unknown>>(data: string) =>
  validateMessage<M>(parseJson(data));

export const parseMessages = <M extends Message<unknown>>(
  data: string,
) => {
  const messages = parseJson(data);

  if (!Array.isArray(messages)) {
    return [validateMessage<M>(messages)];
  }

  return messages.map((message) => validateMessage<M>(message));
};

export const createSocket = <
  M extends Message<unknown>,
  Ctx extends Record<string, unknown>,
>(
  { ws, context, handlers, channel }: {
    context: Ctx;
    handlers: Handlers<M, Ctx>;
    channel?: Channel<M>;
    ws: WebSocket;
  },
): Socket<M, Ctx> => {
  const ctx = {
    current: context,
    unsubscribe: null as (() => void) | null,
  };

  const socket: Socket<M, Ctx> = {
    channel,
    context: (next) => {
      if (next) {
        ctx.current = typeof next === "function" ? next(ctx.current) : next;
      }

      return ctx.current;
    },
    error: (error: unknown) => {
      console.error("socket error", error);
      if (ws.readyState !== WebSocket.OPEN) {
        throw new Error("socket is not open");
      }

      ws.send(
        JSON.stringify({
          id: uuid(),
          type: ".error",
          data: error instanceof Error ? error.stack : String(error),
        }),
      );
    },
    broadcast: (message) => {
      if (!channel) {
        throw new Error("channel is not available");
      }

      channel.publish(message);
    },
    send: (type, data) => {
      if (ws.readyState !== WebSocket.OPEN) {
        throw new Error("socket is not open");
      }

      const message = { id: uuid(), type, data } as unknown as WithId<M>;
      ws.send(JSON.stringify(message));

      return {
        message,
        broadcast: () => socket.broadcast(message),
      };
    },
    sendMany: (messages) => {
      if (ws.readyState !== WebSocket.OPEN) {
        throw new Error("socket is not open");
      }

      const messagesWithId = messages.map((message) => ({
        id: uuid(),
        ...message,
      })) as WithId<M>[];

      for (const message of messagesWithId) {
        ws.send(JSON.stringify(message));
      }

      return {
        messages: messagesWithId,
        broadcast: () => socket.broadcast(messagesWithId),
      };
    },
    close: (code, reason) => {
      if (ws.readyState === WebSocket.CLOSED) {
        return;
      }
      ws.close(code, reason);
    },
  };

  ws.addEventListener("open", async () => {
    try {
      await handlers.onConnect(socket);

      if (channel) {
        ctx.unsubscribe?.();
        ctx.unsubscribe = channel.subscribe((message) => {
          socket.sendMany([message]);
        });
      }
    } catch (error) {
      socket.error(error);
      socket.close(
        1011,
        error,
      );
    }
  });

  ws.addEventListener("message", async (event) => {
    try {
      const message = parseMessage<M>(event.data);

      await handlers.onMessage(message, socket);
    } catch (error) {
      socket.error(error);
    }
  });

  ws.addEventListener(
    "error",
    async (event) => {
      try {
        await handlers.onError(event, socket);
      } catch (error) {
        console.error(error);
        socket.close(1011, error);
      }
    },
  );

  ws.addEventListener(
    "close",
    async (event) => {
      try {
        await handlers.onClose(event.code, event.reason, socket);
      } catch (error) {
        console.error(error);
      } finally {
        ctx.unsubscribe?.();
      }
    },
  );

  return socket;
};
