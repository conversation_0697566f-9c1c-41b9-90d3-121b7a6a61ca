import { MaybePromise, Thenable } from "@reframe/zero/defs.ts";
import { Channel, createSocket, Handlers, Message } from "./core.ts";
import { createChannel } from "./channel.ts";

export type Client<
  M extends Message<unknown>,
  Ctx extends Record<string, unknown>,
> = {
  basePath: `/${string}`;

  handlers: Handlers<M, Ctx>;

  onConnect: (
    handler: Handlers<M, Ctx>["onConnect"],
  ) => Client<M, Ctx>;
  onMessage: (
    handler: Handlers<M, Ctx>["onMessage"],
  ) => Client<M, Ctx>;
  onError: (handler: Handlers<M, Ctx>["onError"]) => Client<M, Ctx>;
  onClose: (handler: Handlers<M, Ctx>["onClose"]) => Client<M, Ctx>;

  middleware: (
    request: Request,
    next: (request: Request) => Thenable<Response>,
  ) => MaybePromise<Response>;

  channel: (channel: string[]) => Channel<M>;
};

const createClient = <
  M extends Message<unknown>,
  Ctx extends Record<string, unknown>,
>(
  basePath: `/${string}`,
  createContext: (channel: string[]) => Ctx | Promise<Ctx>,
  handlers: Handlers<M, Ctx>,
) => {
  const client: Client<M, Ctx> = {
    basePath,
    handlers,
    onConnect: (handler) =>
      createClient(basePath, createContext, {
        ...handlers,
        onConnect: handler,
      }),
    onMessage: (handler) =>
      createClient(basePath, createContext, {
        ...handlers,
        onMessage: handler,
      }),
    onError: (handler) =>
      createClient(basePath, createContext, { ...handlers, onError: handler }),
    onClose: (handler) =>
      createClient(basePath, createContext, { ...handlers, onClose: handler }),

    channel: (channel: string[]) => createChannel(channel),

    middleware: async (
      request: Request,
      next: (request: Request) => Thenable<Response>,
    ) => {
      const url = new URL(request.url);
      if (!url.pathname.startsWith(basePath)) {
        return next(request);
      }

      const channel = url.pathname.slice(basePath.length)
        .split("/").filter(
          Boolean,
        );

      if (request.headers.get("upgrade") !== "websocket") {
        return next(request);
      }

      const { socket: ws, response } = Deno.upgradeWebSocket(
        request,
      );

      try {
        const context = await createContext(channel);

        createSocket<M, Ctx>({
          ws,
          context,
          handlers,
          channel: createChannel(channel),
        });
      } catch (error) {
        console.error(error);
        ws.close(1000);
      }

      return response;
    },
  };

  return client;
};

export const createPubSub = <M extends Message<unknown>>(
  // todo: get basePath automatically from middleware
  basePath: `/${string}`,
) => ({
  withContext: <Ctx extends Record<string, unknown>>(
    createContext: (channel: string[]) => Ctx | Promise<Ctx>,
  ) => {
    return createClient<M, Ctx>(basePath, createContext, {
      onConnect: () => {},
      onMessage: () => {},
      onError: () => {},
      onClose: () => {},
    });
  },
});
